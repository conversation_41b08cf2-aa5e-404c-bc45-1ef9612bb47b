# -*- coding: utf-8 -*-
import json
import time
from collections import defaultdict
from decimal import Decimal
from confluent_kafka import Consumer, TopicPartition, KafkaException, KafkaError
from flask import current_app
from app import config
from app.caches import PerpetualMarketCache
from app.common import PrecisionEnum
from app.models import db, User
from app.models.copy_trading import CopyTraderPositionChangeRecord, \
    CopyTradingRunUserStatus
from app.schedules.copy_trading import update_one_trader_statics, update_one_follower_statics, \
    generate_copy_follower_op_schedule
from app.utils import timestamp_to_datetime, quantize_amount


def get_max_user_id():
    return User.query.order_by(User.id.desc()).first().id


def get_max_offset():
    row = CopyTraderPositionChangeRecord.query.order_by(CopyTraderPositionChangeRecord.offset.desc()).first()
    return row.offset if row else -1


def get_copy_trader_user_id_map():
    rows = CopyTradingRunUserStatus.query.all()
    sub_trader_map = {}
    sub_follower_map = {}
    for r in rows:
        if r.type == CopyTradingRunUserStatus.Type.TRADER:
            sub_trader_map[r.user_id] = r.main_user_id
        elif r.type == CopyTradingRunUserStatus.Type.FOLLOWER:
            sub_follower_map[r.user_id] = r.main_user_id
    return sub_trader_map, sub_follower_map


def get_operation_type(amount_, amount_delta_, event_, is_first_=False):
    _type = None
    model = CopyTraderPositionChangeRecord
    if is_first_:
        return model.OperationType.ADD_POSITION
    if amount_ == 0 or event_ == 5:  # 5表示强平
        _type = model.OperationType.CLOSE_POSITION
    elif amount_delta_ > 0:  # 加仓
        _type = model.OperationType.ADD_POSITION
    elif amount_delta_ < 0:  # 减仓
        _type = model.OperationType.REDUCE_POSITION
    return _type


def get_avg_price(amount_delta_, settle_val_delta_, precision_):
    avg_price_ = abs(
        settle_val_delta_ / amount_delta_ if amount_delta_ else 0)
    return quantize_amount(avg_price_, precision_)


def get_position_info(msg):
    # 获取消息中的 position 信息
    value = json.loads(msg.value())
    position = value.get('position')
    if position:
        pos_id = position['position_id']
        amount = Decimal(position['amount'])
        settle_val = Decimal(position['settle_val'])
        event = value['event']
        market = value['market']
        return pos_id, amount, settle_val, event, market
    return None, None, None, None, None


def run_kafka_consumer():
    consumer_config = {
        'bootstrap.servers': config['KAFKA_PERPETUAL_CONFIG']['KAFKA_SERVERS'],
        'group.id': config['KAFKA_PERPETUAL_CONFIG']['KAFKA_GROUP_ID'],
        'enable.auto.commit': False,
        'session.timeout.ms': 10000,
        'heartbeat.interval.ms': 3000,
        'max.poll.interval.ms': 300000,
    }
    consumer = Consumer(consumer_config)
    consumer.subscribe(['perpetual_sys_positions'])
    max_user_id = get_max_user_id()
    copy_trader_user_id_map, copy_follower_user_id_map = get_copy_trader_user_id_map()
    batch_size = 5000
    msg_count = 0
    start_time = time.time()
    offsets = {}
    max_offset = get_max_offset()
    current_app.logger.info(f'max_offset is {max_offset}')
    money_prec_map = {k: v['money_prec'] for k, v in PerpetualMarketCache().read_aside().items()}

    try:
        while True:
            message_list = consumer.consume(num_messages=5000, timeout=1)
            messages = defaultdict(list)
            for _msg in message_list:
                if _msg.error():
                    current_app.logger.error(f'copy_trading kafka consume error: {_msg.error()}')
                    continue
                messages[_msg.topic()].append(_msg)
            # 每五秒提交一次
            current_time = time.time()
            if offsets and (current_time - start_time) >= 5:
                try:
                    consumer.commit(offsets=list(offsets.values()), asynchronous=False)
                except KafkaException as e:
                    if e.args[0].code() == KafkaError.ILLEGAL_GENERATION:
                        current_app.logger.warning("copy_trading kafka ILLEGAL_GENERATION error")
                        raise
                    else:
                        current_app.logger.error(f"copy_trading kafka commit error: {e}")
                        raise
                msg_count = 0
                start_time = current_time
            for tp, msgs in messages.items():
                trader_change_list = []  # 用于存储带单员的操作
                position_record_map = dict()
                for msg in msgs:
                    value = json.loads(msg.value())
                    user_id = value['user_id']
                    if msg.offset() <= max_offset:
                        continue
                    if user_id > max_user_id:
                        # 说明可能有新的交易员加入，刷新
                        # 保证新增的User，CopyTradingRunUserStatus同个事务
                        db.session.rollback()
                        current_app.logger.info(f'reload max max_user_id: {user_id}>{max_user_id}')
                        max_user_id = get_max_user_id()
                        copy_trader_user_id_map, copy_follower_user_id_map = get_copy_trader_user_id_map()
                    if user_id in copy_trader_user_id_map:
                        trader_change_list.append(msg)
                invalid_offsets = set()  # 用于存储偏移量
                for i in range(len(trader_change_list) - 1):
                    # 获取当前消息和下一个消息
                    cur_msg, next_msg = trader_change_list[i], trader_change_list[i + 1]
                    cur_pos_id, cur_amount, cur_settle_val, cur_event, cur_market = get_position_info(cur_msg)
                    next_pos_id, next_amount, next_settle_val, next_event, _ = get_position_info(
                        next_msg)
                    offset = cur_msg.offset()
                    if not cur_pos_id:  # 如果当前消息没有 position 信息，则跳过
                        invalid_offsets.add(offset)
                        continue
                    if next_pos_id and cur_pos_id == next_pos_id:  # 如果当前和下一个 position_id 相同，进行进一步处理
                        if cur_pos_id not in position_record_map:
                            last_record = CopyTraderPositionChangeRecord.query.filter(
                                CopyTraderPositionChangeRecord.position_id == cur_pos_id,
                                CopyTraderPositionChangeRecord.offset < offset,
                            ).order_by(CopyTraderPositionChangeRecord.id.desc()).first()
                            if last_record:
                                position_record_map[cur_pos_id] = dict(
                                    amount=last_record.amount,
                                    settle_val=last_record.settle_val,
                                    is_first=False,
                                )
                            else:  # 仓位的第一条数据
                                position_record_map[cur_pos_id] = dict(
                                    amount=0,
                                    settle_val=0,
                                    is_first=True,
                                )

                        last_position_record = position_record_map[cur_pos_id]
                        money_prec = money_prec_map.get(cur_market, PrecisionEnum.PRICE_PLACES)

                        next_avg_price = get_avg_price(
                            next_amount-cur_amount,
                            next_settle_val-cur_settle_val,
                            money_prec)
                        next_operation_type = get_operation_type(cur_amount, next_amount-cur_amount, cur_event)

                        cur_avg_price = get_avg_price(
                            cur_amount-last_position_record['amount'],
                            cur_settle_val-last_position_record['settle_val'],
                            money_prec)
                        cur_operation_type = get_operation_type(last_position_record['amount'],
                                                                 cur_amount-last_position_record['amount'],
                                                                 cur_event,
                                                                 last_position_record['is_first']
                                                                 )

                        position_record_map[cur_pos_id] = dict(
                            amount=cur_amount,
                            settle_val=cur_settle_val,
                            is_first=False,
                        )
                        if cur_avg_price == next_avg_price and cur_operation_type == next_operation_type:
                            current_app.logger.info(f'copy_trading msg aggregation'
                                                       f' offset:{cur_msg.offset()}:{next_msg.offset()},'
                                                       f' price:{cur_avg_price}:{next_avg_price}'
                                                       f' operation_type:{cur_operation_type}:{next_operation_type}')
                            invalid_offsets.add(offset)

                for msg in msgs:
                    topic = msg.topic()
                    partition = msg.partition()
                    value = json.loads(msg.value())
                    user_id = value['user_id']

                    offset = msg.offset()
                    msg_sent_at = timestamp_to_datetime(int(msg.timestamp()[1] / 1000))
                    msg_count += 1
                    # 记录每个分区的最新 offset
                    offsets[topic] = TopicPartition(topic, partition, offset + 1)
                    current_time = time.time()

                    if offset in invalid_offsets:
                        continue
                    if offset <= max_offset:
                        continue
                    if user_id in copy_trader_user_id_map:
                        if not value.get('position'):
                            continue
                        f_sub_id, f_u_id = user_id, copy_trader_user_id_map[user_id]
                        market = value['market']
                        asset = value['asset']
                        event = value['event']
                        position = value['position']
                        position_created_at = timestamp_to_datetime(int(position['create_time']))
                        position_updated_at = timestamp_to_datetime(int(position['update_time']))
                        position_id = value['position']['position_id']
                        side = value['position']['side']
                        copy_op_row = CopyTraderPositionChangeRecord(
                            user_id=f_u_id,
                            sub_user_id=f_sub_id,
                            position_detail=msg.value(),
                            market=market,
                            event=event,
                            asset=asset,
                            side=side,
                            position_id=position_id,
                            offset=offset,
                            msg_sent_at=msg_sent_at,
                            position_created_at=position_created_at,
                            position_updated_at=position_updated_at,
                            amount=position['amount'],
                            settle_val=position['settle_val'],
                        )
                        db.session.add(copy_op_row)
                        db.session.commit()
                        max_offset = offset
                        generate_copy_follower_op_schedule.delay()
                        if user_id in copy_trader_user_id_map or user_id in copy_follower_user_id_map:
                            if not value.get('position'):
                                continue
                            position = value['position']
                            if Decimal(position['amount']) == Decimal(0) or value['event'] == 5:
                                # 仓位平了，更新下带单人｜跟单人的统计数据
                                if user_id in copy_trader_user_id_map:
                                    update_one_trader_statics.apply_async((user_id,), countdown=5, expires=120)
                                elif user_id in copy_follower_user_id_map:
                                    update_one_follower_statics.apply_async((user_id,), countdown=5, expires=120)

                if msg_count >= batch_size:
                    # 提交记录的 offset
                    try:
                        consumer.commit(offsets=list(offsets.values()), asynchronous=False)
                    except KafkaException as e:
                        if e.args[0].code() == KafkaError.ILLEGAL_GENERATION:
                            current_app.logger.warning("copy_trading kafka ILLEGAL_GENERATION error")
                            raise
                        else:
                            current_app.logger.error(f"copy_trading kafka commit error: {e}")
                            raise
                    msg_count = 0
                    start_time = current_time
            # poll间隔
            time.sleep(0.2)  # 尽量聚合多的同一笔订单的消息，暂时用0.2秒观察

    finally:
        consumer.close()


if __name__ == "__main__":
    run_kafka_consumer()

__all__ = ['run_kafka_consumer']