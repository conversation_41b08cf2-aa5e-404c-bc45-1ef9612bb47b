# -*- coding: utf-8 -*-
import os
import socket

from logging import get<PERSON><PERSON><PERSON>, <PERSON><PERSON>, getLogRecordFactory, setLogRecordFactory
from logging.config import dictConfig
from traceback import format_exc
from typing import Optional

import flask_babel
import pyroscope
from celery import Celery
from flask import Flask, has_app_context
from flask import g, request
from flask_babel import Babel, Domain, force_locale, get_locale
from flask_migrate import Migrate
# noinspection PyProtectedMember

from .config import config
from .common import Language
from .utils import auto_close_db_session, patch_celery_event_on_success

app: Optional[Flask] = None
_logger: Optional[Logger] = None
migrate: Optional[Migrate] = None
celery: Celery = Celery(
    __name__,
    broker=config['CELERY_BROKER_URL']
)
babel: Optional[Babel] = None

DEFAULT_LABELS = {
    "project": "Coinex",
    "service": "CoinexCom",
    "module": "CoinexComWeb",
}


def create_app():
    global app
    app = Flask(__name__, static_folder=None)

    _init_logging()
    _init_config(app)
    _init_babel(app)
    _init_db(app)
    _init_celery(app)
    _init_assets(app)
    _init_business(app)
    _init_apis(app)
    _init_jinja(app)
    _init_monitor(app)
    _init_resp_size_inspector(app)
    _logger.info('server started successfully')

    return app


def _init_logging():
    # noinspection SpellCheckingInspection
    dictConfig({
        'version': 1,
        'formatters': {
            'simple': {
                'format': '[%(asctime)s] %(levelname)s %(name)s: %(message)s',
            },
            'colored': {
                '()': 'colorlog.ColoredFormatter',
                'format': '[%(asctime)s] '
                          '%(log_color)s%(levelname)s%(reset)s '
                          '%(name)s: %(message)s'
            }
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'formatter': 'colored',
                'level': 'DEBUG',
                'stream': 'ext://sys.stdout'
            },
            'graylog': {
                'class': 'app.utils.GELFUDPHandler',
                'host': config['GRAYLOG_LOGGER_HOST'],
                'port': config['GRAYLOG_LOGGER_PORT'],
                'formatter': 'simple',
                'level': 'WARNING'
            }
        },
        'loggers': {
            'app': {
                'level': 'INFO',
                'handlers': ['console', 'graylog'],
                'propagate': False
            }
        },
        'disable_existing_loggers': False
    })
    # make sure flask `app.name == 'app'`
    # so we can use `current_app.logger` to get `_logger`
    global _logger
    _logger = getLogger('app')

    # add default labels for graylog
    old_factory = getLogRecordFactory()

    def record_factory(*args, **kwargs):
        record = old_factory(*args, **kwargs)
        for k, v in DEFAULT_LABELS.items():
            setattr(record, k, v)
        return record

    setLogRecordFactory(record_factory)


def _init_config(flask_app: Flask):
    _logger.info('initiating configurations...')

    flask_app.config.from_mapping(config)


class CustomDomain(Domain):

    # replace origin gettext, to return default locale translation if not
    # translated
    def gettext(self, string, **variables):
        if string == "":
            # 空字符串的gettext返回的是一些meta信息
            return ""
        msg = super(CustomDomain, self).gettext(string, **variables)
        # return when as raw language or no translations
        if str(get_locale()) == Language.ZH_HANS_CN.value or msg != string:
            return msg

        if str(get_locale()) in [Language.ZH_HANT_HK.value, Language.JA_JP.value] and msg == string:
            # 这里是为了处理一些日语、繁体翻译后等同于原文，比如：加拿大->加拿大
            return msg

        with force_locale(Language.DEFAULT.value):
            return super(CustomDomain, self).gettext(string, **variables)


def _init_babel(flask_app: Flask):
    _logger.info('initiating babel...')

    global babel
    babel = Babel(flask_app)

    def get_locale():
        return g.get('lang', Language.DEFAULT.value)

    babel.init_app(app, locale_selector=get_locale)

    # inject CustomDomain with mock
    # from unittest.mock import patch
    # patch("flask_babel.Domain", new=CustomDomain).start()
    # force inject Domain
    flask_babel.Domain = CustomDomain


def _init_db(flask_app: Flask):
    _logger.info('initiating database...')

    from .models import db
    db.init_app(flask_app)

    global migrate
    migrate = Migrate(flask_app, db)


def _init_celery(flask_app: Flask):
    _logger.info('initiating celery...')

    global celery
    celery.config_from_object('app.config.celery')

    patch_celery_event_on_success()

    class ContextTask(celery.Task):
        abstract = True

        def on_failure(self, exc, task_id, args, kwargs, e_info):
            _ = task_id, e_info
            task_info = f'task_name => {self.name}, ' \
                        f'args => {args}, ' \
                        f'kwargs => {kwargs}'

            with flask_app.app_context():
                from .business import Locked
                logger = flask_app.logger
                if isinstance(exc, Locked):
                    logger.warning(f'{task_info} | Locked: {exc.data}', extra={'task': self.name})
                else:
                    logger.error('\n'.join([task_info, format_exc()]), extra={'task': self.name})

        def __call__(self, *args, **kwargs):
            if has_app_context():
                return super().__call__(*args, **kwargs)
            with flask_app.app_context():  # this means that the task is called asynchronously, not directly.
                _logger.info("[Process %s] Task %s started", os.getpid(), self.name)
                return auto_close_db_session(super().__call__)(*args, **kwargs)

    # noinspection PyPropertyAccess
    celery.Task = ContextTask

    from . import schedules


def _init_assets(flask_app: Flask):
    _logger.info('initiating assets...')

    _ = flask_app
    from . import assets


def _init_business(flask_app: Flask):
    _logger.info('initiating business layer...')

    from .business import init_app
    init_app(flask_app)


def _init_apis(flask_app: Flask):
    _logger.info('initiating APIs...')

    from app.api import init_app
    init_app(flask_app)


def _init_jinja(flask_app: Flask):
    _logger.info('initiating Jinja...')

    from flask_babel import gettext

    def get_year():
        import time
        return time.strftime('%Y')

    def get_date():
        from .utils import now
        return now().strftime('%Y / %m / %d')
    
    def get_current_timestamp():
        from .utils import current_timestamp
        return current_timestamp(to_int=True)

    @flask_app.template_filter('render_token')
    def render_token(value, context):
        from jinja2 import Template
        return Template(value).render(context)

    flask_app.jinja_env.autoescape = True
    flask_app.jinja_env.globals['get_year'] = get_year
    flask_app.jinja_env.globals['get_date'] = get_date
    flask_app.jinja_env.globals['get_current_timestamp'] = get_current_timestamp
    flask_app.jinja_env.globals['_'] = gettext


def _init_monitor(flask_app: Flask):
    _logger.info('initiating Monitor...')

    from app.business.clients import FlaskMonitor
    FlaskMonitor(flask_app, default_labels=DEFAULT_LABELS)

    pyroscope_server_address = flask_app.config["PYROSCOPE_CONFIG"].get("server_address", "")
    if pyroscope_server_address and (os.getenv("COINEX_APPLICATION_TYPE", "").upper() == "ADMIN"):
        # init pyroscope
        pyroscope.configure(
            application_name    = "coinex_backend",
            server_address      = pyroscope_server_address,
            sample_rate         = 100, # default is 100
            detect_subprocesses = True, # detect subprocesses started by the main process; default is False
            oncpu               = True, # report cpu time only; default is True
            gil_only            = True, # only include traces for threads that are holding on to the Global Interpreter Lock;
            enable_logging      = False, # does enable logging facility; default is False
            tags                = {
                "project": "Coinex",
                "service": "CoinexCom",
                "module": "CoinexComWeb",
                "hostname": socket.gethostname()
            }
)


def _init_resp_size_inspector(flask_app: Flask):
    _logger.info('initiating Response Size Inspector...')


    def after_request(response):
        # reposne over 100kB
        if response and response.content_length and response.content_length > 100 * 1024:
            _logger.warning("response size too large", extra={"request_path": request.path, "response_size": response.content_length})
        return response
            
    flask_app.after_request(after_request)