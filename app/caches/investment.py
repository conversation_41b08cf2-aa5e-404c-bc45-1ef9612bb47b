# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import timedelta
from decimal import Decimal
from enum import Enum
import json
from sqlalchemy import func

from app.models.investment import AssetInvestmentConfig, InvStatisticTimeRange, UserDayInterestDetail, UserDayInterestHistory
from app.utils import now, amount_to_str, quantize_amount, today
from app.models.activity import Coupon, CouponDailyBalanceHistory
from app.utils.parser import JsonEncoder
from .base import HashCache
from ..models.equity_center import UserInvestIncreaseEquity, UserDailyIncEquityHistory
from app.models.daily import DailyInvestmentReport
from app.models.user import SubAccount


class Investment7DaysEARCache(HashCache):
    """七日年化"""

    def __init__(self):
        super().__init__(None)

    def reload(self):
        data = self.get_data()
        if data:
            self.save({asset: amount_to_str(value, 6) for asset, value in data.items()})

    @classmethod
    def get_data(cls):
        from app.models.daily import DailyInvestmentReport

        start = now() - timedelta(days=8)
        query = DailyInvestmentReport.query.filter(
            DailyInvestmentReport.report_date > start,
        )
        open_asset = AssetInvestmentConfig.get_valid_assets()
        result = defaultdict(lambda: defaultdict(lambda: dict(day_rate=Decimal(), interest_amount=Decimal())))
        for record in query:
            result[record.asset][record.report_date]["day_rate"] = record.day_rate
            result[record.asset][record.report_date]["interest_amount"] = record.investment_interest_amount
        final_result = dict()
        for asset, _d in result.items():
            if asset not in open_asset:
                continue
            total_interest_amount = Decimal()
            total_amount = Decimal()
            for _, _d1 in _d.items():
                day_rate = _d1["day_rate"]
                total_interest_amount += _d1["interest_amount"]
                if day_rate > Decimal():
                    total_amount += _d1["interest_amount"] / _d1["day_rate"]
            if total_amount > Decimal():
                final_result[asset] = quantize_amount(total_interest_amount * 365 / total_amount, 6)
            else:
                final_result[asset] = Decimal()

        return final_result

    def read_data(self):
        result = self.hgetall()
        return {asset: Decimal(rate) for asset, rate in result.items()}

    def get_rate(self, asset: str, default: Decimal = None) -> Decimal | None:
        if not (rate := self.hget(asset)):
            return default
        return Decimal(rate)


class InvestmentYesterdayARRCache(HashCache):
    """昨日年化"""

    def __init__(self):
        super().__init__(None)

    def reload(self):
        data = self.get_data()
        self.save(data)

    @classmethod
    def get_data(cls):
        from app.models.daily import DailyInvestmentReport

        yesterday = now().date() - timedelta(days=1)
        q = DailyInvestmentReport.query.filter(DailyInvestmentReport.report_date == yesterday).all()
        return {v.asset: amount_to_str(v.day_rate * 365, 6) for v in q}

    def read_data(self):
        result = self.hgetall()
        return {asset: Decimal(rate) for asset, rate in result.items()}


class InvestmentIncTotalIncomeCache(HashCache):
    """用户理财加息-总收益"""

    # todo 保留一段时间，后续删除

    def __init__(self, user_id: int):
        super().__init__(str(user_id))

    def get_user_income(self) -> dict:
        data = self.hgetall()
        return {k: Decimal(v) for k, v in data.items()}

    @classmethod
    def reload(cls):
        rows = (
            CouponDailyBalanceHistory.query.filter(
                CouponDailyBalanceHistory.coupon_type == Coupon.CouponType.INVESTMENT_INCREASE_RATE,
            )
            .group_by(
                CouponDailyBalanceHistory.user_id,
                CouponDailyBalanceHistory.asset,
            )
            .with_entities(
                CouponDailyBalanceHistory.user_id,
                CouponDailyBalanceHistory.asset,
                func.sum(CouponDailyBalanceHistory.amount).label("total_amount"),
            )
            .all()
        )

        user_asset_income_map = defaultdict(lambda: defaultdict(Decimal))
        for r in rows:
            user_asset_income_map[r.user_id][r.asset] = r.total_amount

        for user_id, asset_income_map in user_asset_income_map.items():
            _cache = cls(user_id)
            _data = {str(k): amount_to_str(v) for k, v in asset_income_map.items()}
            _cache.save(_data)


class InvIncEquityYesIncomeCache(HashCache):
    """用户理财加息-权益昨日收益"""

    def __init__(self, user_id: int):
        super().__init__(str(user_id))

    def get_user_income(self) -> dict:
        data = self.hgetall()
        return {k: Decimal(v) for k, v in data.items()}

    @classmethod
    def reload(cls):
        model = UserDailyIncEquityHistory
        rows = model.query.filter(
            model.report_date == today() - timedelta(days=1),
            model.status == model.Status.FINISHED,
        ).all()

        user_asset_income_map = defaultdict(lambda: defaultdict(Decimal))
        for r in rows:
            user_asset_income_map[r.user_id][r.asset] = r.interest_amount

        for user_id, asset_income_map in user_asset_income_map.items():
            _cache = cls(user_id)
            _data = {str(k): amount_to_str(v) for k, v in asset_income_map.items()}
            _cache.save(_data)


class InvIncEquityTotalIncomeCache(HashCache):
    """用户理财加息-权益总收益"""

    def __init__(self, user_id: int):
        super().__init__(str(user_id))

    def get_user_income(self) -> dict:
        data = self.hgetall()
        return {k: Decimal(v) for k, v in data.items()}

    @classmethod
    def reload(cls):
        model = UserInvestIncreaseEquity
        rows = (
            model.query.filter(
                model.increase_amount > 0,
            )
            .group_by(
                model.user_id,
                model.investment_asset,
            )
            .with_entities(
                model.user_id,
                model.investment_asset,
                func.sum(model.increase_amount).label("total_amount"),
            )
        )

        user_asset_income_map = defaultdict(lambda: defaultdict(Decimal))
        for r in rows:
            if not r.investment_asset:
                continue
            user_asset_income_map[r.user_id][r.investment_asset] = r.total_amount

        for user_id, asset_income_map in user_asset_income_map.items():
            _cache = cls(user_id)
            _data = {str(k): amount_to_str(v) for k, v in asset_income_map.items()}
            _cache.save(_data)


class InvestmentBalanceRankCache(HashCache):
    """理财余额排名"""

    LIMIT = 1000
    TTL = 86400

    def __init__(self, asset: str):
        super().__init__(asset)

    def get_rank(self) -> dict:
        return self.hgetall()

    @classmethod
    def reload(cls):
        from app.business.investment import InvestmentDataProc

        dt = now()
        asset_user_balance = InvestmentDataProc.get_dt_asset_user_snap_map(dt)
        for asset, user_balance in asset_user_balance.items():
            rank_user_balance = sorted(user_balance.items(), key=lambda x: x[1], reverse=True)[: cls.LIMIT]
            _cache = cls(asset)
            _cache.save({str(user_id): amount_to_str(balance, 8) for user_id, balance in rank_user_balance})
            _cache.expire(cls.TTL)


class InterestRankCache(HashCache):
    """理财利息排名"""

    LIMIT = 100
    TTL = 86400

    class Interval(Enum):
        day_7 = 7
        day_30 = 30

    def __init__(self, asset: str, interval: Interval):
        super().__init__(f"{asset}:{interval.name}")

    def get_rank(self) -> dict:
        data = self.hgetall()
        return {user_id: json.loads(v) for user_id, v in data.items()}

    @classmethod
    def reload(cls):
        for interval in cls.Interval:
            end = today() - timedelta(days=1)
            start = end - timedelta(days=interval.value)
            asset_user_interest_map = cls.get_asset_user_interest_map(start, end)
            user_ids = set()
            all_asset_rank_map = defaultdict(lambda: defaultdict(list))
            for asset, user_interest in asset_user_interest_map.items():
                rank_user_interest = sorted(user_interest.items(), key=lambda x: x[1], reverse=True)[: cls.LIMIT]
                all_asset_rank_map[asset] = rank_user_interest
                user_ids.update(user_id for user_id, _ in rank_user_interest)

            user_interest_detail_map = cls.get_users_detail_map(user_ids, start, end)

            # 给asset_rank_map赋值
            for asset, user_interest in all_asset_rank_map.items():
                rank_map = {}
                for user_id, interest in user_interest:
                    rank_map[user_id] = json.dumps(
                        dict(
                            total_amount=interest,
                            **user_interest_detail_map[user_id][asset],
                        ),
                        cls=JsonEncoder,
                    )
                _cache = cls(asset, interval)
                _cache.save(rank_map)
                _cache.expire(cls.TTL)

    @classmethod
    def get_asset_user_interest_map(cls, start, end):
        model = UserDayInterestHistory
        sum_query = (
            model.query.filter(
                model.report_date >= start,
                model.report_date <= end,
                model.status == model.Status.SUCCESS,
            )
            .group_by(model.user_id, model.asset)
            .with_entities(
                func.sum(model.interest_amount).label("total_amount"),
                model.user_id,
                model.asset,
            )
            .all()
        )

        asset_user_interest_map = defaultdict(lambda: defaultdict(Decimal))
        for row in sum_query:
            asset_user_interest_map[row.asset][row.user_id] = row.total_amount
        return asset_user_interest_map

    @classmethod
    def get_users_detail_map(cls, user_ids, start, end):
        # 获取收益详情
        d_model = UserDayInterestDetail
        rows = (
            d_model.query.filter(
                d_model.user_id.in_(user_ids),
                d_model.report_date >= start,
                d_model.report_date <= end,
            )
            .group_by(
                d_model.user_id,
                d_model.interest_type,
                d_model.asset,
            )
            .with_entities(
                d_model.user_id,
                d_model.asset,
                d_model.interest_type,
                func.sum(d_model.interest_amount).label("total_amount"),
            )
            .all()
        )
        user_interest_detail_map = defaultdict(lambda: defaultdict(lambda: defaultdict(Decimal)))
        for row in rows:
            user_interest_detail_map[row.user_id][row.asset][row.interest_type.name] = row.total_amount
        return user_interest_detail_map


class InvestmentConfigCache(HashCache):
    """理财配置"""

    model = AssetInvestmentConfig

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        configs = cls.model.query.filter(cls.model.status == cls.model.StatusType.OPEN)
        config_map = {config.asset: json.dumps(config.to_dict(enum_to_name=True), cls=JsonEncoder) for config in configs}
        cls().save(config_map)

    def get_asset_config(self, asset: str) -> dict:
        data = self.hget(asset)
        return json.loads(data) if data else {}

    def get_all_config(self) -> dict:
        data = self.hgetall()
        return {asset: json.loads(v) for asset, v in data.items()}

    def get_assets(self) -> list:
        return self.hkeys()

    def get_asset_show_rate(self, asset: str, is_sub: bool = False) -> Decimal:
        config = self.get_asset_config(asset)
        rule_map = config.get("rule_map") or {}
        ladder_rule = rule_map.get(self.model.ConfigType.LADDER.name, {})
        fixed_rule = rule_map.get(self.model.ConfigType.FIXED.name, {})
        base_rate = Decimal(config.get("base_rate", 0))
        if fixed_rule:
            base_rate += Decimal(fixed_rule.get("rate", 0))
        if ladder_rule and not is_sub:
            base_rate += Decimal(ladder_rule.get("rate", 0))
        return base_rate


class InvestmentStatisticCache(HashCache):
    """活期理财实时统计"""

    def __init__(self, time_range: InvStatisticTimeRange):
        super().__init__(str(time_range.value))

    def get_data(self) -> dict:
        if data := self.read():
            return {k: json.loads(v) for k, v in data.items()}
        return {}

    def get_asset_data(self, asset: str) -> dict:
        """获取特定资产的数据"""
        data = self.hget(asset)
        return json.loads(data) if data else {}

    @classmethod
    def reload_all(cls):
        """重新加载所有时间范围的统计数据"""
        from app.business.investment import InvestmentDataProc, PriceManager

        latest_date = today()
        asset_config = {row.asset: row for row in AssetInvestmentConfig.query.all()}
        sub_sets = {i.user_id for i in SubAccount.query.with_entities(SubAccount.user_id).all()}

        # 获取 server 快照数据
        asset_user_balance = InvestmentDataProc.get_dt_asset_user_snap_map(now())
        prices = PriceManager.assets_to_usd()

        r_model = DailyInvestmentReport
        # 为每个时间范围生成缓存数据
        for time_range in InvStatisticTimeRange:
            start_date = latest_date - timedelta(days=time_range.value)

            # 构建查询
            query = r_model.query.filter(r_model.report_date >= start_date, r_model.report_date <= latest_date)

            # 聚合数据
            aggregated_data = (
                query.with_entities(
                    r_model.asset,
                    func.sum(r_model.investment_interest_usd).label("total_interest_usd"),
                    func.sum(r_model.base_interest_usd).label("total_base_interest_usd"),
                    func.sum(r_model.ladder_interest_usd + r_model.fixed_interest_usd).label("total_subsidy_interest_usd"),
                )
                .group_by(DailyInvestmentReport.asset)
                .all()
            )
            asset_interest_map = {row.asset: row for row in aggregated_data}

            # 计算资产USD数据
            asset_usd_map = defaultdict(Decimal)
            ladder_usd_map = defaultdict(Decimal)

            for asset, user_balance in asset_user_balance.items():
                price = prices.get(asset, Decimal())
                asset_usd_map[asset] += sum(user_balance.values()) * price
                cfg = asset_config[asset]
                rule_map = cfg.rule_map or {}
                ladder_rule = rule_map.get(AssetInvestmentConfig.ConfigType.LADDER.name, {})
                ladder_limit = Decimal(ladder_rule.get("limit", 0))
                for user_id, balance in user_balance.items():
                    if user_id in sub_sets:
                        continue
                    if ladder_limit > 0 and balance:
                        ladder_usd_map[asset] += min(balance, ladder_limit) * price

            # 构建缓存数据
            cache_data = {}
            for asset in asset_user_balance.keys():
                row = asset_interest_map.get(asset)
                investment_usd = asset_usd_map.get(asset, 0)
                total_interest_usd = row.total_interest_usd if row else 0

                cache_data[asset] = json.dumps(
                    {
                        "asset": asset,
                        "user_count": len(asset_user_balance.get(asset, {}).keys()),
                        "investment_usd": amount_to_str(investment_usd, 2),
                        "ladder_usd": amount_to_str(ladder_usd_map.get(asset, 0), 2),
                        "total_interest_usd": amount_to_str(total_interest_usd, 2),
                        "base_interest_usd": amount_to_str(row.total_base_interest_usd, 2) if row else "0",
                        "subsidy_interest_usd": amount_to_str(row.total_subsidy_interest_usd, 2) if row else "0",
                        "annual_rate": cls.calculate_annual_rate(total_interest_usd, investment_usd, time_range.value),
                        "update_time": now(),
                    },
                    cls=JsonEncoder,
                )

            # 保存到缓存
            cls(time_range).save(cache_data)

    @classmethod
    def calculate_annual_rate(cls, interest, investment, days):
        """计算年化利率"""
        if investment and interest:
            return f"{interest / days / investment * 365 * 100:.2f}%"
        return "0.00%"