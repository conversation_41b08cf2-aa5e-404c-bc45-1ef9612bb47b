#!/usr/bin/python
# -*- coding: utf-8 -*-

from .base import Hash<PERSON>ache, StringCache


class UserAccountPLDataCache(HashCache):

    """用户账户盈亏数据分析缓存"""
    def __init__(self, user_id: int, account_type: str):
        super().__init__(f"{user_id}:{account_type}")


class UserAssetPLDataCache(HashCache):

    """用户币种维度盈亏数据分析缓存"""
    def __init__(self, user_id: int, asset: str, account_type: str):
        super().__init__(f"{user_id}:{asset}:{account_type}")


class UserAccountDealCache(StringCache):

    """用户账户交易数据缓存"""
    def __init__(self, user_id: int, account_type: str):
        super().__init__(f"{user_id}:{account_type}")


class BalanceHistoryDailyIdCache(StringCache):
    """Server balance history 每日最小ID缓存(针对现货)"""

    ttl = 86400 * 2

    def __init__(self, db_index: int, table_index: int, ts: int):
        super().__init__(f"{db_index}:{table_index}:{ts}")


class BalanceHistorySyncCache(HashCache):

    def __init__(self, type_: str, user_id: int, account_type: str):

        # type_: spot/perpetual
        super().__init__(f"{type_}:{user_id}:{account_type}")

class AssetPLSyncCache(HashCache):
    """
    币种维度盈亏分析缓存
    """
    def __init__(self, user_id: int):
        super().__init__(f"{user_id}")