# -*- coding: utf-8 -*-
import json
import time
from datetime import timed<PERSON><PERSON>
from decimal import Decimal, ROUND_UP, ROUND_DOWN
from functools import cached_property
from typing import Optional, List, Dict, Tuple

from flask import current_app
from sqlalchemy import func, or_

from app.exceptions import OrderException, InvalidArgument
from app.business import (
    ServerClient,
    ServerResponseCode,
    BalanceBusiness,
    SPOT_ACCOUNT_ID,
    CacheLock,
    LockKeys,
    PriceManager,
    SiteSettings,
)
from app.caches import MarketCache, AmmMarketCache
from app.caches.spot import ExchangeMarketCache
from app.common import (
    CeleryQueues,
    OrderSideType,
    OrderOption,
    TradeBusinessType,
    TradeType, ProducerTopics,
)
from app.config import config
from app.models import db
from app.business.order import Order
from app.models.exchange import (
    AssetExchangeOrder,
    AssetExchangeSysUser,
    AssetExchangeOrderFee,
    SysAssetExchangeOrder,
    AssetExchangeOrderTransferHistory,
    SysAssetExchangeTradeOrderHistory,
    ExchangeFeeType,
)
from app.producer import exactly_once_producer
from app.utils import (
    amount_to_str,
    quantize_amount,
    celery_task,
    route_module_to_celery_queue,
)
from app.utils.date_ import now, current_timestamp, datetime_to_time

route_module_to_celery_queue(__name__, CeleryQueues.EXCHANGE)


SYS_ORDER_TIMEOUT_SECONDS = 60 * 5  # 第一个、第二个市场系统挂单 超时时间
BUY_BACK_TIMEOUT_SECONDS = 60 * 120  # 买回系统挂单 超时时间
MID_ASSETS = ("USDT", "BTC", "USDC")  # 间接兑换支持的中间币种


def alert_exchange_msg(msg: str, ttl: int = 0, msg_id: str = None):
    """ 发送兑换相关的告警消息 """
    from app.business.alert import send_alert_notice

    current_app.logger.error(msg)
    send_alert_notice(msg, config["ADMIN_CONTACTS"].get("web_notice"), ttl, msg_id)


def get_target_asset(source_asset: str, market: str) -> str:
    """ 获取目标币种 """
    market_info = MarketCache(market).dict
    base_asset = market_info["base_asset"]
    quote_asset = market_info["quote_asset"]
    if source_asset == base_asset:
        return quote_asset
    elif source_asset == quote_asset:
        return base_asset
    raise ValueError(f"market {market} get_target_asset by {source_asset} not match")


def send_exchange_order_notice(order: AssetExchangeOrder):
    """ 发送兑换通知 """
    from app.business.push import send_asset_exchange_order_push

    send_asset_exchange_order_push.delay(order.id, current_timestamp(to_int=True))


class ExchangeOrderManager:
    """ 处理 用户兑换订单 """

    def __init__(self, exchange_order_id: int):
        self.exchange_order_id = exchange_order_id
        self.exchange_order: AssetExchangeOrder = AssetExchangeOrder.query.get(exchange_order_id)
        self.markets = json.loads(self.exchange_order.exchange_path)
        self.client = ServerClient(current_app.logger)

    def process(self):
        with CacheLock(key=LockKeys.exchange_order(exchange_order_id=self.exchange_order_id), wait=False):
            db.session.rollback()

            if self.exchange_order.status == AssetExchangeOrder.Status.CREATED:
                self.transfer_source_asset()
            if self.exchange_order.status == AssetExchangeOrder.Status.EXCHANGING:
                self.process_sys_order()
            if self.exchange_order.status == AssetExchangeOrder.Status.EXCHANGED:
                sys_orders = self.get_all_sys_orders()
                self.generate_fee_history(sys_orders)
                self.generate_exchanged_transfer(sys_orders)
                self.transfer_target_asset()
                self.transfer_remain_source_asset_if_necessary()
                self.finish_order()
                self.after_finished()

    def after_finished(self):
        from app.business.equity_center.cashback import cashback_collect_exchange_order_fee_task

        cashback_collect_exchange_order_fee_task.delay(self.exchange_order.id)
        message = dict(
            event_data=dict(
                market="",
                user_id=self.exchange_order.user_id,
                amount=self.exchange_order.target_asset_exchanged_amount,
                amount_asset=self.exchange_order.target_asset
            ),
            biz_type=AssetExchangeOrder.__tablename__,
            biz_id=self.exchange_order.id,
            timestamp=datetime_to_time(self.exchange_order.created_at),
            user_id=self.exchange_order.user_id
        )
        exactly_once_producer.send_message(ProducerTopics.EXCHANGE_DEALS, message)

    def finish_order(self):
        # 结束兑换订单
        transfer_rows: List[AssetExchangeOrderTransferHistory] = AssetExchangeOrderTransferHistory.query.filter(
            AssetExchangeOrderTransferHistory.exchange_order_id == self.exchange_order_id,
        ).all()
        fee_row: AssetExchangeOrderFee = AssetExchangeOrderFee.query.filter(
            AssetExchangeOrderFee.exchange_order_id == self.exchange_order_id,
            AssetExchangeOrderFee.type == ExchangeFeeType.WEB,
        ).first()
        if fee_row:
            fee_asset, fee_amount = fee_row.asset, fee_row.amount
        else:
            fee_asset, fee_amount = None, None

        zero = Decimal()
        source_remain_amount = target_amount = zero
        for transfer in transfer_rows:
            assert transfer.status == AssetExchangeOrderTransferHistory.Status.FINISHED
            if transfer.type == AssetExchangeOrderTransferHistory.Type.TRANSFER_TARGET_ASSET:
                target_amount = transfer.amount
            elif transfer.type == AssetExchangeOrderTransferHistory.Type.TRANSFER_REMAIN_SOURCE_ASSET:
                source_remain_amount = transfer.amount
        source_asset_exchanged_amount = self.exchange_order.source_asset_amount - source_remain_amount
        if target_amount == zero:
            result = AssetExchangeOrder.Result.FAILED
            fee_asset, fee_amount = None, None
            source_asset_exchanged_amount = zero
        elif source_remain_amount > zero:
            result = AssetExchangeOrder.Result.PARTIAL
        else:
            result = AssetExchangeOrder.Result.ALL

        # 更新订单信息
        self.exchange_order.result = result
        self.exchange_order.status = AssetExchangeOrder.Status.FINISHED
        self.exchange_order.fee_asset, self.exchange_order.fee_amount = fee_asset, fee_amount
        self.exchange_order.source_asset_exchanged_amount = source_asset_exchanged_amount
        self.exchange_order.target_asset_exchanged_amount = target_amount

        # 更新sys_user 状态
        sys_user = AssetExchangeSysUser.query.filter(AssetExchangeSysUser.user_id == self.exchange_order.sys_user_id).first()
        assert sys_user and sys_user.status == AssetExchangeSysUser.Status.UNUSABLE
        sys_user.status = AssetExchangeSysUser.Status.USABLE

        db.session.commit()

        send_exchange_order_notice(self.exchange_order)

    def process_sys_order(self):
        assert self.exchange_order.status == AssetExchangeOrder.Status.EXCHANGING
        to_process_sys_order = self._get_or_create_to_process_sys_order()
        if to_process_sys_order:
            sys_order_manager = SysExchangeOrderManager(to_process_sys_order.id, self.exchange_order)
            sys_order_manager.process()

            last_process_oid = to_process_sys_order.id
            to_process_sys_order = self._get_or_create_to_process_sys_order()  # re-check
            if to_process_sys_order and to_process_sys_order.id != last_process_oid:
                # 新生成的系统兑换单
                sys_order_manager = SysExchangeOrderManager(to_process_sys_order.id, self.exchange_order)
                sys_order_manager.process()
                to_process_sys_order = self._get_or_create_to_process_sys_order()  # re-check

        if not to_process_sys_order:
            self.exchange_order.status = AssetExchangeOrder.Status.EXCHANGED
            db.session.commit()

    def _get_or_create_to_process_sys_order(self) -> Optional[SysAssetExchangeOrder]:
        # 获取需要处理的系统兑换单
        sys_orders: List[SysAssetExchangeOrder] = SysAssetExchangeOrder.query.filter(
            SysAssetExchangeOrder.exchange_order_id == self.exchange_order_id,
        ).all()
        market_num = len(self.markets)

        if market_num == 1:
            if not sys_orders:
                # 生成首个市场SysOrder
                sys_order = self.new_first_market_sys_order()
                db.session.add(sys_order)
                db.session.commit()
            else:
                sys_order = sys_orders[0]

            if sys_order.status == SysAssetExchangeOrder.Status.PROCESSING:
                return sys_order
            return None

        else:
            # 跨市场兑换
            sys_order1 = sys_order2 = buy_back_order = None
            for o in sys_orders:
                if o.type == SysAssetExchangeOrder.Type.EXCHANGE:
                    if o.market == self.markets[0]:
                        sys_order1 = o
                    elif o.market == self.markets[1]:
                        sys_order2 = o
                elif o.type == SysAssetExchangeOrder.Type.BUY_BACK:
                    buy_back_order = o

            if not sys_order1:
                sys_order1 = self.new_first_market_sys_order()
                db.session.add(sys_order1)
                db.session.commit()
            if sys_order1.status == SysAssetExchangeOrder.Status.PROCESSING:
                return sys_order1

            # sys_order1 FINISHED
            if sys_order1.target_asset_exchanged_amount == Decimal():
                # 市场1兑换失败，不再生成市场2的系统兑换单
                return None

            if not sys_order2:
                # 第一个单已完成，生成第二个市场的单
                sys_order2 = self.new_second_market_sys_order(sys_order1)
                db.session.add(sys_order2)
                db.session.commit()
            if sys_order2.status == SysAssetExchangeOrder.Status.PROCESSING:
                return sys_order2

            # sys_order2 FINISHED
            if sys_order2.source_asset_remain_amount > Decimal():
                if not buy_back_order:
                    # 第二个市场的单 部分兑换，生成买回订单
                    buy_back_order = self.new_buy_back_sys_order(sys_order2)
                    db.session.add(buy_back_order)
                    db.session.commit()
                if buy_back_order.status == SysAssetExchangeOrder.Status.PROCESSING:
                    return buy_back_order
            return None

    def get_all_sys_orders(self) -> List[SysAssetExchangeOrder]:
        # 系统挂单都完成了，才调用
        assert self.exchange_order.status == AssetExchangeOrder.Status.EXCHANGED
        sys_orders: List[SysAssetExchangeOrder] = SysAssetExchangeOrder.query.filter(
            SysAssetExchangeOrder.exchange_order_id == self.exchange_order_id,
        ).all()
        return sys_orders

    def unpack_mul_market_sys_orders(self, sys_orders: List[SysAssetExchangeOrder]):
        sys_order1 = sys_order2 = buy_back_order = None
        for o in sys_orders:
            if o.type == SysAssetExchangeOrder.Type.EXCHANGE:
                if o.market == self.markets[0]:
                    sys_order1 = o
                elif o.market == self.markets[1]:
                    sys_order2 = o
            elif o.type == SysAssetExchangeOrder.Type.BUY_BACK:
                buy_back_order = o
                assert buy_back_order.result == SysAssetExchangeOrder.Result.ALL  # 买回一定要兑换完
        return sys_order1, sys_order2, buy_back_order

    def generate_fee_history(self, finished_sys_orders: List[SysAssetExchangeOrder]) -> AssetExchangeOrderFee:
        """
        生成手续费记录，如果可以使用CET抵扣，则先扣CET余额；否则扣目标币种（划转目标币种时扣）
        Web收的手续费记录可能没有，Server收的手续费记录可能没有或有多条
        """
        from app.business.order import OrderFeeOption
        from app.business.fee import FeeFetcher

        exist_fee_record = AssetExchangeOrderFee.query.filter(
            AssetExchangeOrderFee.exchange_order_id == self.exchange_order_id,
        ).first()
        if exist_fee_record:
            return exist_fee_record

        zero = Decimal()
        user_id = self.exchange_order.user_id
        target_asset = self.exchange_order.target_asset
        if len(self.markets) == 1:
            # 目标币种兑换的数目
            target_asset_amount = finished_sys_orders[0].target_asset_exchanged_amount
        else:
            _, sys_order2, _ = self.unpack_mul_market_sys_orders(finished_sys_orders)
            target_asset_amount = sys_order2.target_asset_exchanged_amount if sys_order2 else zero

        web_fee_sys_orders = []  # 需要Web收取手续费的系统挂单列表，单独计算，但一次性收取
        server_fee_sys_orders = []  # Server已经收了手续费的系统挂单列表
        for so in finished_sys_orders:
            if so.fee_type == ExchangeFeeType.SERVER:
                server_fee_sys_orders.append(so)
            if so.type == SysAssetExchangeOrder.Type.BUY_BACK or so.fee_type == ExchangeFeeType.SERVER:
                # 买回订单 or server已经收了（AMM市场），Web不再收
                continue
            web_fee_sys_orders.append(so)

        # 写入Server收的手续费记录，只commit一次
        for so in server_fee_sys_orders:
            server_fee_record = AssetExchangeOrderFee(
                exchange_order_id=self.exchange_order_id,
                user_id=user_id,
                asset=so.fee_asset,
                amount=so.fee_amount,
                status=AssetExchangeOrderFee.Status.FINISHED,  # 状态是已完成
                finished_at=now(),
                type=ExchangeFeeType.SERVER,
            )
            db.session.add(server_fee_record)

        if not web_fee_sys_orders:
            zero_fee_record = AssetExchangeOrderFee(
                exchange_order_id=self.exchange_order_id,
                user_id=user_id,
                asset=target_asset,
                amount=0,
                status=AssetExchangeOrderFee.Status.FINISHED,
                finished_at=now(),
                type=ExchangeFeeType.WEB,
            )
            db.session.add(zero_fee_record)
            db.session.commit()
            return zero_fee_record

        cet = "CET"
        cet_price = PriceManager.asset_to_usd(cet)
        target_asset_price = PriceManager.asset_to_usd(self.exchange_order.target_asset)

        # 计算系统兑换单需要的手续费
        sys_order_cet_fee_map = {}
        sys_order_target_fee_map = {}
        for fee_sys_order in web_fee_sys_orders:
            market = fee_sys_order.market
            market_info = MarketCache(market).dict
            sys_target_asset = fee_sys_order.target_asset
            sys_target_asset_price = PriceManager.asset_to_usd(sys_target_asset)
            deal_usd = quantize_amount(fee_sys_order.target_asset_exchanged_amount * sys_target_asset_price, 8)

            # taker手续费
            taker_fee_rate = FeeFetcher(user_id).fetch(TradeBusinessType.SPOT, market)[TradeType.TAKER]

            # 是否CET抵扣
            fee_op = OrderFeeOption(market_info, user_id)
            fee_discount_asset, fee_discount = fee_op.fee_asset, fee_op.fee_discount
            if fee_discount_asset:
                # 开启CET抵扣, 手续费 =【目标币种数量 * 目标币种实时价格 / CET实时价格】* CET抵扣费率
                cet_fee_amount = deal_usd / cet_price * fee_discount * taker_fee_rate
                cet_fee_amount = quantize_amount(cet_fee_amount, 8)
                if cet_fee_amount < zero:
                    cet_fee_amount = zero
                if target_asset_amount == zero:
                    cet_fee_amount = zero
                sys_order_cet_fee_map[fee_sys_order.id] = cet_fee_amount

            # 扣目标币种  手续费 = 成交市值 / 目标币种实时价格 * 用户实际手续费率
            target_fee_amount = quantize_amount(deal_usd / target_asset_price * taker_fee_rate, 8)
            if target_fee_amount < zero:
                # 避免负费率
                target_fee_amount = zero
            if target_asset_amount == zero:
                # 兑换失败时，不收手续费
                target_fee_amount = zero
            sys_order_target_fee_map[fee_sys_order.id] = target_fee_amount

        if sys_order_cet_fee_map and len(sys_order_cet_fee_map) == len(web_fee_sys_orders):
            # 手续费都支持收CET
            total_cet_fee_amount = sum(sys_order_cet_fee_map.values())
            try:
                if total_cet_fee_amount > zero:
                    self.client.add_user_balance(
                        user_id=user_id,
                        asset=cet,
                        amount=amount_to_str(-total_cet_fee_amount),
                        business=BalanceBusiness.EXCHANGE_ORDER_TRANSFER_FEE,
                        business_id=self.exchange_order.id,
                        detail={"remark": f"fee for exchange_order {self.exchange_order_id}"},
                        account_id=SPOT_ACCOUNT_ID,
                    )
            except Exception as _e:
                # 忽略扣减cet的异常，很小概率存在多扣一次cet的手续费
                current_app.logger.error(f"exchange_order: {self.exchange_order_id} deducted cet discount error: {_e}")
            else:
                cet_fee_record = AssetExchangeOrderFee(
                    exchange_order_id=self.exchange_order_id,
                    user_id=user_id,
                    asset=cet,
                    amount=total_cet_fee_amount,
                    status=AssetExchangeOrderFee.Status.FINISHED,  # 状态是已完成
                    finished_at=now(),
                    type=ExchangeFeeType.WEB,
                )
                db.session.add(cet_fee_record)
                # 写入系统兑换单的手续费字段（Web收的）
                for so in web_fee_sys_orders:
                    so.fee_asset = cet
                    so.fee_amount = sys_order_cet_fee_map[so.id]
                    so.fee_type = ExchangeFeeType.WEB
                db.session.commit()
                return cet_fee_record

        total_target_fee_amount = sum(sys_order_target_fee_map.values())
        target_fee_record = AssetExchangeOrderFee(
            exchange_order_id=self.exchange_order_id,
            user_id=user_id,
            asset=target_asset,
            amount=total_target_fee_amount,
            status=AssetExchangeOrderFee.Status.CREATED,  # 状态在划转时修改, 目标币种是CET时来区分是否已扣减
            type=ExchangeFeeType.WEB,
        )
        db.session.add(target_fee_record)
        # 写入系统兑换单的手续费字段（Web收的）
        for so in web_fee_sys_orders:
            so.fee_asset = target_asset
            so.fee_amount = sys_order_target_fee_map[so.id]
            so.fee_type = ExchangeFeeType.WEB

        db.session.commit()
        return target_fee_record

    def generate_exchanged_transfer(self, finished_sys_orders: List[SysAssetExchangeOrder]):
        # 生成 目标币种划转、剩余兑换币种划转(如果有) 的记录
        exist_target_transfer_history = AssetExchangeOrderTransferHistory.query.filter(
            AssetExchangeOrderTransferHistory.exchange_order_id == self.exchange_order_id,
            AssetExchangeOrderTransferHistory.type == AssetExchangeOrderTransferHistory.Type.TRANSFER_TARGET_ASSET,
        ).first()
        if exist_target_transfer_history:
            return

        assert self.exchange_order.status == AssetExchangeOrder.Status.EXCHANGED
        sys_exchange_user_id = self.exchange_order.sys_user_id
        user_id = self.exchange_order.user_id
        zero = Decimal()

        if len(self.markets) == 1:
            sys_order = finished_sys_orders[0]
            source_asset_remain_amount = sys_order.source_asset_remain_amount
            target_asset = sys_order.target_asset
            target_amount = sys_order.target_asset_exchanged_amount
        else:
            sys_order1, sys_order2, buy_back_order = self.unpack_mul_market_sys_orders(finished_sys_orders)
            if sys_order2:
                source_asset_remain_amount = sys_order1.source_asset_remain_amount
                if buy_back_order:
                    source_asset_remain_amount += buy_back_order.target_asset_exchanged_amount
                target_asset = sys_order2.target_asset
                target_amount = sys_order2.target_asset_exchanged_amount
            else:
                # 只有市场1的系统兑换单（市场1 兑换失败）
                assert sys_order1.target_asset_exchanged_amount == zero
                assert not buy_back_order
                source_asset_remain_amount = sys_order1.source_asset_remain_amount
                target_asset = self.exchange_order.target_asset
                target_amount = zero

        source_asset = self.exchange_order.source_asset
        assert target_amount >= zero

        target_transfer_history = AssetExchangeOrderTransferHistory(
            exchange_order_id=self.exchange_order_id,
            type=AssetExchangeOrderTransferHistory.Type.TRANSFER_TARGET_ASSET,
            asset=target_asset,
            amount=target_amount,
            from_user_id=sys_exchange_user_id,
            to_user_id=user_id,
        )
        db.session.add(target_transfer_history)

        if source_asset_remain_amount > zero:
            remain_transfer_history = AssetExchangeOrderTransferHistory(
                exchange_order_id=self.exchange_order_id,
                type=AssetExchangeOrderTransferHistory.Type.TRANSFER_REMAIN_SOURCE_ASSET,
                asset=source_asset,
                amount=source_asset_remain_amount,
                from_user_id=sys_exchange_user_id,
                to_user_id=user_id,
            )
            db.session.add(remain_transfer_history)
        db.session.commit()

    def transfer_source_asset(self) -> bool:
        # 划转兑换币种
        assert self.exchange_order.status == AssetExchangeOrder.Status.CREATED

        history: AssetExchangeOrderTransferHistory = AssetExchangeOrderTransferHistory.query.filter(
            AssetExchangeOrderTransferHistory.exchange_order_id == self.exchange_order_id,
            AssetExchangeOrderTransferHistory.type == AssetExchangeOrderTransferHistory.Type.TRANSFER_SOURCE_ASSET,
        ).first()
        if not history:
            sys_exchange_user_id = self.exchange_order.sys_user_id
            assert sys_exchange_user_id
            history = AssetExchangeOrderTransferHistory(
                exchange_order_id=self.exchange_order_id,
                type=AssetExchangeOrderTransferHistory.Type.TRANSFER_SOURCE_ASSET,
                asset=self.exchange_order.source_asset,
                amount=self.exchange_order.source_asset_amount,
                from_user_id=self.exchange_order.user_id,
                to_user_id=sys_exchange_user_id,
            )
            db.session.add(history)
            db.session.commit()

        asset = history.asset
        amount = history.amount
        remark = f"exchange_order {self.exchange_order_id} transfer_source_asset {history.id}"
        if history.status == AssetExchangeOrderTransferHistory.Status.CREATED:
            try:
                self.client.add_user_balance(
                    user_id=history.from_user_id,
                    asset=asset,
                    amount=-amount,
                    business=BalanceBusiness.EXCHANGE_ORDER_TRANSFER,
                    business_id=history.id,
                    detail={"remark": remark},
                    account_id=SPOT_ACCOUNT_ID,
                )
            except ServerClient.BadResponse as _e:
                if _e.code == ServerResponseCode.INSUFFICIENT_BALANCE:
                    # 用户余额不足，兑换订单置为失败 (没扣款)
                    history.status = AssetExchangeOrderTransferHistory.Status.FAILED
                    self.exchange_order.status = AssetExchangeOrder.Status.FAILED
                    self.exchange_order.result = AssetExchangeOrder.Result.FAILED
                    sys_user = AssetExchangeSysUser.query.filter(
                        AssetExchangeSysUser.user_id == self.exchange_order.sys_user_id).first()
                    assert sys_user and sys_user.status == AssetExchangeSysUser.Status.UNUSABLE
                    sys_user.status = AssetExchangeSysUser.Status.USABLE
                    db.session.commit()
                    send_exchange_order_notice(self.exchange_order)
                    return False
                raise
            history.status = AssetExchangeOrderTransferHistory.Status.DEDUCTED
            history.deducted_at = now()
            db.session.commit()

        if history.status == AssetExchangeOrderTransferHistory.Status.DEDUCTED:
            self.client.add_user_balance(
                user_id=history.to_user_id,
                asset=asset,
                amount=amount,
                business=BalanceBusiness.EXCHANGE_ORDER_TRANSFER,
                business_id=history.id,
                detail={"remark": remark},
                account_id=SPOT_ACCOUNT_ID,
            )
            history.status = AssetExchangeOrderTransferHistory.Status.FINISHED
            history.finished_at = now()
            self.exchange_order.status = AssetExchangeOrder.Status.EXCHANGING
            db.session.commit()
            return True
        return False

    def transfer_target_asset(self) -> bool:
        # 划转目标币种
        history = AssetExchangeOrderTransferHistory.query.filter(
            AssetExchangeOrderTransferHistory.exchange_order_id == self.exchange_order_id,
            AssetExchangeOrderTransferHistory.type == AssetExchangeOrderTransferHistory.Type.TRANSFER_TARGET_ASSET,
        ).first()
        assert history
        fee_row: AssetExchangeOrderFee = AssetExchangeOrderFee.query.filter(
            AssetExchangeOrderFee.exchange_order_id == self.exchange_order_id,
            AssetExchangeOrderFee.type == ExchangeFeeType.WEB,
        ).first()

        asset = history.asset
        amount = history.amount
        remark = f"exchange_order {self.exchange_order_id} {history.type.name} {history.id}"
        zero = Decimal("0")
        if history.status == AssetExchangeOrderTransferHistory.Status.CREATED:
            # 扣系统账号的目标币种
            if amount > zero:
                self.client.add_user_balance(
                    user_id=history.from_user_id,
                    asset=asset,
                    amount=-amount,
                    business=BalanceBusiness.EXCHANGE_ORDER_TRANSFER,
                    business_id=history.id,
                    detail={"remark": remark},
                    account_id=SPOT_ACCOUNT_ID,
                )
            history.deducted_at = now()
            history.status = AssetExchangeOrderTransferHistory.Status.DEDUCTED
            db.session.commit()

        if history.status == AssetExchangeOrderTransferHistory.Status.DEDUCTED:
            # 增加用户的目标币种、扣目标币种手续费
            if amount > zero:
                if (
                    not fee_row
                    or fee_row.asset != asset
                    or fee_row.amount == zero
                    or fee_row.status == AssetExchangeOrderFee.Status.FINISHED
                ):
                    # 没有web要收的手续费 or 手续费不是目标币种 or 手续费为0 or 手续费已经扣了, 则只增加
                    self.client.add_user_balance(
                        user_id=history.to_user_id,
                        asset=asset,
                        amount=amount,
                        business=BalanceBusiness.EXCHANGE_ORDER_TRANSFER,
                        business_id=history.id,
                        detail={"remark": remark},
                        account_id=SPOT_ACCOUNT_ID,
                    )
                else:
                    add_result = self.client.asset_query_business(
                        user_id=history.to_user_id,
                        asset=asset,
                        business=BalanceBusiness.EXCHANGE_ORDER_TRANSFER,
                        business_id=history.id,
                    )
                    if not add_result:
                        assert fee_row.asset == asset
                        self.client.batch_add_user_balance(
                            [
                                # 增加目标币种
                                dict(
                                    user_id=history.to_user_id,
                                    asset=asset,
                                    amount=amount,
                                    business=BalanceBusiness.EXCHANGE_ORDER_TRANSFER,
                                    business_id=history.id,
                                    detail={"remark": remark},
                                    account_id=SPOT_ACCOUNT_ID,
                                ),
                                # 扣目标币种的手续费
                                dict(
                                    user_id=history.to_user_id,
                                    asset=asset,
                                    amount=-fee_row.amount,
                                    business=BalanceBusiness.EXCHANGE_ORDER_TRANSFER_FEE,
                                    business_id=self.exchange_order_id,
                                    detail={"remark": f"fee for exchange_order {self.exchange_order_id}"},
                                    account_id=SPOT_ACCOUNT_ID,
                                ),
                            ]
                        )
            history.finished_at = now()
            history.status = AssetExchangeOrderTransferHistory.Status.FINISHED
            if fee_row and fee_row.status != AssetExchangeOrderFee.Status.FINISHED:
                fee_row.status = AssetExchangeOrderFee.Status.FINISHED
                fee_row.finished_at = now()
            db.session.commit()
            return True
        return False

    def transfer_remain_source_asset_if_necessary(self) -> bool:
        # 划转剩余兑换币种
        history = AssetExchangeOrderTransferHistory.query.filter(
            AssetExchangeOrderTransferHistory.exchange_order_id == self.exchange_order_id,
            AssetExchangeOrderTransferHistory.type == AssetExchangeOrderTransferHistory.Type.TRANSFER_REMAIN_SOURCE_ASSET,
        ).first()
        if not history:
            return False

        zero = Decimal("0")
        asset = history.asset
        amount = history.amount
        remark = f"exchange_order {self.exchange_order_id} {history.type.name} {history.id}"
        if history.status == AssetExchangeOrderTransferHistory.Status.CREATED:
            if amount > zero:
                self.client.add_user_balance(
                    user_id=history.from_user_id,
                    asset=asset,
                    amount=-amount,
                    business=BalanceBusiness.EXCHANGE_ORDER_TRANSFER,
                    business_id=history.id,
                    detail={"remark": remark},
                    account_id=SPOT_ACCOUNT_ID,
                )
            history.deducted_at = now()
            history.status = AssetExchangeOrderTransferHistory.Status.DEDUCTED
            db.session.commit()

        if history.status == AssetExchangeOrderTransferHistory.Status.DEDUCTED:
            if amount > zero:
                self.client.add_user_balance(
                    user_id=history.to_user_id,
                    asset=asset,
                    amount=amount,
                    business=BalanceBusiness.EXCHANGE_ORDER_TRANSFER,
                    business_id=history.id,
                    detail={"remark": remark},
                    account_id=SPOT_ACCOUNT_ID,
                )
            history.finished_at = now()
            history.status = AssetExchangeOrderTransferHistory.Status.FINISHED
            db.session.commit()
            return True
        return False

    def new_first_market_sys_order(self) -> SysAssetExchangeOrder:
        market = self.markets[0]
        first_sys_order = SysAssetExchangeOrder(
            exchange_order_id=self.exchange_order_id,
            type=SysAssetExchangeOrder.Type.EXCHANGE,
            market=market,
            source_asset=self.exchange_order.source_asset,
            source_asset_amount=self.exchange_order.source_asset_amount,
            source_asset_exchanged_amount=0,
            target_asset=get_target_asset(self.exchange_order.source_asset, market),
            target_asset_exchanged_amount=0,
            timeout_at=now() + timedelta(seconds=SYS_ORDER_TIMEOUT_SECONDS),
        )
        return first_sys_order

    def new_second_market_sys_order(self, first_sys_order: SysAssetExchangeOrder) -> SysAssetExchangeOrder:
        market2 = self.markets[1]
        second_sys_order = SysAssetExchangeOrder(
            exchange_order_id=self.exchange_order.id,
            type=SysAssetExchangeOrder.Type.EXCHANGE,
            market=market2,
            source_asset=first_sys_order.target_asset,
            source_asset_amount=first_sys_order.target_asset_exchanged_amount,
            source_asset_exchanged_amount=0,
            target_asset=get_target_asset(first_sys_order.target_asset, market2),
            target_asset_exchanged_amount=0,
            timeout_at=now() + timedelta(seconds=SYS_ORDER_TIMEOUT_SECONDS),
        )
        return second_sys_order

    def new_buy_back_sys_order(self, second_sys_order: SysAssetExchangeOrder) -> SysAssetExchangeOrder:
        market = self.markets[0]
        remain = second_sys_order.source_asset_remain_amount
        assert remain > Decimal()
        buy_back_order = SysAssetExchangeOrder(
            exchange_order_id=self.exchange_order_id,
            type=SysAssetExchangeOrder.Type.BUY_BACK,
            market=market,
            source_asset=second_sys_order.source_asset,
            source_asset_amount=remain,
            source_asset_exchanged_amount=0,
            target_asset=self.exchange_order.source_asset,
            target_asset_exchanged_amount=0,
            timeout_at=now() + timedelta(seconds=BUY_BACK_TIMEOUT_SECONDS),
        )
        return buy_back_order


class SysExchangeOrderManager:
    """ 处理 系统兑换订单 """

    AUCTION_MAX_COUNT = 5  # 拍卖次数 | 价格区间等份
    DEFAULT_PRICE_DEVIATION = Decimal("0.06")  # 最低档位的目标价格幅度

    def __init__(self, sys_order_id: int, exchange_order: AssetExchangeOrder):
        self.sys_order_id = sys_order_id
        self.exchange_order = exchange_order  # get info for log

        self.sys_order: SysAssetExchangeOrder = SysAssetExchangeOrder.query.get(sys_order_id)
        self.client = ServerClient(current_app.logger)
        self.market_info = MarketCache(self.sys_order.market).dict

    @cached_property
    def side(self) -> OrderSideType:
        if self.sys_order.target_asset == self.market_info["quote_asset"]:
            return OrderSideType.SELL
        elif self.sys_order.target_asset == self.market_info["base_asset"]:
            return OrderSideType.BUY
        else:
            raise ValueError(f"un_match market {self.sys_order.market} {self.sys_order.target_asset}")

    @cached_property
    def msg_prefix(self) -> str:
        return f"用户: {self.exchange_order.user_id}, 兑换订单: {self.exchange_order.id}, 系统兑换账户: {self.exchange_order.sys_user_id},"

    def process(self):
        trade_order_manager = TradeOrderManager(
            self.sys_order_id,
            self.exchange_order.sys_user_id,
            self.exchange_order.user_id,
            self.market_info,
            self.side,
        )
        for _ in range(self.AUCTION_MAX_COUNT):
            trade_order_manager.cancel_order()

            self.update_exchanged_amount_and_status()  # session committed
            if self.sys_order.status == SysAssetExchangeOrder.Status.FINISHED:
                # 完全兑换
                return

            price, amount, order_type = self.calc_price_and_amount_and_type()
            if amount == Decimal():
                # 小于最小精度，也算完全兑换
                self.sys_order.status = SysAssetExchangeOrder.Status.FINISHED
                self.sys_order.result = SysAssetExchangeOrder.Result.ALL
                db.session.commit()
                return

            if order_type == Order.NormalOrderType.MARKET and self.sys_order.type == SysAssetExchangeOrder.Type.EXCHANGE:
                if self.side == OrderSideType.SELL:
                    _asset_precision = self.market_info["base_asset_precision"]
                else:
                    _asset_precision = self.market_info["quote_asset_precision"]
                min_o_amount = Decimal("1") / (Decimal("10") ** _asset_precision)
                if amount <= min_o_amount:
                    # 市价单数目不能小于币种精度
                    self.sys_order.status = SysAssetExchangeOrder.Status.FINISHED
                    if self.sys_order.target_asset_exchanged_amount == Decimal():
                        result = SysAssetExchangeOrder.Result.FAILED
                    else:
                        result = SysAssetExchangeOrder.Result.PARTIAL
                    self.sys_order.result = result
                    db.session.commit()
                    return

            if self.sys_order.is_timeout:
                # 超时 不再挂单
                if self.sys_order.type == SysAssetExchangeOrder.Type.EXCHANGE:
                    # 非买回订单 允许部分兑换
                    self.sys_order.status = SysAssetExchangeOrder.Status.FINISHED
                    if self.sys_order.target_asset_exchanged_amount == Decimal():
                        result = SysAssetExchangeOrder.Result.FAILED
                    else:
                        result = SysAssetExchangeOrder.Result.PARTIAL
                    self.sys_order.result = result
                    db.session.commit()

                msg = f"{self.msg_prefix} 系统兑换订单{self.sys_order.type.name}: {self.sys_order_id}, " \
                      f"市场: {self.sys_order.market} 挂单超时"
                if self.sys_order.type == SysAssetExchangeOrder.Type.EXCHANGE:
                    current_app.logger.warning(msg)
                else:
                    alert_exchange_msg(msg, ttl=600, msg_id=f"sys:timeout:{self.sys_order_id}")
                return

            # 如果挂单后立刻完全成交，则继续下一次的挂单
            is_finished = trade_order_manager.place_order(price=price, amount=amount, order_type=order_type)
            if not is_finished:
                return

    def init_auction_price(self):
        # 设置拍卖价格
        market_last_price = self.client.market_last(self.sys_order.market)
        market_exchange_info = ExchangeMarketCache().hget(self.sys_order.market)
        if not market_exchange_info:
            # 提交订单时，市场有兑换信息，但是执行兑换时没有了（深度不够最低档位了）
            msg = f"{self.msg_prefix} 系统兑换订单{self.sys_order.type.name}: {self.sys_order_id} 市场{self.sys_order.market}无兑换信息"
            current_app.logger.error(msg)
            price_deviation = self.DEFAULT_PRICE_DEVIATION
        else:
            market_exchange_info = json.loads(market_exchange_info)
            price_deviation = Decimal(market_exchange_info["price_deviation"])

        if self.side == OrderSideType.BUY:
            deviation = Decimal("1") + price_deviation
        else:
            deviation = Decimal("1") - price_deviation
        quote_asset_precision = self.market_info["quote_asset_precision"]
        min_quote_price = Decimal("1") / (Decimal("10") ** quote_asset_precision)
        auction_target_price = quantize_amount(market_last_price * deviation, quote_asset_precision)
        price_delta = abs(auction_target_price - market_last_price)
        auction_price_delta = quantize_amount(price_delta / Decimal(self.AUCTION_MAX_COUNT), quote_asset_precision)
        auction_price_delta = max(auction_price_delta, min_quote_price)  # 价格间隔小于最小价格精度时 取最小价格精度
        self.sys_order.auction_target_price = auction_target_price
        self.sys_order.auction_price_delta = auction_price_delta
        db.session.commit()
        return market_last_price

    def calc_price_and_amount_and_type(self) -> Tuple[Decimal, Decimal, Order.NormalOrderType]:
        """ 计算挂单价格、挂单数目、挂单类型 """
        from app.assets.asset import get_asset_config

        min_order_amount = get_asset_config(self.sys_order.source_asset).min_order_amount
        remain_source_amount = self.sys_order.source_asset_amount - self.sys_order.source_asset_exchanged_amount
        # 优先判断是否挂市价单
        if remain_source_amount <= min_order_amount:
            source_remain_usd = remain_source_amount * PriceManager.asset_to_usd(self.sys_order.source_asset)
            if self.sys_order.source_asset_exchanged_amount > 0 and source_remain_usd <= Decimal("0.01"):
                # 剩余市值太少 不再兑换
                return Decimal(), Decimal(), Order.NormalOrderType.MARKET

            if self.side == OrderSideType.SELL:
                precision = self.market_info["base_asset_precision"]
            else:
                precision = self.market_info["quote_asset_precision"]
            amount = quantize_amount(remain_source_amount, precision)
            return Decimal(), amount, Order.NormalOrderType.MARKET

        last_limit_order_history = SysAssetExchangeTradeOrderHistory.query.filter(
            SysAssetExchangeTradeOrderHistory.sys_exchange_order_id == self.sys_order_id,
            SysAssetExchangeTradeOrderHistory.type == Order.NormalOrderType.LIMIT.value,
        ).order_by(SysAssetExchangeTradeOrderHistory.id.desc()).first()
        if not last_limit_order_history:
            price = self.init_auction_price()
        else:
            price = last_limit_order_history.price

        if self.side == OrderSideType.BUY:
            price = price + self.sys_order.auction_price_delta
            price = min(price, self.sys_order.auction_target_price)
        else:
            price = price - self.sys_order.auction_price_delta
            price = max(price, self.sys_order.auction_target_price)
        price = quantize_amount(price, self.market_info["quote_asset_precision"])
        assert price > Decimal()

        # BTC-USDT base_asset: BTC  quote_asset: USDT， 下单数是base_asset
        if self.side == OrderSideType.BUY:
            # 买单：买入=base_asset=target 卖出=quote_asset=source
            base_amount = remain_source_amount / price
        else:
            # 卖单：卖出=base_asset=source 买入=quote_asset=target
            base_amount = remain_source_amount
        base_amount = quantize_amount(base_amount, self.market_info["base_asset_precision"])
        if base_amount < Decimal():
            # 买单花的可能更多
            base_amount = Decimal()

        return price, base_amount, Order.NormalOrderType.LIMIT

    def update_exchanged_amount_and_status(self):
        # 更新已兑换数目和状态
        sum_deal_amount_row = (
            SysAssetExchangeTradeOrderHistory.query.filter(
                SysAssetExchangeTradeOrderHistory.sys_exchange_order_id == self.sys_order_id,
                SysAssetExchangeTradeOrderHistory.status == SysAssetExchangeTradeOrderHistory.Status.FINISHED,
            )
            .with_entities(
                func.sum(SysAssetExchangeTradeOrderHistory.base_deal_amount).label("sum_base_deal_amount"),
                func.sum(SysAssetExchangeTradeOrderHistory.quote_deal_amount).label("sum_quote_deal_amount"),
                func.sum(SysAssetExchangeTradeOrderHistory.base_fee_amount).label("sum_base_fee_amount"),
                func.sum(SysAssetExchangeTradeOrderHistory.quote_fee_amount).label("sum_quote_fee_amount"),
            )
            .first()
        )
        sum_base_deal_amount = sum_deal_amount_row[0] or Decimal()
        sum_quote_deal_amount = sum_deal_amount_row[1] or Decimal()
        sum_base_fee_amount = sum_deal_amount_row[2] or Decimal()
        sum_quote_fee_amount = sum_deal_amount_row[3] or Decimal()
        if self.side == OrderSideType.SELL:
            self.sys_order.source_asset_exchanged_amount = sum_base_deal_amount
            # 手续费向上取整，获取数向下取整：会出现手续费是10^-8，获取数是0
            self.sys_order.target_asset_exchanged_amount = max(sum_quote_deal_amount - sum_quote_fee_amount, Decimal())
        else:
            self.sys_order.source_asset_exchanged_amount = sum_quote_deal_amount
            self.sys_order.target_asset_exchanged_amount = max(sum_base_deal_amount - sum_base_fee_amount, Decimal())

        if sum_base_fee_amount or sum_quote_fee_amount:
            self.sys_order.fee_type = ExchangeFeeType.SERVER
            self.sys_order.fee_asset = self.sys_order.target_asset
            self.sys_order.fee_amount = sum_base_fee_amount or sum_quote_fee_amount

        if self.sys_order.source_asset_exchanged_amount >= self.sys_order.source_asset_amount:
            self.sys_order.status = SysAssetExchangeOrder.Status.FINISHED
            self.sys_order.result = SysAssetExchangeOrder.Result.ALL

        db.session.commit()


class TradeOrderManager:
    """ 处理挂单逻辑 """

    def __init__(self, sys_order_id: int, sys_user_id: int, user_id: int, market_info: Dict, side: OrderSideType):
        self.sys_order_id = sys_order_id
        self.market_info = market_info
        self.side = side
        self.sys_user_id = sys_user_id  # 系统挂单用户id
        self.user_id = user_id  # 兑换订单的用户
        self.client = ServerClient(current_app.logger)

    @cached_property
    def is_amm_market(self) -> bool:
        return AmmMarketCache.has(self.market_info["name"])

    def cancel_order(self):
        # 撤单
        last_order_history = self.get_last_trade_order_history()
        if not last_order_history or last_order_history.status == SysAssetExchangeTradeOrderHistory.Status.FINISHED:
            return

        if last_order_history.status == SysAssetExchangeTradeOrderHistory.Status.CREATED:
            order_info = self.find_order_by_client_id(last_order_history)
            if not order_info:
                # 重新下单
                order_info = self._put_order(last_order_history)
            last_order_history.order_id = order_info["id"]
            last_order_history.status = SysAssetExchangeTradeOrderHistory.Status.PENDING
            db.session.commit()

        #
        order_info = None
        try:
            order_info = self.client.cancel_user_order(
                user_id=self.sys_user_id,
                market=last_order_history.market,
                order_id=last_order_history.order_id,
            )
        except Exception as _e:
            if getattr(_e, "code", None) != OrderException.ORDER_NOT_FOUND:
                raise
            current_app.logger.warning(
                f"order_history:{last_order_history.id} order_id:{last_order_history.order_id} is cancelled"
            )
        if not order_info:
            time.sleep(1)  # 可能刚好cancel_order时，订单完全成交了
            _, order_info = self.find_order_by_order_id(last_order_history)

        if order_info:
            self._set_order_history_fields(last_order_history, order_info)
        else:
            last_order_history.base_deal_amount = last_order_history.quote_deal_amount = Decimal()
            last_order_history.base_fee_amount = last_order_history.quote_fee_amount = Decimal()
            current_app.logger.warning(
                f"order_history:{last_order_history.id} order_id:{last_order_history.order_id} order_info empty"
            )
        last_order_history.status = SysAssetExchangeTradeOrderHistory.Status.FINISHED
        db.session.commit()

    def _set_order_history_fields(self, order_history: SysAssetExchangeTradeOrderHistory, order_info: dict):
        if self.side == OrderSideType.SELL:
            # 卖单向下取
            order_history.base_deal_amount = quantize_amount(Decimal(order_info["deal_stock"]), 8, ROUND_DOWN)
            order_history.quote_deal_amount = quantize_amount(Decimal(order_info["deal_money"]), 8, ROUND_DOWN)
        else:
            order_history.base_deal_amount = quantize_amount(Decimal(order_info["deal_stock"]), 8, ROUND_UP)
            order_history.quote_deal_amount = quantize_amount(Decimal(order_info["deal_money"]), 8, ROUND_UP)
        # 手续费向上取整
        order_history.base_fee_amount = quantize_amount(order_info["stock_fee"], 8, ROUND_UP)
        order_history.quote_fee_amount = quantize_amount(order_info["money_fee"], 8, ROUND_UP)

    def place_order(self, price: Decimal, amount: Decimal, order_type: Order.NormalOrderType) -> bool:
        """
        下单
        price: 限价单的价格，市价单无用
        amount: 限价单表示base_amount，市价单-买 表示quote_amount 市价单-卖 表示base_amount
        order_type: 市价单或限价单
        """
        zero = Decimal()
        market = self.market_info["name"]
        quote_precision = self.market_info["quote_asset_precision"]
        base_precision = self.market_info["base_asset_precision"]
        if order_type == Order.NormalOrderType.MARKET:
            price = zero
            if self.side == OrderSideType.SELL:
                quote_amount = zero
                base_amount = quantize_amount(amount, base_precision)
            else:
                base_amount = zero
                quote_amount = quantize_amount(amount, quote_precision)
        else:
            base_amount = quantize_amount(amount, base_precision)
            price = quantize_amount(price, quote_precision)
            quote_amount = zero

        order_history = SysAssetExchangeTradeOrderHistory(
            sys_exchange_order_id=self.sys_order_id,
            market=market,
            base_asset=self.market_info["base_asset"],
            base_amount=base_amount,
            quote_asset=self.market_info["quote_asset"],
            quote_amount=quote_amount,
            price=price,
            side=self.side.value,
            type=order_type.value,
        )
        db.session.add(order_history)
        db.session.commit()

        order_info = self._put_order(order_history)
        order_history.order_id = order_info["id"]
        order_history.status = SysAssetExchangeTradeOrderHistory.Status.PENDING
        db.session.commit()
        return self.check_and_set_order_finished(order_history)

    def check_and_set_order_finished(self, order_history) -> bool:
        is_finished, order_info = self.find_order_by_order_id(order_history)
        if is_finished:
            self._set_order_history_fields(order_history, order_info)
            order_history.status = SysAssetExchangeTradeOrderHistory.Status.FINISHED
            db.session.commit()
            return True
        return False

    @classmethod
    def is_put_order_failed(cls, exc: Exception) -> bool:
        """ 根据异常原因，判断是否是下单失败了 """
        if isinstance(exc, (InvalidArgument, )):
            return True
        if getattr(exc, "code", None) in [
            OrderException.PROTECT_DURATION_ORDER_MAKER_ONLY,
            OrderException.MARKET_OUT_OF_SERVICE,
            OrderException.MARKET_PROTECT_DURATION,
        ]:
            return True
        return False

    def _put_order(self, order_history: SysAssetExchangeTradeOrderHistory) -> Dict:
        # 根据本地挂单历史信息 下单
        from app.business.order import OrderFeeOption
        from app.business.fee import FeeFetcher

        market = self.market_info["name"]
        client_id = str(order_history.id)
        if self.is_amm_market:
            # CET抵扣、费率 由 兑换订单的用户 决定
            op = OrderFeeOption(self.market_info, self.user_id)
            fee_asset = op.fee_asset
            fee_discount = op.fee_discount
            option = OrderOption.WITHOUT_ORDER_MIN_AMOUNT | op.option
            fee_result = FeeFetcher(self.user_id).fetch(TradeBusinessType.SPOT, market)
            taker_fee_rate, maker_fee_rate = fee_result[TradeType.TAKER], fee_result[TradeType.MAKER]
        else:
            # 普通市场挂单交易时不收手续费
            fee_asset = None
            fee_discount = taker_fee_rate = maker_fee_rate = "0"
            option = OrderOption.WITHOUT_ORDER_MIN_AMOUNT

        if order_history.type == Order.NormalOrderType.MARKET:
            amount = order_history.base_amount if self.side == OrderSideType.SELL else order_history.quote_amount
            try:
                order_info = self.client.put_market_order(
                    user_id=self.sys_user_id,
                    account_id=SPOT_ACCOUNT_ID,
                    market=market,
                    side=self.side.value,
                    amount=amount_to_str(amount),
                    taker_fee_rate=str(taker_fee_rate),
                    source=Order.OrderSourceType.SYSTEM.value,
                    fee_asset=fee_asset,
                    fee_discount=str(fee_discount),
                    option=option,
                    client_id=client_id,
                )
            except Exception as e:
                if self.is_put_order_failed(e):
                    order_history.status = SysAssetExchangeTradeOrderHistory.Status.FINISHED
                    db.session.commit()
                raise
        else:
            try:
                order_info = self.client.put_limit_order(
                    user_id=self.sys_user_id,
                    account_id=SPOT_ACCOUNT_ID,
                    market=market,
                    side=self.side.value,
                    amount=amount_to_str(order_history.base_amount),
                    price=amount_to_str(order_history.price),
                    taker_fee_rate=str(taker_fee_rate),
                    maker_fee_rate=str(maker_fee_rate),
                    source=Order.OrderSourceType.SYSTEM.value,
                    fee_asset=fee_asset,
                    fee_discount=str(fee_discount),
                    option=option,
                    client_id=client_id,
                )
            except Exception as e:
                if self.is_put_order_failed(e):
                    order_history.status = SysAssetExchangeTradeOrderHistory.Status.FINISHED
                    db.session.commit()
                raise
        return order_info

    def get_last_trade_order_history(self) -> Optional[SysAssetExchangeTradeOrderHistory]:
        """ 获取 系统兑换订单-最新的挂单记录 """
        last_order_history = (
            SysAssetExchangeTradeOrderHistory.query.filter(
                SysAssetExchangeTradeOrderHistory.sys_exchange_order_id == self.sys_order_id,
            )
            .order_by(SysAssetExchangeTradeOrderHistory.id.desc())
            .first()
        )
        return last_order_history

    def find_order_by_client_id(self, order_history: SysAssetExchangeTradeOrderHistory) -> Optional[Dict]:
        """ 根据client_id查询对应订单 """
        # 订单排序规则: ORDER BY `create_time` DESC, `id` DESC
        start_ts = end_ts = 0
        page = 1
        target_client_id = str(order_history.id)
        while True:
            page_orders = self.client.user_finished_orders(
                user_id=self.sys_user_id,
                market=order_history.market,
                account_id=SPOT_ACCOUNT_ID,
                side=order_history.side,
                start_time=start_ts,
                end_time=end_ts,
                page=page,
                limit=100,
                stop_order_id=None,
            )
            for order_info in page_orders:
                if order_info.get("client_id") == target_client_id:
                    return order_info

            if page_orders.has_next:
                page += 1
            else:
                break

        # 当前委托单
        while True:
            page_orders = self.client.user_pending_orders(
                user_id=self.sys_user_id,
                market=order_history.market,
                account_id=SPOT_ACCOUNT_ID,
                side=order_history.side,
                page=page,
                limit=100,
            )
            for order_info in page_orders:
                if order_info.get("client_id") == target_client_id:
                    return order_info

            if page_orders.has_next:
                page += 1
            else:
                break

    def find_order_by_order_id(self, order_history: SysAssetExchangeTradeOrderHistory) -> Tuple[bool, Optional[Dict]]:
        """ 根据order_id查询对应订单, return is_finished, order_info """
        # 未成交的 且 被取消的订单 无法查询到
        order_info = self.client.finished_order_detail(
            user_id=self.sys_user_id,
            order_id=order_history.order_id,
        )
        if order_info:
            return True, order_info

        pending_order = self.client.pending_order_detail(
            market=order_history.market,
            order_id=order_history.order_id,
        )
        if pending_order and pending_order.get("finished"):
            # 订单完全成交后的几分钟内 order.pending_detail是可以查到的，有个特殊的finished标志
            return True, pending_order
        return False, pending_order


def retry_exchanging_exchange_order_by_market(base_asset: str, quote_asset: str, market: str) -> int:
    """ 用于市场状态异常时（无法下单、撤单等），超过兑换订单的处理时间。恢复后调用，重新执行兑换任务 """
    orders: list[AssetExchangeOrder] = AssetExchangeOrder.query.filter(
        or_(
            AssetExchangeOrder.source_asset.in_([base_asset, quote_asset]),
            AssetExchangeOrder.target_asset.in_([base_asset, quote_asset]),
        ),
        AssetExchangeOrder.status == AssetExchangeOrder.Status.EXCHANGING,
    ).with_entities(
        AssetExchangeOrder.id,
        AssetExchangeOrder.exchange_path,
    ).all()
    retry_num = 0
    for o in orders:
        exchange_path = json.loads(o.exchange_path)
        if market in exchange_path:
            retry_num += 1
            process_exchange_order_task.delay(o.id)
    return retry_num


@celery_task
def process_exchange_order_task(exchange_order_id: int):
    """ 处理兑换订单 """
    if not SiteSettings.exchange_order_enabled:
        return

    manager = ExchangeOrderManager(exchange_order_id)
    manager.process()
