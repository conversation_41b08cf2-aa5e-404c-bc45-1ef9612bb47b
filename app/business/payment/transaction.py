# -*- coding: utf-8 -*-
import json
from decimal import Decimal
from datetime import datetime
from typing import Optional
from functools import cached_property

from flask import current_app
from flask_babel import gettext
from sqlalchemy import func

from app.common import PrecisionEnum
from app.models import User, SignOffUser
from app.models.payment import (
    db, PaymentTransaction, PaymentTransferHistory, PaymentQrcode,
)
from app.business import (
    SPOT_ACCOUNT_ID, ServerClient, BalanceBusiness, ServerResponseCode, SiteSettings, BusinessSettings,
    PriceManager, UserSettings, UserPreferences, LockKeys, CacheLock, DummyCacheLock
)
from app.business.payment.hedging import PaymentAssetHedger, process_payment_hedging_his_task
from app.business.payment.quote import PaymentQuoteHelper
from app.business.payment.notice import transaction_finished_notice_task
from app.caches.user import UserVisitPermissionCache
from app.caches.payment import UserDailyPaymentUsdCache, PaymentQuoteMarketCache
from app.exceptions import InvalidArgument, InsufficientBalance
from app.utils import new_hex_token, quantize_amount, now


PAY_TRANSFER_TYPE_BUSINESS_MAP = {
    # 划转记录类型: 账户流水类型
    PaymentTransferHistory.Type.TRANSFER_PAY_ASSET: BalanceBusiness.PAYMENT_PAY,
    PaymentTransferHistory.Type.TRANSFER_RECEIVE_ASSET: BalanceBusiness.PAYMENT_RECEIVE,
}


class UserPaymentLimiter:
    """用户付款额度相关逻辑"""

    WIDE_LIMIT_RATIO = Decimal('1.01')  # 实际付款额度允许超出一点

    def __init__(self, user: User):
        self.user: User = user
        self.user_id = user.id

    @cached_property
    def user_pref(self) -> UserPreferences:
        return UserPreferences(self.user_id)

    @cached_property
    def daily_system_limit(self) -> Decimal:
        """系统单账号每日付款限额"""
        return BusinessSettings.user_daily_payment_limit

    @cached_property
    def daily_custom_limit(self) -> Decimal:
        """用户手动设置的每日付款限额"""
        return self.user_pref.daily_payment_limit or BusinessSettings.user_daily_payment_default_limit

    def get_withdrawal_limit_info(self) -> tuple[Decimal, Decimal]:
        """获取用户提现限额"""
        from app.business.wallet import get_user_withdrawal_limit_info

        withdrawal_limit_info = get_user_withdrawal_limit_info(self.user)
        withdrawal_limit_usd = withdrawal_limit_info['withdrawal_limit_usd']
        withdrawn_usd = withdrawal_limit_info['withdrawn_usd']
        if withdrawal_limit_usd_30_days := withdrawal_limit_info.get('withdrawal_limit_usd_30_days'):
            withdrawal_limit_usd = min(withdrawal_limit_usd, withdrawal_limit_usd_30_days)
        return withdrawal_limit_usd, withdrawn_usd

    def get_daily_limit_usd(self) -> Decimal:
        """获取用户每日付款限额（取最小值）"""
        limit_usd_list = [
            self.daily_system_limit,
            self.daily_custom_limit,
            self.get_withdrawal_limit_info()[0]
        ]
        return min(limit_usd_list)

    def get_daily_used_usd(self) -> Decimal:
        """获取用户今日已使用的付款额度"""
        return UserDailyPaymentUsdCache(self.user_id).get_usd()

    def get_daily_remain_usd(self) -> Decimal:
        """获取用户今日剩余付款额度"""
        return max(self.get_daily_limit_usd() - self.get_daily_used_usd(), Decimal())

    def can_pay_amount(self, pay_asset: str, pay_amount: Decimal) -> tuple[bool, str]:
        """检查用户是否可以支付指定金额"""
        from app.business.wallet import user_withdrawal_amount_exceeded

        to_pay_usd = PriceManager.asset_to_usd(pay_asset) * pay_amount
        remain_usd = self.get_daily_remain_usd()
        if to_pay_usd > remain_usd * self.WIDE_LIMIT_RATIO:
            return False, ''

        exceeded, from_daily_limit = user_withdrawal_amount_exceeded(self.user, to_pay_usd)
        if exceeded:
            err_msg = '' if from_daily_limit else gettext('暂无法付款，近30天可提现额度不足')
            return False, err_msg

        return True, ''

    def check_pay_amount(self, pay_asset: str, pay_amount: Decimal) -> None:
        """检查用户是否可以支付指定金额"""
        _is_can_pay, _err_msg = self.can_pay_amount(pay_asset, pay_amount)
        if not _is_can_pay:
            _err_msg = _err_msg or gettext('超出每日付款限额')
            raise InvalidArgument(message=_err_msg)

    def set_daily_custom_limit(self, new_custom_limit_usd: Decimal) -> None:
        """设置用户每日付款限额"""
        system_limit = self.daily_system_limit
        if not (0 <= new_custom_limit_usd <= system_limit):
            raise InvalidArgument(message=gettext('每日最高可付%(limit)s USD', limit=system_limit))

        self.user_pref.daily_payment_limit = new_custom_limit_usd

    def add_pay_amount(self, asset: str, amount: Decimal) -> None:
        """添加付款金额到缓存"""
        from app.business.wallet import add_withdrawal_amount_to_cache

        UserDailyPaymentUsdCache(self.user_id).add_amount(asset, amount)
        add_withdrawal_amount_to_cache(self.user_id, asset, amount)

    @classmethod
    def decrease_user_pay_amount(cls, user_id: int, asset: str, amount: Decimal, at: datetime) -> None:
        """付款失败时：减少付款金额到缓存"""
        from app.business.wallet import add_withdrawal_amount_to_cache

        UserDailyPaymentUsdCache(user_id).add_amount(asset, -amount, at)
        add_withdrawal_amount_to_cache(user_id, asset, -amount, at)


class PaymentTransactionHelper:
    """付款相关逻辑"""

    MAX_PAY_RECEIVE_DELTA_RATIO = Decimal('0.1')  # 付款、收款市值最多相差xx%
    MIN_PAY_RECEIVE_RATIO = Decimal(1) - MAX_PAY_RECEIVE_DELTA_RATIO
    MAX_PAY_RECEIVE_RATIO = Decimal(1) + MAX_PAY_RECEIVE_DELTA_RATIO

    @classmethod
    def check_payment_params(
        cls,
        pay_asset: str,
        pay_amount: Decimal,
        receive_asset: str,
        receive_amount: Decimal,
    ):
        from app.assets import get_asset_config

        if quantize_amount(pay_amount, PrecisionEnum.COIN_PLACES) != pay_amount:
            raise InvalidArgument
        if quantize_amount(receive_amount, PrecisionEnum.COIN_PLACES) != receive_amount:
            raise InvalidArgument
        if pay_amount <= Decimal() or receive_amount <= Decimal():
            raise InvalidArgument

        # 功能开关检查
        if not SiteSettings.local_transfers_enabled:
            raise InvalidArgument(message=gettext('不可付款'))
        for asset_ in {pay_asset, receive_asset}:
            if not get_asset_config(asset_).local_transfers_enabled:
                raise InvalidArgument(message=gettext('不可付款'))

        if pay_asset == receive_asset:
            # 同币种付款检查
            if pay_amount != receive_amount:
                # 币种相同，则数目需要相同
                raise InvalidArgument
            if not SiteSettings.same_asset_payment_enabled:
                raise InvalidArgument(message=gettext('不可付款'))
        else:
            # 跨币种付款检查
            if not SiteSettings.diff_asset_payment_enabled:
                raise InvalidArgument(message=gettext('不可付款'))

        return True

    @classmethod
    def check_payer_user_status(cls, payer_user: User):
        from app.business.red_packet.send import user_has_withdrawal_approver
        from app.business.risk_control import withdrawals_disabled_by_risk_control

        payer_id = payer_user.id
        if user_has_withdrawal_approver(payer_id):
            raise InvalidArgument(message=gettext('已开启提现多人审核的账号，暂无法使用收付款功能'))

        # like WithdrawalHelper.validate_user_permission(user)
        user_settings = UserSettings(payer_id)
        if user_settings.withdrawals_disabled_after_security_editing:
            raise InvalidArgument(message=gettext('修改安全工具后24小时无法付款。'))
        if user_settings.withdrawals_disabled_after_withdraw_password_editing:
            raise InvalidArgument(message=gettext('修改提现密码后24小时无法付款。'))
        if not user_settings.withdrawals_enabled:
            raise InvalidArgument(message=gettext('暂无法付款，如需更多帮助请提交工单咨询。'))
        if withdrawals_disabled_by_risk_control(payer_id):
            raise InvalidArgument(message=gettext('暂无法付款，如需更多帮助请提交工单咨询。'))

    @classmethod
    def check_receiver_user_status(cls, receiver_id: int):
        if UserVisitPermissionCache().check_user_permission(
            receiver_id,
            [UserVisitPermissionCache.FORBIDDEN_VALUE, UserVisitPermissionCache.ONLY_WITHDRAWAL_VALUE]
        ):
            raise InvalidArgument(message=gettext('该账号已失效，不支持付款'))
        if SignOffUser.is_signoff_user(receiver_id):
            raise InvalidArgument(message=gettext('该账户已失效，不支持付款'))

    @classmethod
    def check_user_balance(cls, user_id: int, asset: str, amount: Decimal):
        balances = ServerClient().get_user_balances(user_id, asset=asset)
        available = balances.get(asset, {}).get('available', Decimal())
        if amount > available:
            raise InsufficientBalance

    @classmethod
    def check_pending_hedging_count(cls, pay_asset: str, receive_asset: str):
        market_paths = PaymentQuoteHelper.build_market_paths_by_assets(pay_asset, receive_asset, PaymentQuoteHelper.get_online_markets())
        all_quote_markets = set()
        for path in market_paths:
            all_quote_markets.update(path)
        if not all_quote_markets:
            raise InvalidArgument(message=gettext("暂不支持兑换"))
        market_quote_info_list = PaymentQuoteMarketCache().hmget(list(all_quote_markets))
        max_pending_nums = [int(json.loads(i)["max_pending_num"]) for i in market_quote_info_list]
        if not max_pending_nums:
            raise InvalidArgument(message=gettext("暂不支持兑换"))

        max_pending_num = min(max_pending_nums)
        pending_hedging_count = PaymentAssetHedger.get_pending_hedging_his_count(pay_asset, receive_asset)
        if pending_hedging_count > max_pending_num:
            raise InvalidArgument(message=gettext("当前市场波动较大，请稍后再试。"))

    @classmethod
    def new_payer_deduct_transfer(cls, trac: PaymentTransaction) -> PaymentTransferHistory:
        trans = PaymentTransferHistory(
            transaction_id=trac.id,
            user_id=trac.payer_id,
            type=PaymentTransferHistory.Type.TRANSFER_PAY_ASSET,
            asset=trac.pay_asset,
            amount=trac.pay_amount,
        )
        db.session.add(trans)
        return trans

    @classmethod
    def new_receiver_add_transfer(cls, trac: PaymentTransaction) -> PaymentTransferHistory:
        trans = PaymentTransferHistory(
            transaction_id=trac.id,
            user_id=trac.receiver_id,
            type=PaymentTransferHistory.Type.TRANSFER_RECEIVE_ASSET,
            asset=trac.receive_asset,
            amount=trac.receive_amount,
        )
        db.session.add(trans)
        return trans

    @classmethod
    def _execute_deduct_transfer(cls, row: PaymentTransferHistory, on_finished_commit: bool):
        """ 执行单条划转（只扣减余额）"""
        client = ServerClient()
        if row.status == PaymentTransferHistory.Status.CREATED:
            business = PAY_TRANSFER_TYPE_BUSINESS_MAP[row.type]
            try:
                result = client.add_user_balance(
                    user_id=row.user_id,
                    asset=row.asset,
                    amount=str(-row.amount),
                    business=business,
                    business_id=row.id,
                    detail={"remark": f"payment {row.transaction_id} for transfer {row.id}"},
                    account_id=SPOT_ACCOUNT_ID,
                )
                if not result:
                    current_app.logger.error(
                        f"payment_do_only_deduct_transfer {row.transaction_id} {row.id} {row.type.name} "
                        f"{row.user_id} {row.asset} {row.amount} deduct DUPLICATE_BALANCE_UPDATE"
                    )
            except Exception as e:
                current_app.logger.error(
                    f"payment_do_only_deduct_transfer {row.transaction_id} {row.id} {row.type.name} "
                    f"{row.user_id} {row.asset} {row.amount} failed {e!r}"
                )
                if getattr(e, 'code', None) == ServerResponseCode.INSUFFICIENT_BALANCE:
                    row.status = PaymentTransferHistory.Status.FAILED
                    db.session.commit()
                    return
                raise
            row.status = PaymentTransferHistory.Status.FINISHED
            row.deducted_at = row.finished_at = now()
            if on_finished_commit:
                db.session.commit()

    @classmethod
    def _execute_add_transfer(cls, row: PaymentTransferHistory, on_finished_commit: bool):
        """ 执行单条划转（只增加余额）"""
        client = ServerClient()
        if row.status == PaymentTransferHistory.Status.CREATED:
            business = PAY_TRANSFER_TYPE_BUSINESS_MAP[row.type]
            try:
                result = client.add_user_balance(
                    user_id=row.user_id,
                    asset=row.asset,
                    amount=str(row.amount),
                    business=business,
                    business_id=row.id,
                    detail={"remark": f"payment {row.transaction_id} for transfer {row.id}"},
                    account_id=SPOT_ACCOUNT_ID,
                )
                if not result:
                    current_app.logger.error(
                        f"payment_do_only_add_transfer {row.transaction_id} {row.id} {row.type.name} "
                        f"{row.user_id} {row.asset} {row.amount} add DUPLICATE_BALANCE_UPDATE"
                    )
            except Exception as e:
                current_app.logger.error(
                    f"payment_do_only_add_transfer {row.transaction_id} {row.id} {row.type.name} "
                    f"{row.user_id} {row.asset} {row.amount} failed {e!r}"
                )
                raise
            row.status = PaymentTransferHistory.Status.FINISHED
            row.finished_at = now()
            if on_finished_commit:
                db.session.commit()

    @classmethod
    def do_transfer_by_his(cls, row: PaymentTransferHistory, on_finished_commit: bool):
        type_enum = PaymentTransferHistory.Type
        if row.type == type_enum.TRANSFER_PAY_ASSET:
            return cls._execute_deduct_transfer(row, on_finished_commit)
        elif row.type == type_enum.TRANSFER_RECEIVE_ASSET:
            return cls._execute_add_transfer(row, on_finished_commit)
        else:
            raise ValueError(f"not support {row.type.name} transfer")

    @classmethod
    def do_pay(
        cls,
        payer_user: User,
        pay_asset: str,
        pay_amount: Decimal,  # 包括了pay_fee_amount
        pay_fee_amount: Decimal,
        receiver_id: int,
        receive_asset: str,
        receive_amount: Decimal,
        remark: str,
    ) -> PaymentTransaction:
        payer_id = payer_user.id
        cls.check_payment_params(pay_asset, pay_amount, receive_asset, receive_amount)
        cls.check_payer_user_status(payer_user)
        cls.check_receiver_user_status(receiver_id)
        cls.check_user_balance(payer_id, pay_asset, pay_amount)
        
        payment_limiter = UserPaymentLimiter(payer_user)
        payment_limiter.check_pay_amount(pay_asset, pay_amount)

        if pay_asset != receive_asset:
            # 跨币种付款，限制 最大同时兑换数
            _lock_obj1 = CacheLock(LockKeys.payment_asset_trac(pay_asset))
            _lock_obj2 = CacheLock(LockKeys.payment_asset_trac(receive_asset))
        else:
            _lock_obj1 = DummyCacheLock()
            _lock_obj2 = DummyCacheLock()

        with _lock_obj1, _lock_obj2:
            db.session.rollback()

            if pay_asset != receive_asset:
                cls.check_pending_hedging_count(pay_asset, receive_asset)

            trac = cls.new_pay_transaction(
                payer_id=payer_id,
                pay_asset=pay_asset,
                pay_amount=pay_amount,
                pay_fee_amount=pay_fee_amount,
                receiver_id=receiver_id,
                receive_asset=receive_asset,
                receive_amount=receive_amount,
                remark=remark,
            )

            payment_limiter.add_pay_amount(pay_asset, pay_amount)

            cls.process_transaction(trac)

        return trac

    @classmethod
    def new_pay_transaction(
        cls,
        payer_id: int,
        pay_asset: str,
        pay_amount: Decimal,
        pay_fee_amount: Decimal,
        receiver_id: int,
        receive_asset: str,
        receive_amount: Decimal,
        remark: str,
    ) -> PaymentTransaction:
        assert pay_amount > Decimal() and pay_fee_amount >= Decimal() and receive_amount > Decimal()

        trac = PaymentTransaction(
            payer_id=payer_id,
            pay_asset=pay_asset,
            pay_amount=pay_amount,
            pay_fee_amount=pay_fee_amount,
            receiver_id=receiver_id,
            receive_asset=receive_asset,
            receive_amount=receive_amount,
            type=PaymentTransaction.Type.NORMAL,
            remark=remark,
        )
        db.session.add(trac)
        db.session.flush()
        cls.new_payer_deduct_transfer(trac)
        db.session.commit()

        return trac

    @classmethod
    def process_transaction(cls, trac: PaymentTransaction):
        if trac.status == PaymentTransaction.Status.CREATED:
            cls._handle_created_trac(trac)
        if trac.status == PaymentTransaction.Status.PROCESSING:
            cls._handle_processing_trac(trac)

    @classmethod
    def retry_process_transaction(cls, trac: PaymentTransaction):
        if trac.status == PaymentTransaction.Status.CREATED:
            payer_deduct_trans: PaymentTransferHistory = PaymentTransferHistory.query.filter(
                PaymentTransferHistory.transaction_id == trac.id,
                PaymentTransferHistory.type == PaymentTransferHistory.Type.TRANSFER_PAY_ASSET,
            ).first()
            if payer_deduct_trans.status == PaymentTransferHistory.Status.CREATED:
                result = ServerClient().asset_query_business(
                    user_id=payer_deduct_trans.user_id,
                    asset=payer_deduct_trans.asset,
                    business=PAY_TRANSFER_TYPE_BUSINESS_MAP[payer_deduct_trans.type],
                    business_id=payer_deduct_trans.id,
                )
                if not result:
                    payer_deduct_trans.status = PaymentTransferHistory.Status.FAILED
                    trac.status = PaymentTransaction.Status.FAILED
                    db.session.commit()
                    UserPaymentLimiter.decrease_user_pay_amount(trac.payer_id, trac.pay_asset, trac.pay_amount, trac.created_at)
                    return
        cls.process_transaction(trac)

    @classmethod
    def _handle_created_trac(cls, trac: PaymentTransaction):
        assert trac.status == PaymentTransaction.Status.CREATED
        payer_deduct_trans: PaymentTransferHistory = PaymentTransferHistory.query.filter(
            PaymentTransferHistory.transaction_id == trac.id,
            PaymentTransferHistory.type == PaymentTransferHistory.Type.TRANSFER_PAY_ASSET,
        ).first()
        assert payer_deduct_trans
        cls.do_transfer_by_his(payer_deduct_trans, on_finished_commit=False)
        if payer_deduct_trans.status == PaymentTransferHistory.Status.FAILED:
            # 扣款失败
            trac.status = PaymentTransaction.Status.FAILED
            db.session.commit()
            UserPaymentLimiter.decrease_user_pay_amount(trac.payer_id, trac.pay_asset, trac.pay_amount, trac.created_at)
            raise InsufficientBalance
        elif payer_deduct_trans.status == PaymentTransferHistory.Status.FINISHED:
            # maybe repeat
            trac.status = PaymentTransaction.Status.PROCESSING
            cls.new_receiver_add_transfer(trac)
            hedging_his = None
            if trac.is_diff_asset:
                hedging_his = PaymentAssetHedger.create_hedging_history(trac)
            db.session.flush()
            db.session.commit()
            if hedging_his:
                process_payment_hedging_his_task.delay(hedging_his.id)

    @classmethod
    def _handle_processing_trac(cls, trac: PaymentTransaction):
        assert trac.status == PaymentTransaction.Status.PROCESSING
        receiver_add_trans: PaymentTransferHistory = PaymentTransferHistory.query.filter(
            PaymentTransferHistory.transaction_id == trac.id,
            PaymentTransferHistory.type == PaymentTransferHistory.Type.TRANSFER_RECEIVE_ASSET,
        ).first()
        assert receiver_add_trans
        cls.do_transfer_by_his(receiver_add_trans, on_finished_commit=False)
        if receiver_add_trans.status == PaymentTransferHistory.Status.FINISHED:
            trac.finished_at = now()
            trac.status = PaymentTransaction.Status.FINISHED
            db.session.commit()
            transaction_finished_notice_task.delay(trac.id)


class PaymentQrcodeHelper:
    """付款二维码相关逻辑"""

    USER_ASSET_MAX_QRCODE_LIMIT = 1000  # 每个用户单个币种的最大数目

    @classmethod
    def exceeds_user_asset_limit(cls, user_id: int, asset: str) -> bool:
        """检查用户单个币种的二维码数量限制"""
        count = PaymentQrcode.query.filter(
            PaymentQrcode.user_id == user_id,
            PaymentQrcode.asset == asset,
            PaymentQrcode.status == PaymentQrcode.Status.VALID,
        ).with_entities(
            func.count(PaymentQrcode.id),
        ).scalar() or 0
        return count > cls.USER_ASSET_MAX_QRCODE_LIMIT

    @classmethod
    def gen_new_code(cls, size: int = 32) -> str:
        for _ in range(10):
            code = new_hex_token(size)
            if not PaymentQrcode.query.filter(PaymentQrcode.code == code).with_entities(PaymentQrcode.code).first():
                return code
        raise RuntimeError

    @classmethod
    def get_or_create_qrcode(cls, user_id: int, asset: str, amount: Optional[Decimal], remark: Optional[str]) -> PaymentQrcode:
        row: PaymentQrcode = PaymentQrcode.get_or_create(
            user_id=user_id,
            asset=asset,
            status=PaymentQrcode.Status.VALID,
            amount=amount,
            remark=remark,
        )
        if row.id:
            return row
        if cls.exceeds_user_asset_limit(user_id, asset):
            raise InvalidArgument(message=gettext("超出最大上限"))

        code = cls.gen_new_code()
        row.code = code
        db.session.add(row)
        db.session.commit()
        return row

    @classmethod
    def get_qrcode_info(cls, code: str) -> dict:
        row: PaymentQrcode = PaymentQrcode.query.filter(
            PaymentQrcode.code == code,
            PaymentQrcode.status == PaymentQrcode.Status.VALID,
        ).first()
        if not row:
            return {}
        return {
            "receiver_id": row.user_id,
            "receive_asset": row.asset,
            "receive_amount": row.amount,
            "remark": row.remark,
        }
