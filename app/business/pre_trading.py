# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_UP

from flask import current_app
from flask_babel import gettext

from app.config import config
from app.common import PrecisionEnum, ServerBalanceType, MessageTitle, MessageContent, MessageWebLink
from app.models import Message, Market, AmmMarket, BalanceUpdateBusiness
from app.models.system import MarketMaintain
from app.models.pre_trading import (
    db, PreTradingAssetConfig, PreTradingUserAsset, PreTradingUserOpHistory, PreTradingUserAssetHistory,
    UserSettlementHistory,
)
from app.exceptions import InsufficientBalance, InvalidArgument
from app.business import (
    <PERSON><PERSON><PERSON>, LockKeys, CacheLock, BalanceBusiness, SPOT_ACCOUNT_ID, TradeLogDB, ServerResponseCode,
    UserPreferences,
)
from app.business.email import send_pre_trading_notice_email
from app.utils import quantize_amount, now, datetime_to_str, amount_to_str, ConfigMode


OP_TYPE_BUS_TYPE_MAP = {
    PreTradingUserOpHistory.Type.ISSUE: BalanceBusiness.PRE_TRADING_ISSUE,
    PreTradingUserOpHistory.Type.REDEMPTION: BalanceBusiness.PRE_TRADING_REDEMPTION,
    PreTradingUserOpHistory.Type.POSITION_SETTLEMENT: BalanceBusiness.PRE_TRADING_POS_SETTLE,
    PreTradingUserOpHistory.Type.ISSUE_SETTLEMENT: BalanceBusiness.PRE_TRADING_ISSUE_SETTLE,
}


def get_pre_asset_config(asset: str) -> PreTradingAssetConfig:
    pre_asset_cfg: PreTradingAssetConfig = PreTradingAssetConfig.query.filter(
        PreTradingAssetConfig.asset == asset,
    ).first()
    if not pre_asset_cfg:
        raise ValueError(f"pre_asset_config {asset} not exist")
    return pre_asset_cfg


def new_asset_his_by_op_his(op_his: PreTradingUserOpHistory, sys_user_id: int) -> list[PreTradingUserAssetHistory]:
    if op_his.op_type == op_his.Type.ISSUE:
        # 发行token
        # 1. 扣用户现货的质押币
        # 2. 给系统用户现货加质押币
        # 3. 给用户现货加发行币
        asset_his1 = PreTradingUserAssetHistory(
            op_id=op_his.id,
            user_id=op_his.user_id,
            asset=op_his.pledge_asset,
            amount=-op_his.pledge_asset_amount,
            op_type=op_his.op_type,
            status=PreTradingUserAssetHistory.Status.CREATED,
        )
        asset_his2 = PreTradingUserAssetHistory(
            op_id=op_his.id,
            user_id=sys_user_id,
            asset=op_his.pledge_asset,
            amount=op_his.pledge_asset_amount,
            op_type=op_his.op_type,
            status=PreTradingUserAssetHistory.Status.CREATED,
        )
        asset_his3 = PreTradingUserAssetHistory(
            op_id=op_his.id,
            user_id=op_his.user_id,
            asset=op_his.asset,
            amount=op_his.asset_amount,
            op_type=op_his.op_type,
            status=PreTradingUserAssetHistory.Status.CREATED,
        )
        return [asset_his1, asset_his2, asset_his3]
    elif op_his.op_type in [op_his.Type.REDEMPTION, op_his.Type.POSITION_SETTLEMENT]:
        # 赎回质押币、持仓交割
        # 1. 扣用户现货的发行币
        # 2. 扣系统用户现货的质押币，需要减去手续费（这部分留在系统用户现货里）
        # 3. 给用户现货加质押币，需要减去手续费（这部分留在系统用户现货里）
        asset_his1 = PreTradingUserAssetHistory(
            op_id=op_his.id,
            user_id=op_his.user_id,
            asset=op_his.asset,
            amount=-op_his.asset_amount,
            op_type=op_his.op_type,
            status=PreTradingUserAssetHistory.Status.CREATED,
        )
        asset_his2 = PreTradingUserAssetHistory(
            op_id=op_his.id,
            user_id=sys_user_id,
            asset=op_his.pledge_asset,
            amount=-(op_his.pledge_asset_amount - op_his.fee_amount),
            op_type=op_his.op_type,
            status=PreTradingUserAssetHistory.Status.CREATED,
        )
        asset_his3 = PreTradingUserAssetHistory(
            op_id=op_his.id,
            user_id=op_his.user_id,
            asset=op_his.pledge_asset,
            amount=op_his.pledge_asset_amount - op_his.fee_amount,
            op_type=op_his.op_type,
            status=PreTradingUserAssetHistory.Status.CREATED,
        )
        return [asset_his1, asset_his2, asset_his3]
    elif op_his.op_type == op_his.Type.ISSUE_SETTLEMENT:
        # 发行交割
        # 1. 减少用户token的发行数（修改UserAsset）
        # 2. 扣系统用户现货的质押币，需要减去手续费（这部分留在系统用户现货里）
        # 3. 给用户现货加质押币，需要减去手续费（这部分留在系统用户现货里）
        asset_his1 = PreTradingUserAssetHistory(
            op_id=op_his.id,
            user_id=sys_user_id,
            asset=op_his.pledge_asset,
            amount=-(op_his.pledge_asset_amount - op_his.fee_amount),
            op_type=op_his.op_type,
            status=PreTradingUserAssetHistory.Status.CREATED,
        )
        asset_his2 = PreTradingUserAssetHistory(
            op_id=op_his.id,
            user_id=op_his.user_id,
            asset=op_his.pledge_asset,
            amount=op_his.pledge_asset_amount - op_his.fee_amount,
            op_type=op_his.op_type,
            status=PreTradingUserAssetHistory.Status.CREATED,
        )
        return [asset_his1, asset_his2]
    else:
        raise ValueError(f"unknown op_type {op_his.op_type.name}")


def do_transfer_by_asset_his(asset_his_rows: list[PreTradingUserAssetHistory]):
    client = ServerClient()
    for asset_his in asset_his_rows:
        if asset_his.amount == Decimal():
            continue
        bus = OP_TYPE_BUS_TYPE_MAP[asset_his.op_type]
        client.add_user_balance(
            user_id=asset_his.user_id,
            asset=asset_his.asset,
            amount=asset_his.amount,
            business=bus,
            business_id=asset_his.id,
            detail={"remark": f"pre_trading_op:{asset_his.op_id}"},
            account_id=SPOT_ACCOUNT_ID,
        )


def do_issue(user_id: int, asset: str, pledge_amount: Decimal) -> PreTradingUserOpHistory:

    with CacheLock(LockKeys.pre_trading(user_id, asset)):
        db.session.rollback()

        pre_asset_cfg = get_pre_asset_config(asset)
        if pre_asset_cfg.status != PreTradingAssetConfig.Status.AUDITED:
            raise InvalidArgument

        issue_amount = pre_asset_cfg.calc_issue_amount(pledge_amount)
        if issue_amount < pre_asset_cfg.min_issue_amount:
            raise InvalidArgument

        pledge_asset = pre_asset_cfg.pledge_asset
        balance = ServerClient().get_user_balances(user_id, asset=pledge_asset)
        balance_pledge = balance.get(pledge_asset, {}).get("available", Decimal())
        if pledge_amount > balance_pledge:
            raise InsufficientBalance

        user_asset: PreTradingUserAsset = PreTradingUserAsset.get_or_create(user_id=user_id, asset=asset)
        assert user_asset.status != PreTradingUserAsset.Status.FINISHED

        if not user_asset.id:
            # 首次发行
            user_asset.pledge_asset = pledge_asset
            user_asset.issue_amount = 0
            user_asset.pledge_amount = 0
            user_asset.status = PreTradingUserAsset.Status.PLEDGE

        op_his = PreTradingUserOpHistory(
            user_id=user_id,
            asset=asset,
            pledge_asset=pledge_asset,
            asset_amount=issue_amount,
            pledge_asset_amount=pledge_amount,
            fee_amount=0,
            op_type=PreTradingUserOpHistory.Type.ISSUE,
            status=PreTradingUserOpHistory.Status.CREATED,
        )
        db.session.add(user_asset)
        db.session.add(op_his)
        db.session.flush()
        asset_his_rows = new_asset_his_by_op_his(op_his, pre_asset_cfg.sys_user_id)
        db.session.add_all(asset_his_rows)
        db.session.commit()

        try:
            do_transfer_by_asset_his(asset_his_rows)
        except ServerClient.BadResponse as _e:
            if _e.code == ServerResponseCode.INSUFFICIENT_BALANCE:
                # 一定是用户的质押币的余额不足，直接改成失败
                for asset_his in asset_his_rows:
                    asset_his.status = PreTradingUserAssetHistory.Status.FAILED
                op_his.status = PreTradingUserOpHistory.Status.FAILED
                db.session.commit()
            raise

        for asset_his in asset_his_rows:
            asset_his.status = PreTradingUserAssetHistory.Status.FINISHED
        op_his.status = PreTradingUserOpHistory.Status.FINISHED
        op_his.finished_at = now()
        user_asset.issue_amount += op_his.asset_amount
        user_asset.pledge_amount += op_his.pledge_asset_amount
        if user_asset.finish_type is not None:
            user_asset.finish_type = None
        if user_asset.finished_at is not None:
            user_asset.finished_at = None
        db.session.commit()
    return op_his


def do_redeem(user_id: int, asset: str, redeem_amount: Decimal) -> PreTradingUserOpHistory:
    with CacheLock(LockKeys.pre_trading(user_id, asset)):
        db.session.rollback()

        pre_asset_cfg = get_pre_asset_config(asset)
        if pre_asset_cfg.status != PreTradingAssetConfig.Status.AUDITED:
            raise InvalidArgument

        client = ServerClient()
        user_balance = client.get_user_balances(user_id, asset=asset)
        user_balance_amount = user_balance.get(asset, {}).get("available", Decimal())
        if redeem_amount > user_balance_amount:
            raise InsufficientBalance

        user_asset: PreTradingUserAsset = PreTradingUserAsset.query.filter(
            PreTradingUserAsset.user_id == user_id,
            PreTradingUserAsset.asset == asset,
        ).first()
        if not user_asset or user_asset.status == PreTradingUserAsset.Status.FINISHED:
            raise InvalidArgument
        if redeem_amount > user_asset.issue_amount:
            raise InsufficientBalance

        redeem_pledge_amount = pre_asset_cfg.calc_pledge_amount(redeem_amount)
        if redeem_pledge_amount > user_asset.pledge_amount:
            raise InsufficientBalance
        pledge_asset = pre_asset_cfg.pledge_asset
        sys_balance = client.get_user_balances(pre_asset_cfg.sys_user_id, asset=pledge_asset)
        sys_balance_pledge = sys_balance.get(pledge_asset, {}).get("available", Decimal())
        if redeem_pledge_amount > sys_balance_pledge:
            raise ValueError(f"redeem asset {asset} sys_balance_error redeem_pledge_amount {redeem_pledge_amount}")

        # 赎回手续费 = min(最新成交价, 质押价格) * 赎回数量 * 赎回费率
        market = f"{asset}USDT"
        market_row: Market = Market.query.filter(Market.name == market).with_entities(Market.status).first()
        if not market_row:
            # 如果市场没上线，就取0为最新成交价，不收赎回手续费
            last_price = Decimal(0)
        else:
            last_price = None
            for _ in range(2):
                try:
                    last_price = client.market_last(market)
                    break
                except:  # noqa
                    pass
            if last_price is None:
                if market_row.status in [Market.Status.PENDING, Market.Status.OFFLINE]:
                    # 市场下架后一段时间内也可以取到价格，如果报错则默认0
                    last_price = Decimal(0)
                else:
                    raise InvalidArgument(message=gettext("请求频繁，请稍后再试"))
        last_price_f = min(last_price, pre_asset_cfg.pledge_ratio)
        fee_amount = redeem_amount * last_price_f * pre_asset_cfg.redeem_fee_rate
        fee_amount = quantize_amount(fee_amount, PrecisionEnum.COIN_PLACES, ROUND_UP)  # 手续费向上取整
        assert fee_amount >= Decimal(0)
        if fee_amount > redeem_pledge_amount:
            raise ValueError(f"redeem asset {asset} fee_amount_error "
                             f"fee_amount {fee_amount} redeem_pledge_amount {redeem_pledge_amount}")

        op_his = PreTradingUserOpHistory(
            user_id=user_id,
            asset=asset,
            pledge_asset=pledge_asset,
            asset_amount=redeem_amount,
            pledge_asset_amount=redeem_pledge_amount,
            fee_amount=fee_amount,
            op_type=PreTradingUserOpHistory.Type.REDEMPTION,
            status=PreTradingUserOpHistory.Status.CREATED,
        )
        db.session.add(user_asset)
        db.session.add(op_his)
        db.session.flush()
        asset_his_rows = new_asset_his_by_op_his(op_his, pre_asset_cfg.sys_user_id)
        db.session.add_all(asset_his_rows)
        db.session.commit()

        try:
            do_transfer_by_asset_his(asset_his_rows)
        except ServerClient.BadResponse as _e:
            if _e.code == ServerResponseCode.INSUFFICIENT_BALANCE:
                # 有2币扣减，假定是用户的质押币的余额不足，直接改成失败
                for asset_his in asset_his_rows:
                    asset_his.status = PreTradingUserAssetHistory.Status.FAILED
                op_his.status = PreTradingUserOpHistory.Status.FAILED
                db.session.commit()
            raise

        for asset_his in asset_his_rows:
            asset_his.status = PreTradingUserAssetHistory.Status.FINISHED
        op_his.status = PreTradingUserOpHistory.Status.FINISHED
        op_his.finished_at = now()
        user_asset.issue_amount -= op_his.asset_amount
        user_asset.pledge_amount -= op_his.pledge_asset_amount
        if user_asset.issue_amount == 0:
            user_asset.pledge_amount = 0  # 可能因为精度问题剩余一点，也改成0，算做收入
            user_asset.finished_at = now()
            user_asset.finish_type = PreTradingUserAsset.FinishType.REDEEM
        db.session.commit()
    return op_his


def retry_issue_by_op_his(op_his_id: int):
    op_his: PreTradingUserOpHistory = PreTradingUserOpHistory.query.get(op_his_id)
    assert op_his.status == PreTradingUserOpHistory.Status.CREATED
    assert op_his.op_type == PreTradingUserOpHistory.Type.ISSUE

    asset_his_rows: list[PreTradingUserAssetHistory] = PreTradingUserAssetHistory.query.filter(
        PreTradingUserAssetHistory.op_id == op_his.id,
    ).order_by(
        PreTradingUserAssetHistory.id.asc(),  # 先写入扣减的his，再写入增加的his
    ).all()
    asset_his1 = asset_his_rows[0]  # 只有一笔扣减
    assert asset_his1.amount < 0

    user_asset: PreTradingUserAsset = PreTradingUserAsset.query.filter(
        PreTradingUserAsset.user_id == op_his.user_id,
        PreTradingUserAsset.asset == op_his.asset,
    ).first()
    assert user_asset
    assert user_asset.status == PreTradingUserAsset.Status.PLEDGE

    client = ServerClient()
    deduct_res = client.asset_query_business(
        user_id=asset_his1.user_id,
        asset=asset_his1.asset,
        business=OP_TYPE_BUS_TYPE_MAP[asset_his1.op_type],
        business_id=asset_his1.id,
        account_id=SPOT_ACCOUNT_ID,
    )
    if not deduct_res:
        # 第一笔无扣减 直接改成失败
        for asset_his in asset_his_rows:
            asset_his.status = PreTradingUserAssetHistory.Status.FAILED
        op_his.status = PreTradingUserOpHistory.Status.FAILED
        db.session.commit()
        return
    else:
        with CacheLock(LockKeys.pre_trading(user_asset.user_id, user_asset.asset)):
            db.session.rollback()

            remain_his_rows = asset_his_rows[1:]
            do_transfer_by_asset_his(remain_his_rows)
            for asset_his in asset_his_rows:
                asset_his.status = PreTradingUserAssetHistory.Status.FINISHED
            op_his.status = PreTradingUserOpHistory.Status.FINISHED
            op_his.finished_at = now()
            user_asset.issue_amount += op_his.asset_amount
            user_asset.pledge_amount += op_his.pledge_asset_amount
            if user_asset.finish_type is not None:
                user_asset.finish_type = None
            if user_asset.finished_at is not None:
                user_asset.finished_at = None
            db.session.commit()


def retry_redeem_by_op_his(op_his_id: int):
    op_his: PreTradingUserOpHistory = PreTradingUserOpHistory.query.get(op_his_id)
    assert op_his.status == PreTradingUserOpHistory.Status.CREATED
    assert op_his.op_type == PreTradingUserOpHistory.Type.REDEMPTION

    asset_his_rows: list[PreTradingUserAssetHistory] = PreTradingUserAssetHistory.query.filter(
        PreTradingUserAssetHistory.op_id == op_his.id,
    ).order_by(
        PreTradingUserAssetHistory.id.asc(),
    ).all()
    # 有2笔扣减
    asset_his1 = asset_his_rows[0]
    asset_his2 = asset_his_rows[1]
    assert asset_his1.amount < 0 and asset_his2.amount < 0

    user_asset: PreTradingUserAsset = PreTradingUserAsset.query.filter(
        PreTradingUserAsset.user_id == op_his.user_id,
        PreTradingUserAsset.asset == op_his.asset,
    ).first()
    assert user_asset
    assert user_asset.status == PreTradingUserAsset.Status.PLEDGE

    client = ServerClient()
    deduct_res = client.asset_query_business(
        user_id=asset_his1.user_id,
        asset=asset_his1.asset,
        business=OP_TYPE_BUS_TYPE_MAP[asset_his1.op_type],
        business_id=asset_his1.id,
        account_id=SPOT_ACCOUNT_ID,
    )
    if not deduct_res:
        # 第一笔无扣减 直接改成失败
        for asset_his in asset_his_rows:
            asset_his.status = PreTradingUserAssetHistory.Status.FAILED
        op_his.status = PreTradingUserOpHistory.Status.FAILED
        db.session.commit()
        return
    else:
        with CacheLock(LockKeys.pre_trading(user_asset.user_id, user_asset.asset)):
            db.session.rollback()

            # 假设第2笔的系统用户余额一定够，可以扣减成功
            remain_his_rows = asset_his_rows[1:]
            do_transfer_by_asset_his(remain_his_rows)
            for asset_his in asset_his_rows:
                asset_his.status = PreTradingUserAssetHistory.Status.FINISHED
            op_his.status = PreTradingUserOpHistory.Status.FINISHED
            op_his.finished_at = now()
            user_asset.issue_amount -= op_his.asset_amount
            user_asset.pledge_amount -= op_his.pledge_asset_amount
            if user_asset.issue_amount == 0:
                user_asset.finished_at = now()
                user_asset.finish_type = PreTradingUserAsset.FinishType.REDEEM
            db.session.commit()


def send_settle_notice(user_id: int, asset_cfg: PreTradingAssetConfig, settle_at: datetime):
    pref = UserPreferences(user_id)
    settle_at_str = datetime_to_str(settle_at, pref.timezone_offset, fmt='%Y-%m-%d %H:%M')
    message_popup_expired_at = now() + timedelta(days=3)
    params = dict(
        asset=asset_cfg.asset,
        pledge_asset=asset_cfg.pledge_asset,
        settle_price=amount_to_str(asset_cfg.settlement_price),
        settle_at=settle_at_str,
    )
    db.session_add_and_commit(
        Message(
            user_id=user_id,
            title=MessageTitle.PRE_TRADING_SETTLEMENT.name,
            content=MessageContent.PRE_TRADING_SETTLEMENT.name,
            params=json.dumps(params),
            extra_info=json.dumps(
                dict(
                    web_link=MessageWebLink.PRE_TRADING_DETAIL_PAGE.value.format(asset=asset_cfg.asset),
                    android_link="",
                    ios_link="",
                )
            ),
            display_type=Message.DisplayType.POPUP_WINDOW,
            expired_at=message_popup_expired_at,
            channel=Message.Channel.SYSTEM,
        )
    )
    params["detail_url"] = config['SITE_URL'] + f"/pre-token-trading/{asset_cfg.asset}"
    send_pre_trading_notice_email.delay(user_id, "pre_trading_settlement", params)


def group_user_issue_and_pledge_amounts() -> dict:
    """ 预测市场发行数和质押数 """
    rows: list[PreTradingUserAsset] = PreTradingUserAsset.query.filter(
        PreTradingUserAsset.status == PreTradingUserAsset.Status.PLEDGE,
    ).with_entities(
        PreTradingUserAsset.user_id,
        PreTradingUserAsset.asset,
        PreTradingUserAsset.issue_amount,
        PreTradingUserAsset.pledge_asset,
        PreTradingUserAsset.pledge_amount,
    ).all()
    user_issue_data_map = {}
    for r in rows:
        key_ = (r.user_id, r.asset)
        user_issue_data_map[key_] = [r.issue_amount, r.pledge_asset, r.pledge_amount]
    return user_issue_data_map


class SettleHelper:
    """ 交割结算相关逻辑 """

    def __init__(self, asset: str):
        self.asset = asset  # issue asset | token
        self.asset_cfg = get_pre_asset_config(asset)

    @classmethod
    def get_snap_balance(cls, ts: int):
        table = TradeLogDB.table(f'slice_balance_{ts}')  # 这里用脚本指定的时间戳，不用 slice_balance_table
        if not table:
            raise ValueError(f"snap_balance {ts} not exist")
        results = table.select(
            "user_id", "asset", "balance", "t",
            where=f"account={SPOT_ACCOUNT_ID}",
        )
        return results

    def log(self, *args):
        current_app.logger.warning(f"SettleHelper【{self.asset}】{str(args)}")

    def get_issue_asset_balances(self, ts: int) -> dict[int, Decimal]:
        balance_rows = self.get_snap_balance(ts)
        pledge_asset_balances = defaultdict(Decimal)
        has_error_data = False
        for r in balance_rows:
            user_id, asset_, balance, t = r
            if asset_ == self.asset:
                if t != ServerBalanceType.AVAILABLE.value:
                    # 有其他类型的余额，说明数据不正常
                    self.log(f"snap_balance {self.asset} {ts} has error type {t} {r}")
                    has_error_data = True
                pledge_asset_balances[user_id] += Decimal(balance)
        if has_error_data:
            raise ValueError(f"snap_balance {self.asset} {ts} has error type data")
        for user_id, amount in pledge_asset_balances.items():
            pledge_asset_balances[user_id] = quantize_amount(amount, PrecisionEnum.COIN_PLACES)  # equal 0
        return {k: v for k, v in pledge_asset_balances.items() if v > 0}

    def update_issue_asset_status_to_finished(self):
        # 可能有全部赎回的记录，也更新下状态
        PreTradingUserAsset.query.filter(
            PreTradingUserAsset.asset == self.asset,
            PreTradingUserAsset.issue_amount == 0,
            PreTradingUserAsset.status == PreTradingUserAsset.Status.PLEDGE,
        ).update(
            {'status': PreTradingUserAsset.Status.FINISHED},
            synchronize_session=False,
        )
        self.asset_cfg.status = PreTradingAssetConfig.Status.FINISHED
        db.session.add(self.asset_cfg)
        db.session.commit()

    def execute_settle(self, ts: int):
        """
        执行交割：
            1. 根据PreTradingUserAsset和token的余额数据，生成UserSettlementHistory
            2. 根据UserSettlementHistory，生成PreTradingUserOpHistory（1条或2条）
            3. 按PreTradingUserOpHistory单笔执行（写PreTradingUserAssetHistory、执行划转、更新UserAsset等）
        """

        self.check()

        # 1. 生成交割历史
        settle_his_rows = self.do_gen_settle_his(ts)

        # 2. 生成对应的op_his
        op_his_rows = PreTradingUserOpHistory.query.filter(
            PreTradingUserOpHistory.asset == self.asset,
            PreTradingUserOpHistory.op_type.in_(
                [
                    PreTradingUserOpHistory.Type.ISSUE_SETTLEMENT,
                    PreTradingUserOpHistory.Type.POSITION_SETTLEMENT,
                ]
            ),
        ).order_by(PreTradingUserOpHistory.id.asc()).all()
        if not op_his_rows:
            op_his_rows = self.generate_op_histories(settle_his_rows)

        # 3. 处理op_his
        failed_op_his_rows = []
        success_user_ids = set()
        for op_his in op_his_rows:
            try:
                _is_suc = self.do_settle_by_op_his(op_his)
                if _is_suc:
                    success_user_ids.add(op_his.user_id)
            except Exception as e:
                db.session.rollback()
                self.log(f"do_settle_by_op_his {op_his.id} error {e!r}")
                current_app.logger.exception(f"do_settle_by_op_his {op_his.to_dict()} error {e!r}")
                failed_op_his_rows.append(op_his)

        # 4. 发送触达
        self.send_users_notice(success_user_ids)

        if failed_op_his_rows:
            raise ValueError(f"存在失败的OpHis {failed_op_his_rows}")

    def do_gen_settle_his(self, ts: int) -> list[UserSettlementHistory]:
        """ 生成用户交割记录 """
        settle_his_rows = UserSettlementHistory.query.filter(
            UserSettlementHistory.asset == self.asset,
        ).order_by(UserSettlementHistory.id.asc()).all()
        if not settle_his_rows:
            settle_his_rows = self.generate_settle_histories(ts)
        return settle_his_rows

    def check(self):
        pre_asset_cfg = self.asset_cfg
        if pre_asset_cfg.active_status != PreTradingAssetConfig.ActiveStatus.STOPPED:
            raise ValueError(f"发行币{self.asset}状态不是STOPPED")

        settle_price = pre_asset_cfg.settlement_price
        max_price = pre_asset_cfg.pledge_ratio
        if settle_price > max_price:
            raise ValueError(f"发行币{self.asset} 交割价{settle_price} 大于 质押比例|价格上限{max_price}")

    def generate_settle_histories(self, ts: int) -> list[UserSettlementHistory]:
        asset = self.asset
        pre_asset_cfg = self.asset_cfg
        pledge_asset = pre_asset_cfg.pledge_asset
        pos_asset_balances = self.get_issue_asset_balances(ts=ts)

        user_asset_rows: list[PreTradingUserAsset] = PreTradingUserAsset.query.filter(
            PreTradingUserAsset.asset == asset,
            PreTradingUserAsset.issue_amount > 0,
        ).all()

        # 先执行发行质押的交割，再进行持仓交割
        zero = Decimal(0)
        settle_price = pre_asset_cfg.settlement_price
        user_issue_settle_map = dict()  # value: tuple(当前发行数量, 发行交割金额, 发行交割手续费)
        for user_asset in user_asset_rows:
            # 发行质押的交割：交割资金=质押资金-交割价格*发行数量 ， 实际到账金额=交割价值-（交割价值*交易手续费1%）
            issue_settle_amount = user_asset.pledge_amount - settle_price * user_asset.issue_amount
            issue_settle_amount = quantize_amount(issue_settle_amount, PrecisionEnum.COIN_PLACES)
            issue_settle_amount = max(issue_settle_amount, zero)
            issue_fee_amount = issue_settle_amount * pre_asset_cfg.settlement_fee_rate
            issue_fee_amount = quantize_amount(issue_fee_amount, PrecisionEnum.COIN_PLACES, ROUND_UP)
            issue_fee_amount = min(issue_fee_amount, issue_settle_amount)
            user_issue_settle_map[user_asset.user_id] = (
                user_asset.issue_amount, issue_settle_amount, issue_fee_amount
            )

        user_pos_settle_map = dict()  # value: tuple(当前持仓数量, 持仓交割金额, 持仓交割手续费)
        for user_id, pos_amount in pos_asset_balances.items():
            if not user_id:
                raise ValueError(f"execute_settle asset {asset} snap_balance_data_error "
                                 f"user_id:{user_id}, amount:{pos_amount}")

            # 现货持仓的交割：交割资金=持仓数量*交割价格 ，实际到账金额=交割价值-（交割价值*交易手续费1%）
            pos_settle_amount = pos_amount * settle_price
            pos_settle_amount = quantize_amount(pos_settle_amount, PrecisionEnum.COIN_PLACES)
            pos_fee_amount = pos_settle_amount * pre_asset_cfg.settlement_fee_rate
            pos_fee_amount = quantize_amount(pos_fee_amount, PrecisionEnum.COIN_PLACES, ROUND_UP)
            pos_fee_amount = min(pos_fee_amount, pos_settle_amount)
            user_pos_settle_map[user_id] = (
                pos_amount, pos_settle_amount, pos_fee_amount
            )

        # 系统用户要扣减的质押币的数目，交割单位是质押币种
        sys_total_deduct_amount = (
            sum(i[1] - i[2] for i in user_issue_settle_map.values()) +
            sum(i[1] - i[2] for i in user_pos_settle_map.values())
        )
        sys_balance = ServerClient().get_user_balances(pre_asset_cfg.sys_user_id, asset=pledge_asset)
        sys_balance_pledge = sys_balance.get(pledge_asset, {}).get('available', Decimal())
        if sys_total_deduct_amount > sys_balance_pledge:
            raise ValueError(f"execute_settle asset {asset} sys_balance_error "
                             f"sys_total_deduct_amount {sys_total_deduct_amount}")

        settle_his_rows = []
        all_user_ids = set(user_issue_settle_map) | set(user_pos_settle_map)
        for user_id in all_user_ids:
            issue_settle_amounts = user_issue_settle_map.get(user_id, (zero, zero, zero))
            pos_settle_amounts = user_pos_settle_map.get(user_id, (zero, zero, zero))
            settle_his = UserSettlementHistory(
                user_id=user_id,
                asset=asset,
                pledge_asset=pledge_asset,
                issue_amount=issue_settle_amounts[0],
                position_amount=pos_settle_amounts[0],
                settle_price=settle_price,
                issue_fee_amount=issue_settle_amounts[2],
                position_fee_amount=pos_settle_amounts[2],
                issue_settle_amount=issue_settle_amounts[1],
                position_settle_amount=pos_settle_amounts[1],
            )
            settle_his_rows.append(settle_his)
        db.session.add_all(settle_his_rows)
        db.session.commit()
        return settle_his_rows

    @classmethod
    def generate_op_histories(cls, settle_his_rows: list[UserSettlementHistory]) -> list[PreTradingUserOpHistory]:
        op_his_rows = []
        for settle_his in settle_his_rows:
            user_id = settle_his.user_id
            asset_ = settle_his.asset
            pledge_asset = settle_his.pledge_asset
            if settle_his.issue_amount > 0:
                op_his = PreTradingUserOpHistory(
                    user_id=user_id,
                    asset=asset_,
                    pledge_asset=pledge_asset,
                    asset_amount=settle_his.issue_amount,
                    pledge_asset_amount=settle_his.issue_settle_amount,
                    fee_amount=settle_his.issue_fee_amount,
                    op_type=PreTradingUserOpHistory.Type.ISSUE_SETTLEMENT,
                    status=PreTradingUserOpHistory.Status.CREATED,
                )
                op_his_rows.append(op_his)
            if settle_his.position_amount > 0:
                op_his = PreTradingUserOpHistory(
                    user_id=user_id,
                    asset=asset_,
                    pledge_asset=pledge_asset,
                    asset_amount=settle_his.position_amount,
                    pledge_asset_amount=settle_his.position_settle_amount,
                    fee_amount=settle_his.position_fee_amount,
                    op_type=PreTradingUserOpHistory.Type.POSITION_SETTLEMENT,
                    status=PreTradingUserOpHistory.Status.CREATED,
                )
                op_his_rows.append(op_his)
        db.session.add_all(op_his_rows)
        db.session.commit()
        return op_his_rows

    def do_settle_by_op_his(self, op_his: PreTradingUserOpHistory):
        if op_his.status == PreTradingUserOpHistory.Status.FINISHED:
            return

        assert op_his.op_type in [
            PreTradingUserOpHistory.Type.ISSUE_SETTLEMENT,
            PreTradingUserOpHistory.Type.POSITION_SETTLEMENT,
        ]

        asset_his_rows: list[PreTradingUserAssetHistory] = PreTradingUserAssetHistory.query.filter(
            PreTradingUserAssetHistory.op_id == op_his.id,
        ).order_by(
            PreTradingUserAssetHistory.id.asc(),
        ).all()
        if not asset_his_rows:
            asset_his_rows = new_asset_his_by_op_his(op_his, self.asset_cfg.sys_user_id)
            db.session.add_all(asset_his_rows)
            db.session.commit()

        if op_his.op_type == PreTradingUserOpHistory.Type.ISSUE_SETTLEMENT:
            # 只有一笔扣减（扣系统用户现货的质押币）
            asset_his1 = asset_his_rows[0]
            assert asset_his1.amount <= 0  # 交割价=pledge_ratio时，扣减数为0

            user_asset: PreTradingUserAsset = PreTradingUserAsset.query.filter(
                PreTradingUserAsset.user_id == op_his.user_id,
                PreTradingUserAsset.asset == op_his.asset,
            ).first()
            assert user_asset
            assert user_asset.status == PreTradingUserAsset.Status.PLEDGE

            do_transfer_by_asset_his(asset_his_rows)
            for asset_his in asset_his_rows:
                asset_his.status = PreTradingUserAssetHistory.Status.FINISHED
            op_his.status = PreTradingUserOpHistory.Status.FINISHED
            op_his.finished_at = now()
            user_asset.issue_amount = 0
            user_asset.pledge_amount = 0
            user_asset.finished_at = now()
            user_asset.finish_type = PreTradingUserAsset.FinishType.SETTLEMENT
            user_asset.status = PreTradingUserAsset.Status.FINISHED
            db.session.commit()
        else:
            # 有2笔扣减（扣用户现货的持仓币，扣系统用户现货的质押币）
            asset_his1 = asset_his_rows[0]
            asset_his2 = asset_his_rows[1]
            assert asset_his1.amount < 0 and asset_his2.amount <= 0  # 交割价=0时，第二笔扣减数为0
            do_transfer_by_asset_his(asset_his_rows)
            for asset_his in asset_his_rows:
                asset_his.status = PreTradingUserAssetHistory.Status.FINISHED
            op_his.status = PreTradingUserOpHistory.Status.FINISHED
            op_his.finished_at = now()
            db.session.commit()
        self.log(f"do_settle_by_op_his {op_his.id} {op_his.user_id} success")
        return True

    def send_users_notice(self, user_ids: set[int]):
        now_ = now()
        for user_id in user_ids:
            try:
                send_settle_notice(user_id, self.asset_cfg, now_)
            except Exception as _e:
                self.log(f"send_settle_notice {user_ids} error {_e!r}")


class SettleExecutor:
    """ 执行交割相关逻辑 """

    def __init__(self, asset: str):
        self.asset = asset  # issue asset | token
        self.asset_cfg = get_pre_asset_config(asset)

    def log(self, *args):
        current_app.logger.warning(f"SettleExecutor【{self.asset}】{str(args)}")

    @classmethod
    def check_market(cls, market_name: str):
        market_row: Market = Market.query.filter(Market.name == market_name).first()
        if market_row and not (market_row.status == Market.Status.OFFLINE or market_row.trading_disabled):
            if not MarketMaintain.query.filter(
                MarketMaintain.market == market_name,
                MarketMaintain.maintain_status == MarketMaintain.MaintainStatus.OUT_OF_SERVICE,
                MarketMaintain.status == MarketMaintain.Status.VALID,
            ):
                raise InvalidArgument(message=f"市场 {market_name} 状态不是下架|OFFLINE 或 交易未关闭 或 未单市场停服维护")
        amm_market_row: AmmMarket = AmmMarket.query.filter(AmmMarket.name == market_name).first()
        if amm_market_row and amm_market_row.status != AmmMarket.Status.OFFLINE:
            raise InvalidArgument(message=f"AMM市场 {market_name} 状态不是下架|OFFLINE")

        client = ServerClient()
        if market_row:
            res = client.market_order_depth(market=market_name, limit=1, interval='0')
            if len(res['asks']) > 0:
                raise InvalidArgument(message=f"市场 {market_name} 存在asks")
            if len(res['bids']) > 0:
                raise InvalidArgument(message=f"市场 {market_name} 存在bids")
            try:
                orders = client.market_stop_book_orders(market_name, 1, 1, 1)
            except:  # noqa
                orders = []
            if len(orders) > 0:
                raise InvalidArgument(message=f"市场 {market_name} 存在stop_order")

    def check_deps(self, ts: int):
        """
        检查相关依赖：
        1、发行配置的状态
        2、要求市场停了，要求市场没有订单、issue_asset没有冻结资产
        """
        from app.assets import get_asset_config

        issue_asset = self.asset
        asset_cfg = get_asset_config(issue_asset, mode=ConfigMode.REAL_TIME)
        if asset_cfg.local_transfers_enabled:
            asset_cfg.local_transfers_enabled = False
            self.log(f"发行token {issue_asset} local_transfers_enabled set to False")

        pre_asset_cfg = self.asset_cfg
        if pre_asset_cfg.active_status != PreTradingAssetConfig.ActiveStatus.STOPPED:
            raise InvalidArgument(message=f"发行token {issue_asset} 状态不是交割中|STOPPED")

        helper = SettleHelper(issue_asset)
        helper.check()

        market_name = f"{pre_asset_cfg.asset}{pre_asset_cfg.pledge_asset}"
        self.check_market(market_name)

        min_balance_amount = amount_to_str(Decimal(10) ** -8)
        _table = TradeLogDB.table(f'slice_balance_{ts}')
        if not _table.exists():
            raise InvalidArgument(message=f"server资产快照 {_table} 不存在，请等待下一次快照")
        bad_rows = _table.select(
            "user_id", "asset", "balance", "t",
            where=f"account={SPOT_ACCOUNT_ID} and asset='{issue_asset}' "
                  f"and t in ({ServerBalanceType.FROZEN.value},{ServerBalanceType.LOCK.value})",
        )
        if bad_rows:
            self.log("bad_rows", bad_rows[:10])
            raise InvalidArgument(message="资产快照存在非AVAILABLE记录，请等待下一次快照")
        user0_rows = _table.select(
            "user_id", "asset", "balance", "t",
            where=f"account={SPOT_ACCOUNT_ID} and asset='{issue_asset}' and user_id=0 and balance>={min_balance_amount}",
        )
        if user0_rows:
            self.log("user0_rows", user0_rows)
            raise InvalidArgument(message="资产快照存在用户0的记录，请等待下一次快照")

    def step1_transfer_user0_asset(self) -> tuple[bool, str]:
        """ 把0账户的issue_asset资产转到小币兑换账号 """

        issue_asset = self.asset
        pre_asset_cfg = self.asset_cfg
        if pre_asset_cfg.active_status != PreTradingAssetConfig.ActiveStatus.STOPPED:
            raise InvalidArgument(message=f"发行token {issue_asset} 状态不是交割中|STOPPED")

        market_name = f"{pre_asset_cfg.asset}{pre_asset_cfg.pledge_asset}"
        self.check_market(market_name)

        with CacheLock(LockKeys.pre_trading_settle(issue_asset)):
            db.session.rollback()

            client = ServerClient()
            user0_balance = client.get_user_balances(user_id=0, asset=issue_asset).get(issue_asset, {})
            assert user0_balance.get('frozen', 0) == 0
            assert user0_balance.get('lock', 0) == 0

            avai_amount = user0_balance.get('available', 0)
            if avai_amount > 0:
                to_user_id = config['SMALL_COIN_EXCHANGE_USER_ID']
                bus_id = BalanceUpdateBusiness.new_id(
                    to_user_id,
                    issue_asset,
                    avai_amount,
                )
                remark = f"pre_trading token:{issue_asset} trans"
                client.batch_add_user_balance(
                    [
                        dict(
                            user_id=0,
                            asset=issue_asset,
                            amount=-avai_amount,
                            business=BalanceBusiness.SYSTEM,
                            business_id=bus_id,
                            detail={"remark": remark},
                            account_id=SPOT_ACCOUNT_ID,
                        ),
                        dict(
                            user_id=to_user_id,
                            asset=issue_asset,
                            amount=avai_amount,
                            business=BalanceBusiness.SYSTEM,
                            business_id=bus_id,
                            detail={"remark": remark},
                            account_id=SPOT_ACCOUNT_ID,
                        )
                    ]
                )
                self.log(f"用户0 {issue_asset}资产数为{avai_amount} 划转到 {to_user_id} 成功，请等待下一次快照")
                return True, f"用户0 {issue_asset}资产数为{amount_to_str(avai_amount)} 划转成功，请等待下一次快照后，再生成交割数据"
            else:
                self.log(f"用户0 {issue_asset}资产数为{avai_amount} {user0_balance}，不执行划转")
                return False, f"用户0 {issue_asset}资产数为{amount_to_str(avai_amount)} 无需执行划转，请执行下一步"

    def step2_generate_settle_his(self, ts: int):
        """ 生成用户交割记录 """
        self.check_deps(ts)

        issue_asset = self.asset
        user0_balance = ServerClient().get_user_balances(user_id=0, asset=issue_asset).get(issue_asset, {})
        if user0_balance.get('available', 0) > 0:
            raise InvalidArgument(message=f"用户0 存在{issue_asset}资产{user0_balance}，请先进行划转")

        helper = SettleHelper(issue_asset)
        helper.check()

        with CacheLock(LockKeys.pre_trading_settle(issue_asset)):
            db.session.rollback()

            self.log("generate_settle_his start ")
            settle_his_rows = helper.do_gen_settle_his(ts)
            self.log(f"generate_settle_his done, total {len(settle_his_rows)} settle_his_rows")

    def step3_execute_settle(self, ts: int):
        from app.business.risk_control.pre_trading import check_pre_market_settle_task, FailOption
        from app.caches.pre_trading import PreAssetViewCache, PreAssetStatisticCache

        self.check_deps(ts)

        issue_asset = self.asset

        with CacheLock(LockKeys.pre_trading_settle(issue_asset)):
            db.session.rollback()

            helper = SettleHelper(issue_asset)
            self.log("execute_settle start ")
            try:
                helper.execute_settle(ts)
            except Exception as e_:
                check_pre_market_settle_task.delay(issue_asset, FailOption.RETRY)
                raise e_
            self.log("execute_settle done ")

            helper.update_issue_asset_status_to_finished()
            self.log("update_issue_asset_status_to_finished done ")

        check_pre_market_settle_task.delay(issue_asset, FailOption.RETRY)

        PreAssetViewCache.reload()
        self.log("PreAssetViewCache.reload done")
        PreAssetStatisticCache.reload()
        self.log("PreAssetStatisticCache.reload done")
