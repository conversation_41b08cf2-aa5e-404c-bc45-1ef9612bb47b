# -*- coding: utf-8 -*-
import contextlib
from decimal import Decimal
from enum import IntEnum
from functools import wraps
from json import dumps as json_dumps
from inspect import getfullargspec
from logging import Logger, getLogger
from typing import (Dict, List, Tuple, Iterable, Callable, Type, Optional,
                    Union, Any, TypeVar)
import pymysql
import requests
from flask import current_app
from hmac import HMAC
import hashlib

from flask_babel import gettext

from app.business.prices import PriceManager
from app.common.constants import ProducerTopics, OrderSideType

from ...common import BalanceBusiness, OrderOption, PERPETUAL_ALL_MARKET_TYPE
from ...config import config
from ...models import Deposit, Withdrawal
from ...exceptions import (
    InternalServerError, InvalidArgument,
    ServiceTimeout, ServiceUnavailable, OrderExceptionMap, OrderException,
    InsufficientBalance, ErrorWithResponseCode, PerpetualOrderExceptionMap,
    PerpetualResponseCode, NotCompletelyKillOrder, OnlyMakerKillOrder
)
from ...models.system import MarketMaintain
from ...producer import exactly_once_producer
from ...utils import BaseHTTPClient, JsonRPC2Client, RESTClient, AmountType, \
    current_timestamp
from ...utils.http_client import Headers

_logger = getLogger(__name__)

ALL_MARGIN_ACCOUNT_ID = -2
ALL_RECORD_ACCOUNT_ID = -1

SPOT_ACCOUNT_ID = 0

MAX_ORDER_ACCOUNT_ID = 10000
# 买&卖
ORDER_BOTH_SIDE = 0

# 用户订单
USER_ORDER_SOURCE = OrderOption.NORMAL
# 系统订单(目前有杠杆强平单、质押兑换订单)
SYSTEM_ORDER_SOURCE = OrderOption.SYSTEM

REQUEST_HEADERS = {"Request-Source": "web"}


def validate_order_account_id(_account_id):
    return _account_id == ALL_MARGIN_ACCOUNT_ID or \
        ALL_RECORD_ACCOUNT_ID <= _account_id < MAX_ORDER_ACCOUNT_ID


def _update_detail_price(asset, detail) -> Dict:
    price = PriceManager.asset_to_usd(asset)
    detail = detail or {}

    # p代表实时市场价，字段名和server产生的成交流水一致
    detail['p'] = detail.get('p', str(price))
    return detail

def _send_balance_update_msg(user_id, account_id, asset, business, business_id, amount, detail):
    event_data = {
            'user_id': user_id,
            'account_id': account_id,
            'asset': asset,
            'business': business,
            'business_id': business_id,
            'price': detail.get('p', 0),
            'amount': str(amount),
            'detail': detail or {},
        }
    message = dict(
        event_data=event_data,
        biz_type=business,
        biz_id=business_id,
        timestamp=current_timestamp(to_int=True),
        user_id=user_id
    )
    exactly_once_producer.send_message(ProducerTopics.BALANCE_UPDATE, message)

class ServerResponseCode(IntEnum):
    OK = 0
    INVALID_ARGUMENT = 1
    INTERNAL_ERROR = 2
    SERVICE_UNAVAILABLE = 3
    METHOD_NOT_FOUND = 4
    SERVICE_TIMEOUT = 5
    ORDER_NOT_FOUND = 6
    INVALID_OPERATION = 7
    SERVER_TOO_BUSY = 8
    DUPLICATE_BALANCE_UPDATE = 10
    INSUFFICIENT_BALANCE = 11
    NOT_COMPLETELY_KILL_ORDER = 12
    ONLY_MAKER_KILL_ORDER = 13
    PROTECT_DURATION_ORDER_MAKER_ONLY = 14
    MARKET_OUT_OF_SERVICE = 15
    MARKET_PROTECT_DURATION = 16


class OrderResponseCode(IntEnum):
    OK = 0
    INVALID_ARGUMENT = 1
    INTERNAL_ERROR = 2
    SERVICE_UNAVAILABLE = 3
    METHOD_NOT_FOUND = 4
    SERVICE_TIMEOUT = 5
    SERVER_TOO_BUSY = 8
    BALANCE_NOT_ENOUGH = 10
    LESS_THAN_LEAST_AMOUNT = 11
    NOT_COMPLETELY_KILL_ORDER = 12
    ONLY_MAKER_KILL_ORDER = 13
    PROTECT_DURATION_ORDER_MAKER_ONLY = 14
    MARKET_OUT_OF_SERVICE = 15
    MARKET_PROTECT_DURATION = 16


class StopOrderResponseCode(IntEnum):
    OK = 0
    INVALID_ARGUMENT = 1
    INTERNAL_ERROR = 2
    SERVICE_UNAVAILABLE = 3
    METHOD_NOT_FOUND = 4
    SERVICE_TIMEOUT = 5
    SERVER_TOO_BUSY = 8
    INVALID_STOP_PRICE = 10
    LESS_THAN_LEAST_AMOUNT = 11
    NOT_COMPLETELY_KILL_ORDER = 12
    ONLY_MAKER_KILL_ORDER = 13
    PROTECT_DURATION_ORDER_MAKER_ONLY = 14
    MARKET_OUT_OF_SERVICE = 15
    MARKET_PROTECT_DURATION = 16


class CancelOrderResponseCode(IntEnum):
    OK = 0
    INVALID_ARGUMENT = 1
    INTERNAL_ERROR = 2
    SERVICE_UNAVAILABLE = 3
    METHOD_NOT_FOUND = 4
    SERVICE_TIMEOUT = 5
    SERVER_TOO_BUSY = 8
    ORDER_NOT_FOUND = 10
    USER_NOT_MATCH = 11
    SYSTEM_ORDER = 12
    MARKET_OUT_OF_SERVICE = 15


class BatchResponseCode(IntEnum):
    # 调用 batch 操作时，web 封装的错误
    UNLOCK_BALANCE_ERROR = 1
    UPDATE_BALANCE_ERROR = 2


class QueryBusinessFlag(IntEnum):
    # 0表示默认asset，1表示 lock asset，2表示unlock asset
    ASSET = 0
    LOCK = 1
    UNLOCK = 2


ORDER_EXCEPTION_MAPPING: Dict[int, Type[Exception]] = {
    ServerResponseCode.INVALID_ARGUMENT: InvalidArgument,
    ServerResponseCode.INTERNAL_ERROR: InternalServerError,
    ServerResponseCode.SERVICE_UNAVAILABLE: ServiceUnavailable,
    ServerResponseCode.METHOD_NOT_FOUND: ServiceUnavailable,
    ServerResponseCode.INVALID_OPERATION: ServiceUnavailable,
    ServerResponseCode.SERVER_TOO_BUSY: ServiceUnavailable,
    ServerResponseCode.SERVICE_TIMEOUT: ServiceTimeout,
    ServerResponseCode.ORDER_NOT_FOUND: OrderExceptionMap[OrderException.ORDER_NOT_FOUND],
    OrderResponseCode.BALANCE_NOT_ENOUGH: InsufficientBalance,
    OrderResponseCode.LESS_THAN_LEAST_AMOUNT:
        OrderExceptionMap[OrderException.LESS_THAN_LEAST_AMOUNT],
    OrderResponseCode.NOT_COMPLETELY_KILL_ORDER: NotCompletelyKillOrder,
    OrderResponseCode.ONLY_MAKER_KILL_ORDER: OnlyMakerKillOrder,
    OrderResponseCode.PROTECT_DURATION_ORDER_MAKER_ONLY: OrderExceptionMap[
        OrderException.PROTECT_DURATION_ORDER_MAKER_ONLY],
    OrderResponseCode.MARKET_OUT_OF_SERVICE: OrderExceptionMap[
        OrderException.MARKET_OUT_OF_SERVICE],
    OrderResponseCode.MARKET_PROTECT_DURATION: OrderExceptionMap[
        OrderException.MARKET_PROTECT_DURATION],
}

STOP_ORDER_EXCEPTION_MAPPING: Dict[int, Type[Exception]] = {
    ServerResponseCode.INVALID_ARGUMENT: InvalidArgument,
    ServerResponseCode.INTERNAL_ERROR: InternalServerError,
    ServerResponseCode.SERVICE_UNAVAILABLE: ServiceUnavailable,
    ServerResponseCode.METHOD_NOT_FOUND: ServiceUnavailable,
    ServerResponseCode.INVALID_OPERATION: ServiceUnavailable,
    ServerResponseCode.SERVER_TOO_BUSY: ServiceUnavailable,
    ServerResponseCode.SERVICE_TIMEOUT: ServiceTimeout,
    StopOrderResponseCode.INVALID_STOP_PRICE: InvalidArgument(message=gettext(
        '触发价不可等于最新成交价')),
    StopOrderResponseCode.LESS_THAN_LEAST_AMOUNT:
        OrderExceptionMap[OrderException.LESS_THAN_LEAST_AMOUNT],
    StopOrderResponseCode.NOT_COMPLETELY_KILL_ORDER: NotCompletelyKillOrder,
    StopOrderResponseCode.ONLY_MAKER_KILL_ORDER: OnlyMakerKillOrder,
    StopOrderResponseCode.PROTECT_DURATION_ORDER_MAKER_ONLY: OrderExceptionMap[
        OrderException.PROTECT_DURATION_ORDER_MAKER_ONLY],
    StopOrderResponseCode.MARKET_OUT_OF_SERVICE: OrderExceptionMap[
        OrderException.MARKET_OUT_OF_SERVICE],
    StopOrderResponseCode.MARKET_PROTECT_DURATION: OrderExceptionMap[
        OrderException.MARKET_PROTECT_DURATION],
}

CANCEL_ORDER_EXCEPTION_MAPPING: Dict[int, Type[Exception]] = {
    ServerResponseCode.INVALID_ARGUMENT: InvalidArgument,
    ServerResponseCode.INTERNAL_ERROR: InternalServerError,
    ServerResponseCode.SERVICE_UNAVAILABLE: ServiceUnavailable,
    ServerResponseCode.METHOD_NOT_FOUND: ServiceUnavailable,
    ServerResponseCode.SERVICE_TIMEOUT: ServiceTimeout,
    ServerResponseCode.INVALID_OPERATION: OrderExceptionMap[OrderException.BIDDING_STATUS],
    ServerResponseCode.SERVER_TOO_BUSY: ServiceUnavailable,
    CancelOrderResponseCode.ORDER_NOT_FOUND:
        OrderExceptionMap[OrderException.ORDER_NOT_FOUND],
    CancelOrderResponseCode.USER_NOT_MATCH:
        OrderExceptionMap[OrderException.NOT_YOUR_ORDER],
    CancelOrderResponseCode.SYSTEM_ORDER:
        OrderExceptionMap[OrderException.SYSTEM_ORDER],
    CancelOrderResponseCode.MARKET_OUT_OF_SERVICE: OrderExceptionMap[
        OrderException.MARKET_OUT_OF_SERVICE]
}

T = TypeVar('T')


class ResultPage(List[T]):

    def __init__(self, items: Any, page: int, limit: int, total: int = None):
        if limit is not None:
            has_next = len(items) > limit
            del items[limit:]
        else:
            has_next = False
        super().__init__(items)
        self._page = page
        self._limit = limit
        self._total = total
        self._has_next = has_next

    @classmethod
    def from_response(cls, response: dict, page: int, limit: int,
                      *, data_attr: str = 'records'):
        return cls(
            response[data_attr],
            page,
            limit,
            response.get('total')
        )

    @property
    def page(self) -> int:
        return self._page

    @property
    def limit(self) -> int:
        return self._limit

    @property
    def total(self) -> Optional[int]:
        return self._total

    @property
    def has_next(self) -> bool:
        return self._has_next

    @has_next.setter
    def has_next(self, has_next: bool):
        self._has_next = has_next

    def as_dict(self, converter: Callable = None):
        if converter is not None:
            for i, x in enumerate(self):
                self[i] = converter(x)
        res = dict(
            data=self,
            curr_page=self._page,
            has_next=self.has_next,
            count=len(self)
        )
        if (total := self._total) is not None:
            res['total'] = total
        return res


# https://github.com/viabtc/coinex_exchange_server/blob/master/doc/HTTP-Protocol.md


class _BaseServerClient:
    _client: JsonRPC2Client
    ResponseCode: Type[IntEnum]
    ExceptionMap: Dict[int, ErrorWithResponseCode]
    IGNORE_ERROR_CODES: Tuple[IntEnum, ...]

    WhiteListCode = (
        ServerResponseCode.SERVICE_TIMEOUT,
        ServerResponseCode.SERVICE_UNAVAILABLE,
        PerpetualResponseCode.CONTRACT_SERVICE_TIMEOUT,
        PerpetualResponseCode.CONTRACT_SERVICE_UNAVAILABLE,
        PerpetualResponseCode.CONTRACT_SERVICE_TIMEOUT_LOW_LEVEL,
        PerpetualResponseCode.CONTRACT_SERVICE_UNAVAILABLE_LOW_LEVEL,
    )

    class BadResponse(ErrorWithResponseCode):

        def __init__(self, code: int, data: dict):
            self._code = code
            self.message_template = data.get('message', '')
            super().__init__(data=data)

        @property
        def code(self) -> int:
            return self._code

        @property
        def response_code(self) -> int:
            return self._code

    class BatchBadResponse(BadResponse):

        def __init__(self, code: int, data: dict, batch_code: BatchResponseCode):
            self.batch_code = batch_code
            super().__init__(code, data)

    @classmethod
    def retry(cls, count: int):
        return BaseHTTPClient.retry(
            count,
            exc_whitelist=(
                lambda e: (isinstance(e, _BaseServerClient.BadResponse)
                           and e.code in cls.WhiteListCode)))

    def do_request(self, method: str, *args, **kwargs) -> Any:
        with JsonRPC2Client.Options(**kwargs):
            try:
                return self._client.post(method, *args)
            except JsonRPC2Client.BadResponse as e:
                current_app.logger.error(f'request {method=}, {args=}, {kwargs=}, '
                                         f'response {e.code=} {e.data=}')
                raise ServiceUnavailable(dict(code=e.code, data=e.data))
            except JsonRPC2Client.RPCBadResponse as e:
                ex = self.BadResponse(e.code, e.dict)
                if e.code not in self.IGNORE_ERROR_CODES:
                    current_app.logger.error(f'request {method=}, {args=}, {kwargs=}, '
                                             f'response {e.code=} {e.dict=}')
                if ex.code in self.ExceptionMap:
                    ex.message_template = self.ExceptionMap[
                        ex.code].message_template
                raise ex
            except requests.exceptions.ReadTimeout:
                raise self.ExceptionMap[ServerResponseCode.SERVICE_TIMEOUT]


_retry = _BaseServerClient.retry


def _str_to_decimal(balance, *, depth: int = 0):
    if depth <= 0:
        return Decimal(balance)
    depth -= 1
    return {key: _str_to_decimal(value, depth=depth)
            for key, value in balance.items()}


def _page_to_offset(page: int = 1, limit: int = 10):
    page = max(1, page)
    return (page - 1) * limit, limit + 1


@contextlib.contextmanager
def ignore_duplicate_error():
    try:
        yield
    except ServerClient.BadResponse as e:
        if e.code == ServerResponseCode.DUPLICATE_BALANCE_UPDATE:
            pass


def batched(func=None, *, max_limit: int = None):
    def dec(_func):
        # In order to replace the `page` and `limit` arguments we need to find
        # out whether these arguments are positional or keywords.
        page_idx, limit_idx = None, None
        arg_spec = getfullargspec(_func)
        for i, a in enumerate(arg_spec.args):
            if a == 'page':
                page_idx = i
            elif a == 'limit':
                limit_idx = i

        # Subtraction by 1 is for preservation of the potential increment of
        # `limit` to detect whether there's another page ahead.
        limit = (max_limit or config['SERVER_CLIENT_BATCH_PAGE_LIMIT']) - 1

        @wraps(_func)
        def wrapper(*args, **kwargs):
            nonlocal page_idx, limit_idx, limit

            # First we find out whether each of `page` and `limit` is passed to
            # the function positionally or as a keyword.
            _page, _limit = None, None
            if limit_idx is not None:
                try:
                    _limit = args[limit_idx]
                except IndexError:
                    pass
            if _limit is None:
                _limit = kwargs.get('limit')
            if _limit is None or _limit <= limit:
                return _func(*args, **kwargs)
            if page_idx is not None:
                try:
                    _page = args[page_idx]
                except IndexError:
                    pass
            if _page is None:
                _page = kwargs.get('page', 1)

            # Then we replace them with auto incremental pages and a proper
            # limit.
            _args_copy, _kwargs_copy = list(args), kwargs.copy()
            _result = None
            __page = _page
            while True:
                if page_idx is not None and page_idx < len(args):
                    _args_copy[page_idx] = __page
                else:
                    _kwargs_copy['page'] = __page

                __limit = limit
                if (_exceeded := (__page - _page + 1) * limit - _limit) > 0:
                    __limit -= _exceeded
                if limit_idx is not None and limit_idx < len(args):
                    _args_copy[limit_idx] = __limit
                else:
                    _kwargs_copy['limit'] = __limit

                # Chance of failure is much higher in a batch.
                __result = _retry(3)(_func)(*_args_copy, **_kwargs_copy)
                if _result is None:
                    _result = __result
                else:
                    _result.extend(__result)

                if (isinstance(_result, ResultPage)
                        and isinstance(__result, ResultPage)):
                    _result.has_next = __result.has_next

                if _exceeded >= 0 or len(__result) < limit:
                    break
                __page += 1

            return _result

        return wrapper

    if func is not None:
        return dec(func)

    return dec


class ServerClient(_BaseServerClient):
    ResponseCode = ServerResponseCode
    ExceptionMap = ORDER_EXCEPTION_MAPPING
    IGNORE_ERROR_CODES = (OrderResponseCode.BALANCE_NOT_ENOUGH,
                          OrderResponseCode.ONLY_MAKER_KILL_ORDER,
                          OrderResponseCode.LESS_THAN_LEAST_AMOUNT,
                          OrderResponseCode.NOT_COMPLETELY_KILL_ORDER,
                          CancelOrderResponseCode.ORDER_NOT_FOUND,
                          CancelOrderResponseCode.INVALID_ARGUMENT)

    def __init__(self, logger: Logger = None):
        self._client = JsonRPC2Client(
            config['CLIENT_CONFIGS']['server']['url'],
            headers=REQUEST_HEADERS
        )

    @_retry(3)
    def get_user_balances(self, user_id: int, asset: Optional[str] = None,
                          *, account_id: int = SPOT_ACCOUNT_ID
                          ) -> Dict[str, Dict[str, Decimal]]:
        """
                    account id default value is 0.
                    get one user asset list. asset is optional.
        """
        args = [user_id, account_id]
        if asset is not None:
            args.append(asset)
        return _str_to_decimal(
            self.do_request('asset.query', *args), depth=2)

    def get_user_locked_assets(self, user_id: int,
                               *, account_id: int = SPOT_ACCOUNT_ID
                               ) -> Dict[str, Decimal]:
        return _str_to_decimal(
            self.do_request('asset.query_lock', user_id, account_id), depth=1)

    def get_user_accounts_balances(self, user_id: int
                                   ) -> Dict[str, Dict[str,
    Dict[str, Decimal]]]:
        """
                    get one user all asset list.
                    include margin and investment account.
                    not include contract account.
        {'0': {'BTC': {'available': '100.********', 'frozen': '0.********'}}}
        """
        return _str_to_decimal(
            self.do_request('asset.query_all', user_id), depth=3)

    def get_users_balances(self, account_id: int, user_ids: Iterable[int]
                           ) -> Dict[int, Dict[str, Dict[str, Decimal]]]:
        """
        `account_id` must be greater than 0.
        `user_ids` is an array.
        get users asset list.
        response:
        {766: {'USDT': {'available': '0.********', 'frozen': '0.********'}}}
        """
        if account_id <= 0:
            raise NotImplementedError('`account_id` must be greater than 0')
        user_ids = list(user_ids)
        if len(user_ids) > 200:
            raise ValueError('user_ids should not exceed 200')
        res = self.do_request('asset.query_users', account_id, user_ids)
        result = {int(user_id): value for user_id, value in res.items()}
        return _str_to_decimal(result, depth=3)

    @batched
    @_retry(3)
    def get_user_balance_history(self, user_id: int, asset: str = '',
                                 *, account_id: int = SPOT_ACCOUNT_ID,
                                 business: Union[str, BalanceBusiness, list[Union[str, BalanceBusiness]]] = '',
                                 start_time: int = 0, end_time: int = 0,
                                 page: int = 0, limit: int = 100
                                 ) -> ResultPage[Dict[str, Any]]:
        if isinstance(business, BalanceBusiness):
            business = business.value
        if isinstance(business, list):
            _tmp = []
            for _b in business:
                if isinstance(_b, BalanceBusiness):
                    _tmp.append(_b.value)
                else:
                    _tmp.append(_b)
            business = _tmp

        res = self.do_request('asset.history', user_id, account_id, asset,
                              business, start_time, end_time,
                              *_page_to_offset(page, limit))
        return ResultPage.from_response(res, page, limit)

    @batched(max_limit=101)
    def user_pending_orders(self, user_id: int, market: str = None, *,
                            account_id: int = 0, side: int = 0,
                            page: int, limit: int
                            ) -> ResultPage[Dict[str, Any]]:
        res = self.do_request('order.pending', user_id, account_id, market,
                              side, *_page_to_offset(page, limit))
        return ResultPage.from_response(res, page, limit)

    def user_pending_orders_by_client_id(self, user_id: int, market: str = None, *,
                                         client_id: str, account_id: int = 0, side: int = 0,
                                         page: int = 1, limit: int = 10,
                                         ) -> ResultPage[Dict[str, Any]]:
        res = self.do_request('order.pending', user_id, account_id, market,
                              side, *_page_to_offset(page, limit), client_id)
        return ResultPage.from_response(res, page, limit)

    def pending_order_detail(self, market: str, order_id: int):
        return self.do_request('order.pending_detail', market, order_id)

    def pending_order_stop_detail(self, market: str, order_id: int):
        return self.do_request('order.pending_stop_detail', market, order_id)

    @batched(max_limit=101)
    def user_finished_orders(self, user_id: int, market: str = '',
                             *,
                             account_id: int = SPOT_ACCOUNT_ID,
                             side: int = 0, start_time: int,
                             end_time: int, stop_order_id: Optional[int], page: int, limit: int
                             ) -> ResultPage[Dict[str, Any]]:
        if stop_order_id:
            res = self.do_request('order.finished', user_id, account_id, market,
                                  side, start_time, end_time,
                                  *_page_to_offset(page, limit), stop_order_id)
        else:
            res = self.do_request('order.finished', user_id, account_id, market,
                                  side, start_time, end_time,
                                  *_page_to_offset(page, limit))
        return ResultPage.from_response(res, page, limit)

    def finished_order_detail(self, user_id: int, order_id: int):
        return self.do_request('order.finished_detail', user_id, order_id)

    def finished_stop_order_detail(self, user_id: int, order_id: int):
        return self.do_request('order.finished_stop_detail', user_id, order_id)

    def user_order_deals(self, user_id: int, order_id: int,
                         *, account_id: int = SPOT_ACCOUNT_ID,
                         page: int, limit: int) -> ResultPage[Dict[str, Any]]:
        res = self.do_request('order.deals', user_id, account_id, order_id,
                              *_page_to_offset(page, limit))
        return ResultPage.from_response(res, page, limit)

    def market_order_depth(self, *, market: str, limit: int, interval: str):
        return self.do_request('order.depth', market, limit, interval)

    @batched(max_limit=101)
    def market_book_orders(self, market: str, side, page: int, limit: int
                           ) -> ResultPage[Dict[str, Any]]:
        res = self.do_request('order.book', market, side,
                              *_page_to_offset(page, limit))
        return ResultPage.from_response(res, page, limit, data_attr='orders')
    
    def market_book_orders_lite(self, market: str, page: int, limit: int, side: int = 0
                                ) -> Dict[str, Any]:
        """
        side: 0: both, 1: buy, 2: sell
        当side为0时，limit为ask_orders和bid_orders分别取limit，
        当side为1或2时，limit为ask_orders或bid_orders取limit
        return:
        {
            'ask_orders': [{'user_id': 871, 'account': 0, 'price': '1000', 
            'amount': '26483.********', 'left': '26338.********'}, {...}], 
            'bid_orders': [{...}, {...}, {...}], 
            'ask_total': 2, 
            'bid_total': 3, 
            'offset': 0, 'limit': 3, 
            'last': '999.9'
            }
        """
        offset = (page-1)*limit
        res = self.do_request('order.book_lite', market, side, offset, limit)
        return res

    @batched(max_limit=101)
    def market_stop_book_orders(self, market: str, side, page: int,
                                limit: int) -> ResultPage[Dict[str, Any]]:
        res = self.do_request('order.stop_book', market, side,
                              *_page_to_offset(page, limit))
        return ResultPage.from_response(res, page, limit, data_attr='orders')

    @batched(max_limit=101)
    def user_pending_stop_orders(self, user_id, market: str = None,
                                 *, account_id: int = 0,
                                 side: int = 0, page: int,
                                 limit: int) -> ResultPage[Dict[str, Any]]:
        res = self.do_request('order.pending_stop', user_id, account_id,
                              market, side, *_page_to_offset(page, limit))
        return ResultPage.from_response(res, page, limit)

    @batched(max_limit=101)
    def user_finished_stop_orders(self, user_id: int, market: str = '',
                                  *, account_id: int = 0,
                                  side: int = 0, status: int = 0, start_time: int,
                                  end_time: int, page: int, limit: int
                                  ) -> ResultPage[Dict[str, Any]]:
        res = self.do_request('order.finished_stop', user_id, account_id,
                              market, side, start_time, end_time,
                              *_page_to_offset(page, limit), status)
        return ResultPage.from_response(res, page, limit)

    def get_all_indices(self) -> Dict[str, Decimal]:
        ret = self.index_list()
        return {market: Decimal(data['index'])
                for market, data in ret.items()}

    @_retry(2)
    def index_list(self) -> Dict[str, Any]:
        """
                return:
                {
                    'ETHUSDT': {
                        'time': *************,
                        'index': '3470.36',
                        'sources': {'okex': {'ban_interval': 0, 'ban_reason': 0, 'ban_start': 0, 'exchange': 'binance',
                         'last': '3400.********', 'time': Decimal('**********.9660001'), 'weight': '1.********'}, },
                            },
                    ...
                }
                """
        return self.do_request('index.list')

    def get_market_index(self, market: str) -> Decimal:
        return Decimal(self.do_request('index.query', market)['index'])

    def trade_net_rank(self, *, market: list, start_time: int, end_time: int):
        return self.do_request('trade.net_rank', market, start_time, end_time)

    def trade_amount_rank(self, *, market: list, start_time: int, end_time: int
                          ):
        return self.do_request('trade.amount_rank', market, start_time,
                               end_time)

    @_retry(3)
    def market_kline(self, *, market: str, start_time: int, end_time: int,
                     interval: int):
        """
        "result": [
                [
                    1492358400, time
                    "7000.00",  open
                    "8000.0",   close
                    "8100.00",  highest
                    "6800.00",  lowest
                    "1000.00",  amount
                    "123456.78" volume
                ]
            ]
        """
        return self.do_request('market.kline', market, start_time, end_time,
                               interval)

    def market_last(self, market: str) -> Decimal:
        return Decimal(self.do_request('market.last', market))

    def market_status(self, *, market: str, period):
        return self.do_request('market.status', market, period)

    def get_all_market_tickers(self) -> Dict[str, Dict[str, Any]]:
        data = self.market_status(market="", period=86400)
        tickers = {
            item["market"]: 
                {
                    'vol': item["volume"],
                    'low': item["low"],
                    'open': item["open"],
                    'high': item["high"],
                    'last': item["last"],
                    'buy': '0',
                    'buy_amount': item["buy_total"],
                    'sell': '0',
                    'sell_amount': item["sell_total"]
                } for item in data}
        return tickers

    def market_deals(self, *, market: str, limit: int, last_id: int):
        return self.do_request('market.deals', market, limit, last_id)

    @batched(max_limit=101)
    def market_user_deals(self, user_id: int, market: str = '',
                          *, account_id: int = 0,
                          side: int = 0, start_time: int,
                          end_time: int, page: int, limit: int
                          ) -> ResultPage[Dict[str, Any]]:
        res = self.do_request('market.user_deals', user_id, account_id, market,
                              side, start_time, end_time,
                              *_page_to_offset(page, limit))
        return ResultPage.from_response(res, page, limit)

    def market_status_today(self, *, market: str):
        return self.do_request('market.status_today', market)

    def market_list(self):
        return self.do_request('market.list')

    def market_deals_ext(self, *, market: str, limit: int, last_id: int, real: bool = True):
        """real字段为server新添加的字段，用于筛选是否包含自成交数据，False时包含自成交数据，默认为True"""
        return self.do_request('market.deals_ext', market, limit, last_id, real)

    def market_summary(self, markets):
        return self.do_request('market.summary', markets)

    def market_deal_rank(self, *, market: str, start_time: int, end_time: int,
                         side: int, top_num):
        return self.do_request('market.deal_rank', market, start_time,
                               end_time, side, top_num)

    def add_user_balance(self, user_id: int, asset: str, amount: AmountType,
                         business: Union[str, BalanceBusiness],
                         business_id: int, detail: Dict[str, Any] = None,
                         *, account_id: int = SPOT_ACCOUNT_ID) -> bool:
        """
        important: 多次相同 user_id, asset, business, business_id 的操作只会成功一次
                (server目前只支持两天内防重入检测)
                 user_id: 用户 ID，Integer
                 asset: 资产名，String
                 business: 业务类型，String
                 business_id: 业务 ID，Integer,
                 amount: 变化金额，String, 负数表示扣除
                 detail: json 对象
        """
        if not amount:
            return False
        if isinstance(business, BalanceBusiness):
            business = business.value
        try:
            detail = _update_detail_price(asset, detail)
            self.do_request('asset.update', user_id, account_id, asset,
                            business, business_id, str(amount), detail)
        except self.BadResponse as e:
            if e.code == ServerResponseCode.DUPLICATE_BALANCE_UPDATE:
                current_app.logger.warning(
                    f'{user_id}-{amount}{asset}-{business}-{business_id} DUPLICATE_BALANCE_UPDATE'
                )
                return False
            current_app.logger.warning(
                f'{user_id}-{amount}{asset}-{business}-{business_id} BALANCE_UPDATE_FAILED: {e!r}'
            )
            raise
        # send balance update KAFKA message here.
        _send_balance_update_msg(user_id, account_id, asset, business, business_id, amount, detail)
        return True

    def batch_add_user_balance(self, data: List[Dict]):
        """
        批量变更资产，该rpc用于需要原子性的场景，不要用于批量变更资产。
        server按顺序执行，一旦失败立即返回，不会回滚已执行的操作，因此不是原子的。但在参数正确的情况下，
        增加资产通常不会失败，调用方应传递可以原子执行的操作，如扣减、增加资产和增加、增加资产这两种情况。
        data为add_user_balance方法关键字传参时的字典列表。“““
        """
        params = []
        for item in data:
            if not Decimal(item['amount']):
                continue
            business = item['business']
            if isinstance(business, BalanceBusiness):
                business = business.value
            
            detail = _update_detail_price(item['asset'], item.get('detail', {}))
            params.append([
                item['user_id'],
                item.get('account_id', 0),
                item['asset'],
                business,
                item['business_id'],
                str(item['amount']),
                detail
            ])
        if not params:
            return
        result = self.do_request('asset.update_batch', *params)
        # 批量执行结果在result字段以列表方式返回
        for i, r in enumerate(result):
            if err := r.get('error'):
                raise self.BadResponse(err.get('code', -1), err)
            if (code := r.get('code', 0)) != 0:
                raise self.BadResponse(code, r)

            param = params[i]
            _send_balance_update_msg(*param)

    def new_batch_add_user_balance(self, data):
        # batch_add_user_balance 的新版本，使用 server.batch 替代旧的 asset.update_batch
        params = []
        for item in data:
            if not Decimal(item['amount']):
                continue
            business = item['business']
            if isinstance(business, BalanceBusiness):
                business = business.value

            detail = _update_detail_price(item['asset'], item.get('detail', {}))
            params.append(
                ['asset.update', [
                    item['user_id'],
                    item.get('account_id', 0),
                    item['asset'],
                    business,
                    item['business_id'],
                    str(item['amount']),
                    detail
                ]]
            )
        if not params:
            return
        result = self.do_request('server.batch', *params)
        for i, r in enumerate(result):
            if err := r.get('error'):
                code = err.get('code')
                raise self.BadResponse(code or -1, err)
            param = params[1][i]
            _send_balance_update_msg(*param)
        return True

    @_retry(3)
    def add_and_lock_user_balance(self, user_id: int, asset: str, amount: AmountType,
                                  business: Union[str, BalanceBusiness],
                                  business_id: int, detail: Dict[str, Any] = None,
                                  *, account_id: int = SPOT_ACCOUNT_ID) -> bool:
        """增加并锁定用户资产，该接口调用server的批量执行rpc，执行增加和锁定两条命令。
           参考batch_add_user_balance, 为了保证原子性，amount必须大于0"""
        if not amount:
            return False
        if Decimal(amount) < 0:
            raise ValueError('amount must be positive')
        if isinstance(business, BalanceBusiness):
            business = business.value

        detail = _update_detail_price(asset, detail)
        asset_update = ['asset.update', [user_id, account_id, asset, business, business_id, str(amount), detail]]
        asset_lock = ['asset.lock', [user_id, account_id, asset, business, business_id, str(amount)]]
        result = self.do_request('server.batch', asset_update, asset_lock)

        for i, r in enumerate(result):
            if err := r.get('error'):
                code = err.get('code')
                if code == ServerResponseCode.DUPLICATE_BALANCE_UPDATE:
                    current_app.logger.warning(
                        f'{user_id}-{amount}{asset}-{business}-{business_id} DUPLICATE_BALANCE_UPDATE_LOCK'
                    )
                    return False
                current_app.logger.warning(
                    f'{user_id}-{amount}{asset}-{business}-{business_id} BALANCE_UPDATE_LOCK_FAILED: {err}'
                )
                raise self.BadResponse(code or -1, err)
        
            if i == 0: # asset.update
                param = asset_update[1]
                _send_balance_update_msg(*param)
        return True

    @_retry(3)
    def unlock_and_sub_user_balance(self, user_id: int, asset: str,
                                    amount: AmountType,
                                    business_id: int, detail: Dict[str, Any] = None,
                                    *, account_id: int = SPOT_ACCOUNT_ID,
                                    sub_amount: AmountType = None,
                                    unlock_bus: BalanceBusiness,
                                    sub_bus: BalanceBusiness
                                    ):
        """解锁并扣减用户资产，该接口调用server的批量执行rpc，为了保证原子性，amount必须大于0"""
        if not amount:
            return False
        if (amount := Decimal(amount)) < 0:
            raise ValueError('amount must be positive')
        if sub_amount is None:
            sub_amount = amount
        if (sub_amount := Decimal(sub_amount)) < 0:
            raise ValueError('sub_amount must be positive')

        detail = _update_detail_price(asset, detail)
        unlock = ['asset.unlock',
                  [user_id, account_id, asset, unlock_bus.value, business_id, str(amount)]]
        update = ['asset.update',
                  [user_id, account_id, asset, sub_bus.value, business_id, str(-sub_amount), detail]]
        batch_list = [unlock, update]
        err_code_list = [BatchResponseCode.UNLOCK_BALANCE_ERROR, BatchResponseCode.UPDATE_BALANCE_ERROR]
        result = self.do_request('server.batch', *batch_list)
        for idx, r in enumerate(result):
            if err := r.get('error'):
                code = err.get('code')
                current_app.logger.error(
                    f'{user_id}-{amount}{asset}-{business_id} UNLOCK_AND_SUB_ERROR: {err}'
                )
                raise self.BatchBadResponse(code or -1, err, err_code_list[idx])
        
            if idx == 1: # asset.update
                param = update[1]
                _send_balance_update_msg(*param)
        return True

    def lock_user_balance(self, user_id: int, asset: str, amount: str,
                          business: Union[str, BalanceBusiness],
                          business_id: int,
                          *, account_id: int = SPOT_ACCOUNT_ID):
        if isinstance(business, BalanceBusiness):
            business = business.value
        return self.do_request('asset.lock', user_id, account_id, asset,
                               business, business_id, amount)

    def unlock_user_balance(self, user_id: int, asset: str, amount: str,
                            business: Union[str, BalanceBusiness],
                            business_id: int,
                            *, account_id: int = SPOT_ACCOUNT_ID):
        if isinstance(business, BalanceBusiness):
            business = business.value
        return self.do_request('asset.unlock', user_id, account_id, asset,
                               business, business_id, amount)

    def silent_add_user_balance(self, user_id: int, asset: str, amount: AmountType,
                           business: Union[str, BalanceBusiness],
                           business_id: int, detail: Dict[str, Any] = None,
                           *, account_id: int = SPOT_ACCOUNT_ID) -> bool:
        """同add_user_balance，但不记录流水"""
        if not amount:
            return False
        if isinstance(business, BalanceBusiness):
            business = business.value
        option = 1 # no balance history
        try:
            self.do_request('asset.update', user_id, account_id, asset,
                            business, business_id, str(amount), detail or {}, option)
        except self.BadResponse as e:
            if e.code == ServerResponseCode.DUPLICATE_BALANCE_UPDATE:
                return False
            raise
        return True

    def put_limit_order(self, *, user_id: int, account_id: int = 0,
                        market: str, side: int, amount: str, price,
                        taker_fee_rate, maker_fee_rate, source, fee_asset,
                        fee_discount,
                        option: int = 0, client_id=''):
        try:
            return self.do_request('order.put_limit', user_id, account_id,
                                   market, side, amount, price, taker_fee_rate,
                                   maker_fee_rate, source, fee_asset,
                                   fee_discount, option, client_id)
        except self.BadResponse as e:
            raise ORDER_EXCEPTION_MAPPING[e.code]

    def put_market_order(self, *, user_id: int, account_id: int = 0,
                         market: str, side, amount: str, taker_fee_rate,
                         source, fee_asset, fee_discount, option: int = 0,
                         client_id=''):
        try:
            return self.do_request('order.put_market', user_id, account_id,
                                   market, side, amount, taker_fee_rate,
                                   source, fee_asset, fee_discount, option,
                                   client_id)
        except self.BadResponse as e:
            raise ORDER_EXCEPTION_MAPPING[e.code]

    def order_modify(self, user_id: int, market: str, order_id: int, amount: str, price: str) -> Dict[str, Any]:
        try:
            return self.do_request('order.modify', user_id, market,
                                   order_id, amount, price)
        except self.BadResponse as e:
            raise ORDER_EXCEPTION_MAPPING[e.code]

    def order_modify_stop(self, user_id: int, market: str, order_id: int, amount: str, price: str, stop_price: str) -> \
    Dict[str, Any]:
        try:
            return self.do_request('order.modify_stop', user_id, market,
                                   order_id, amount, price, stop_price)
        except self.BadResponse as e:
            raise STOP_ORDER_EXCEPTION_MAPPING[e.code]

    def put_stop_limit_order(self, *, user_id: int, account_id: int = 0,
                             market: str, side, amount: str, stop_price, price,
                             taker_fee_rate, maker_fee_rate, source, fee_asset,
                             fee_discount, option: int = 0, client_id=''):
        try:
            return self.do_request('order.put_stop_limit', user_id, account_id,
                                   market, side, amount, stop_price, price,
                                   taker_fee_rate, maker_fee_rate, source,
                                   fee_asset, fee_discount, option, client_id)
        except self.BadResponse as e:
            raise STOP_ORDER_EXCEPTION_MAPPING[e.code]

    def put_stop_market_order(self, *, user_id: int, account_id: int = 0,
                              market: str, side, amount: str, stop_price,
                              taker_fee_rate, maker_fee_rate, source, fee_asset,
                              fee_discount, option: int = 0, client_id=''):
        try:
            return self.do_request('order.put_stop_market', user_id, account_id,
                                   market, side, amount, stop_price,
                                   taker_fee_rate, maker_fee_rate, source,
                                   fee_asset, fee_discount, option, client_id)
        except self.BadResponse as e:
            raise STOP_ORDER_EXCEPTION_MAPPING[e.code]

    def cancel_user_stop_order(self, user_id: int, market: str, order_id: int,
                               source_option: int = USER_ORDER_SOURCE):
        try:
            return self.do_request('order.cancel_stop', user_id, market,
                                   order_id, source_option)
        except self.BadResponse as e:
            raise CANCEL_ORDER_EXCEPTION_MAPPING[e.code]

    def cancel_user_all_stop_order(self, *, user_id: int, account_id: int,
                                   market: str, side: int = 0, source_option: int = USER_ORDER_SOURCE):
        return self.do_request('order.cancel_stop_all', user_id, account_id,
                               market, side, source_option)

    def cancel_user_order(self, user_id: int, market: str, order_id: int,
                          source_option: int = USER_ORDER_SOURCE):
        try:
            return self.do_request('order.cancel', user_id, market, order_id, source_option)
        except self.BadResponse as e:
            raise CANCEL_ORDER_EXCEPTION_MAPPING[e.code]

    def cancel_user_all_order(self, user_id: int, account_id: int,
                              market: Optional[str], side: int = 0, source_option: int = USER_ORDER_SOURCE):
        """"""
        return self.do_request('order.cancel_all', user_id, account_id,
                               market,
                               side, source_option)

    def cancel_batch_user_order(self, user_id: int, market: str, order_ids: Iterable[int],
                                source_option: int = USER_ORDER_SOURCE):
        """server后续不允许market传None，必须传market参数"""
        try:
            return self.do_request('order.cancel_batch', user_id, market, order_ids, source_option)
        except self.BadResponse as e:
            raise CANCEL_ORDER_EXCEPTION_MAPPING[e.code]

    def cancel_batch_user_stop_order(self, user_id: int, market: str, order_ids: Iterable[int],
                                     source_option: int = USER_ORDER_SOURCE):
        return self.do_request('order.cancel_stop_batch', user_id, market, order_ids, source_option)

    def market_self_deal(self, market: str, amount: AmountType,
                         price: AmountType, side: int):
        return self.do_request('market.self_deal',
                               market, str(amount), str(price), side)

    def call_stop_cancel(self, market: str):
        """竞价模式停止撤单"""
        return self.do_request('call.stop_cancel', market)

    def call_execute(self, market: str):
        return self.do_request('call.execute', market)

    def call_start(self, market: str):
        return self.do_request('call.start', market)

    def update_assets(self):
        return self.do_request('config.update_asset')

    def update_market(self):
        return self.do_request('config.update_market')

    def update_index(self):
        return self.do_request('config.update_index')

    def users_trade_volume(self,
                           markets: Iterable[str],
                           user_ids: Iterable[int],
                           start_time: int,
                           end_time: int,
                           ):
        # 前闭后开
        return self.do_request('trade.users_volume', markets, user_ids,
                               start_time, end_time)

    def query_deal_summary(self, start_time: int, end_time: int, market: str | None = None):
        if market:
            return self.do_request('trade.deal_summary', start_time, end_time, market)
        return self.do_request('trade.deal_summary', start_time, end_time)

    def monitor_list_scopes(self) -> List[str]:
        return self.do_request('monitor.list_scope')

    def monitor_list_keys(self, scope: str) -> List[str]:
        return self.do_request('monitor.list_key', scope)

    def monitor_list_hosts(self, scope: str, key: str) -> List[str]:
        return self.do_request('monitor.list_host', scope, key)

    def monitor_query_minute(self, scope: str, key: str, host: str, count: int
                             ) -> List[Tuple[int, int]]:
        return list(map(tuple, self.do_request('monitor.query_minute',
                                               scope, key, host, count)))

    def monitor_query_daily(self, scope: str, key: str, host: str, count: int
                            ) -> List[Tuple[int, int]]:
        return list(map(tuple, self.do_request('monitor.query_daily',
                                               scope, key, host, count)))

    def asset_query_business(self, user_id: int, asset: str,
                             business: Union[str, BalanceBusiness],
                             business_id: int, *,
                             account_id: int = SPOT_ACCOUNT_ID,
                             lock_flag: QueryBusinessFlag = QueryBusinessFlag.ASSET,
                             ) -> bool:

        if isinstance(business, BalanceBusiness):
            business = business.value
        return self.do_request('asset.query_business', user_id, account_id,
                               asset, business, business_id, lock_flag.value)

    def notice_user_message(self, user_id: int, channel_id: int, data: Dict):
        """
        data = {'title': 'test', 'url': 'test', 'content': 'test', 'type': 1}
        """
        return self.do_request('notice.user_message', channel_id, user_id, data)

    @_retry(3)
    def update_protect_duration(self, status: bool):
        """更新保护期状态，True-使server进入保护期，False-使server退出保护期"""
        return self.do_request('sys.protection', status)

    def get_protect_status(self) -> Dict:
        """查询server是否处于保护期，{'status': True}， True-是，False-否"""
        return self.do_request('sys.protection_query')

    @_retry(3)
    def set_market_maintain_status(self, market: str, status: MarketMaintain.MaintainStatus):
        """设置单市场维护状态"""
        return self.do_request('sys.market_set_status', market, status.value)

    def get_market_maintain_status(self, market: str):
        """获取单市场维护状态"""
        # MARKET_STATUS_START 0
        # MARKET_STATUS_STOP 1
        # MARKET_STATUS_PROTECTION 2
        # MARKET_STATUS_CAUTION_START 3
        # MARKET_STATUS_CAUTION_STOP_CANCEL 4
        return self.do_request('sys.market_get_status', market)


class PerpetualServerClient(_BaseServerClient):
    """
    https://github.com/viabtc/coinex_perpetual_server/blob/master/doc/HTTP-Protocol.md
    """

    ResponseCode = PerpetualResponseCode
    ExceptionMap = PerpetualOrderExceptionMap
    IGNORE_ERROR_CODES = (PerpetualResponseCode.CONTRACT_BALANCE_NOT_ENOUGH,
                          PerpetualResponseCode.CONTRACT_AMOUNT_EXCEED_LIMIT,
                          PerpetualResponseCode.CONTRACT_ORDER_ID_INVALID,
                          PerpetualResponseCode.CONTRACT_INVALID_ARGUMENT)

    def __init__(self, logger: Logger = None):
        self._client = JsonRPC2Client(
            config['CLIENT_CONFIGS']['perpetual_server']['url'],
            headers=REQUEST_HEADERS
        )

    @_retry(2)
    def get_market_status(self, market: Union[None, str] = None, period: int = 86400) -> Dict[str, Any]:
        """market=None时，server返回所有合约市场的信息，注意返回的position_amount字段是刷量后的。
           该接口返回的成交量包含了自成交。server所有数据库的数据都不包含自成交。
        """
        return self.do_request('market.status', market, period)

    @_retry(2)
    def market_kline(
            self, *, market: str, start_time: int, end_time: int, interval: int
    ):
        return self.do_request(
            "market.kline", market, start_time, end_time, interval
        )

    @_retry(2)
    def get_basis_history(self, market: str, start_time: int, end_time: int):
        return self.do_request('market.basis_history', market, start_time, end_time)

    @_retry(2)
    def market_last(self, market: str) -> str:
        return Decimal(self.do_request('market.last', market))

    @_retry(2)
    def market_order_depth(self, *, market: str, limit: int, interval: str):
        """
        {'asks': [['28010.00', '0.9983']],
        'bids': [['28000.00', '0.9929']],
        'last': '28000',
        'time': 1694769601025,
        'sign_price': '28000.00',
        'index_price': '28000.00'}
        """
        return self.do_request('order.depth', market, limit, interval)

    @_retry(2)
    def asset_list(self) -> List[Dict[str, Any]]:
        return self.do_request('asset.list')

    @_retry(2)
    def market_list(self) -> List[Dict[str, Any]]:
        return self.do_request('market.list')

    @_retry(2)
    def market_summary(self, market: str) -> Dict[str, Any]:
        return self.do_request('market.summary', market)

    @_retry(2)
    def big_customer_market_summary(self, market: str, percent: str
                                    ) -> Dict[str, Any]:
        """获取大客户（仓位数量（多仓和空仓）的总排名前{percent}的用户）多空人数"""
        return self.do_request('market.long_short_distributed', market, percent)

    @_retry(2)
    def market_deals_ext(self, *, market: str, limit: int, last_id: int):
        return self.do_request('market.deals_ext', market, limit, last_id)

    @_retry(2)
    def market_insurances(self) -> List[Dict[str, Any]]:
        return self.do_request('market.insurances')

    @_retry(2)
    def limit_config(self) -> Dict[str, List[str]]:
        return self.do_request('market.limit_config')

    @_retry(2)
    def get_preference(self, user_id: int, market: Union[str, None]) -> Dict[str, Any]:
        return self.do_request('market.get_preference', user_id, market)

    @_retry(2)
    def get_settle_switch(self, user_id: int):
        """获取结算开关状态"""
        return self.do_request('market.get_preference', user_id, None)

    @_retry(2)
    def index_list(self) -> Dict[str, Any]:
        """
                return:
                {
                'ETHUSD': {'time': 1736302065000, 'index': '3391.25',
                'sources': [
                {'trade_pair': 'ETH-USD', 'exchange': 'coinbase',
                'url': 'https://api.pro.coinbase.com/products/ETH-USD/trades?limit=20', 'weight': '0.25000000',
                'last_price': '3392.56', 'time': Decimal('1736302067.2449329'), 'last_time_interval': 0,
                'last_update': 1736302065, 'last_update_interval': 1, 'ban_start': 0, 'ban_reason': 0, 'ban_interval': 0},
                ]
                }
                """
        return self.do_request('index.list')

    @_retry(2)
    def premium_history(self, market: str,
                        start_time: int = 0, end_time: int = 0,
                        page: int = 1, limit: int = 10
                        ) -> Dict[str, Any]:
        offset, limit = _page_to_offset(page, limit)
        return self.do_request('market.premium_history',
                               market, start_time, end_time, offset, limit)

    @_retry(2)
    def funding_history(self, market: str,
                        start_time: int = 0, end_time: int = 0,
                        page: int = 1, limit: int = 10
                        ) -> Dict[str, Any]:
        offset, limit = _page_to_offset(page, limit)
        return self.do_request('market.funding_history',
                               market, start_time, end_time, offset, limit)

    def adjust_leverage(self, user_id: int, market: str, position_type: int,
                        leverage: str) -> Dict[str, Any]:
        return self.do_request('market.adjust_leverage', user_id, market,
                               position_type, leverage)

    def order_modify(self, user_id: int, market: str, order_id: int, amount: str, price: str) -> Dict[str, Any]:
        try:
            return self.do_request('order.modify', user_id, market,
                                   order_id, amount, price)
        except self.BadResponse as e:
            raise self.ExceptionMap[e.code]

    def order_modify_stop(self, user_id: int, market: str, order_id: int, amount: str, price: str, stop_price: str) -> \
    Dict[str, Any]:
        try:
            return self.do_request('order.modify_stop', user_id, market,
                                   order_id, amount, price, stop_price)
        except self.BadResponse as e:
            raise self.ExceptionMap[e.code]

    def pending_order_stop_detail(self, market: str, order_id: int):
        return self.do_request('order.pending_stop_detail', market, order_id)

    @_retry(2)
    def get_user_balances(self, user_id: int, asset: Optional[str] = None
                          ) -> Dict[str, Dict[str, Decimal]]:
        args = [user_id]
        if asset is not None:
            args.append(asset)
        return _str_to_decimal(
            self.do_request('asset.query', *args), depth=2)

    def get_users_balances(self, asset: str, user_ids: Iterable[int]
                           ) -> Dict[int, Dict[str, Dict[str, Decimal]]]:
        """
        `asset` must str
        `user_ids` is an array.
        get users asset list.
        response:
        {766: {'available': '0.********', 'frozen': '0.********'}}
        """
        user_ids = list(user_ids)
        if len(user_ids) > 200:
            raise ValueError('user_ids should not exceed 200')
        res = self.do_request('asset.query_users', asset, user_ids)
        result = {int(user_id): {asset: value} for user_id, value in res.items()}
        return _str_to_decimal(result, depth=3)


    def update_insurance(self, asset: str, business: Union[str, BalanceBusiness],
                         amount: AmountType, business_id: int):
        if isinstance(business, BalanceBusiness):
            business = business.value
        return self.do_request('insurance.update', asset, business, business_id, str(amount))

    def add_user_balance(self, user_id: int, asset: str, amount: AmountType,
                         business: Union[str, BalanceBusiness],
                         business_id: int,
                         detail: Dict[str, Any] = None) -> bool:
        """
                 user_id: 用户 ID，Integer
                 asset: 资产名，String
                 business: 业务类型，String
                 business_id: 业务 ID，Integer,
                 多次相同 user_id, asset, business, business_id 的操作只会成功一次
                 amount: 变化金额，String, 负数表示扣除
                 detail: json 对象

        """
        if isinstance(business, BalanceBusiness):
            business = business.value
        detail = _update_detail_price(asset, detail)
        try:
            self.do_request('asset.update', user_id, asset,
                            business, business_id, str(amount), detail)
        except self.BadResponse as e:
            if e.code == self.ResponseCode.CONTRACT_BALANCE_REPEAT:
                return False
            raise
        detail['system'] = 'perpetual'
        _send_balance_update_msg(user_id, 0, asset, business, business_id, amount, detail)
        return True

    @batched
    @_retry(2)
    def get_user_balance_history(self, user_id: int, asset: str = '',
                                 business: Union[str, BalanceBusiness] = '',
                                 start_time: int = 0, end_time: int = 0,
                                 page: int = 0, limit: int = 100
                                 ) -> ResultPage[Dict[str, Any]]:
        if isinstance(business, BalanceBusiness):
            business = business.value
        res = self.do_request('asset.history', user_id, asset, business,
                              start_time, end_time,
                              *_page_to_offset(page, limit))
        return ResultPage.from_response(res, page, limit)

    @batched
    @_retry(2)
    def user_deals(self, user_id: int, market: str, side: int,
                   start_time: int = 0, end_time: int = 0,
                   page: int = 1, limit: int = 10,
                   market_type: int = 0
                   ) -> ResultPage[Dict[str, Any]]:
        res = self.do_request('market.user_deals', user_id, market, side,
                              start_time, end_time,
                              *_page_to_offset(page, limit), market_type)
        return ResultPage.from_response(res, page, limit)

    def put_limit(self, user_id: int, market: str, side: int, amount: str,
                  price: str, taker_fee_rate: str, maker_fee_rate: str,
                  source: str, fee_asset: str, fee_discount: str,
                  effect_type: str, client_id: str = None, stop_loss_take_profit: Union[Dict, None] = None):
        return self.do_request('order.put_limit', user_id, market, side,
                               amount, price, taker_fee_rate, maker_fee_rate,
                               source, fee_asset, fee_discount, effect_type, client_id, stop_loss_take_profit)

    def put_market(self, user_id: int, market: str, side: int, amount: str,
                   taker_fee_rate: str, source: str, fee_asset: str,
                   fee_discount: str, option: int = 0, client_id: str = None,
                   stop_loss_take_profit: Union[Dict, None] = None):
        return self.do_request('order.put_market', user_id, market, side,
                               amount, taker_fee_rate, source, fee_asset,
                               fee_discount, option, client_id, stop_loss_take_profit)

    def limit_close(self, user_id: int, market: str, position_id: int,
                    amount: str, price: str, taker_fee_rate: str,
                    maker_fee_rate: str, source: str, effect_type: int, fee_asset: str = None,
                    fee_discount: str = None):
        return self.do_request('order.limit_close', user_id, market,
                               position_id, amount, price, taker_fee_rate,
                               maker_fee_rate, source, fee_asset, fee_discount,
                               effect_type)

    def market_close(self, user_id: int, market: str, position_id: int, amount: str,
                     taker_fee_rate: str, source: str, fee_asset: str = None,
                     fee_discount: str = None, option: int = 0, client_id: str = None):
        return self.do_request('order.market_close', user_id, market,
                               position_id, amount, taker_fee_rate, source, fee_asset,
                               fee_discount, option, client_id)

    def market_close_all(self, user_id: int, market: str, position_id: int,
                         taker_fee_rate: str, maker_fee_rate: str, source: str):
        return self.do_request('position.market_close_all', user_id, market,
                               position_id, taker_fee_rate, maker_fee_rate, source)

    def put_stop_limit(self, user_id: int, market: str, side: int,
                       stop_type: int, amount: str, stop_price: str,
                       price: str, taker_fee_rate: str, maker_fee_rate: str,
                       source: str, fee_asset: str, fee_discount: str,
                       effect_type: int, client_id: str = None, stop_loss_take_profit: Union[Dict, None] = None):
        return self.do_request('order.put_stop_limit', user_id, market, side,
                               stop_type, amount, stop_price, price,
                               taker_fee_rate, maker_fee_rate, source,
                               fee_asset, fee_discount, effect_type, client_id, stop_loss_take_profit)

    def put_stop_market(self, user_id: int, market: str, side: int,
                        stop_type: int, amount: str, stop_price: str,
                        taker_fee_rate: str, maker_fee_rate: str, source: str, fee_asset: str,
                        fee_discount: str, option: int = 0, client_id: str = None,
                        stop_loss_take_profit: Union[Dict, None] = None):
        return self.do_request('order.put_stop_market', user_id, market, side,
                               stop_type, amount, stop_price, taker_fee_rate, maker_fee_rate,
                               source, fee_asset, fee_discount, option, client_id, stop_loss_take_profit)

    def cancel_order(self, user_id: int, market: str, order_id: int):
        return self.do_request('order.cancel', user_id, market, order_id)

    def cancel_batch_orders(self, user_id: int, market: str, order_ids: List[int]):
        """server后续不允许market传None，必须传market参数"""
        return self.do_request('order.cancel_batch', user_id, market, order_ids)

    def cancel_batch_stop_orders(self, user_id: int, market: Optional[str], order_ids: List[int]):
        return self.do_request('order.cancel_stop_batch', user_id, market, order_ids)

    def cancel_all(self, user_id: int, market: str | None, side=ORDER_BOTH_SIDE):
        """market为None为撤销用户所有市场的订单"""
        return self.do_request('order.cancel_all', user_id, market, side)

    @batched(max_limit=101)
    @_retry(2)
    def pending_order(self, user_id: int, market: Optional[str], side: int = 0,
                      page: int = 1, limit: int = 10):
        offset, limit = _page_to_offset(page, limit)
        return self.do_request('order.pending', user_id, market, side, offset,
                               limit)

    @batched(max_limit=101)
    @_retry(2)
    def pending_stop(self, user_id: int, market: Optional[str], side: int = 0,
                     page: int = 1, limit: int = 10):
        offset, limit = _page_to_offset(page, limit)
        return self.do_request('order.pending_stop', user_id, market, side,
                               offset, limit)

    def cancel_stop(self, user_id: int, market: str, order_id: int):
        return self.do_request('order.cancel_stop', user_id, market, order_id)

    def cancel_stop_all(self, user_id: int, market: str | None, side=ORDER_BOTH_SIDE):
        return self.do_request('order.cancel_stop_all', user_id, market, side)

    def pending_order_detail(self, market: str, order_id: int):
        return self.do_request('order.pending_detail', market, order_id)

    @batched
    @_retry(2)
    def order_finished(self, user_id: int, market: str, side: int,
                       start_time: int, end_time: int, stop_order_id: Optional[int],
                       page: int, limit: int, market_type: int = PERPETUAL_ALL_MARKET_TYPE
                       ) -> ResultPage[Dict[str, Any]]:
        if stop_order_id:
            res = self.do_request('order.finished', user_id, market, side,
                                  start_time, end_time,
                                  *_page_to_offset(page, limit), stop_order_id, market_type)
        else:
            # 无 stop_order_id 传 0
            res = self.do_request('order.finished', user_id, market, side,
                                  start_time, end_time,
                                  *_page_to_offset(page, limit), 0, market_type)
        return ResultPage.from_response(res, page, limit)

    @batched
    @_retry(2)
    def finished_stop(self, user_id: int, market: str, side: int,
                      start_time: int, end_time: int, page: int, limit: int, status: int = 0,
                      market_type: int = PERPETUAL_ALL_MARKET_TYPE
                      ) -> ResultPage[Dict[str, Any]]:
        res = self.do_request('order.finished_stop', user_id, market, side,
                              start_time, end_time,
                              *_page_to_offset(page, limit), status, market_type)
        return ResultPage.from_response(res, page, limit)

    def finished_order_detail(self, user_id: int, order_id: int):
        return self.do_request('order.finished_detail', user_id, order_id)

    def finished_stop_order_detail(self, user_id: int, order_id: int):
        return self.do_request('order.finished_stop_detail', user_id, order_id)

    @_retry(2)
    def order_deals(self, user_id: int, order_id: int, page, limit):
        offset, limit = _page_to_offset(page, limit)
        return self.do_request('order.deals', user_id, order_id, offset, limit)

    @_retry(2)
    def position_pending(self, user_id: int, market: str = None):
        return self.do_request('position.pending', user_id, market)

    @_retry(2)
    def position_expect(self, user_id: int,
                        market: str,
                        side: OrderSideType,
                        price: str,
                        taker_fee_rate: str
                        ):
        return self.do_request('position.expect', user_id, market, side, price, taker_fee_rate)



    @batched
    @_retry(2)
    def position_finished(self, user_id: int, market: Union[str, List] = '',
                          start_time: int = 0, end_time: int = 0,
                          page: int = 1, limit: int = 10, side: Optional[int] = 0):
        offset, limit = _page_to_offset(page, limit)
        return self.do_request('position.finished', user_id, market,
                               start_time, end_time, offset, limit, side)

    @batched
    @_retry(2)
    def position_funding(self, user_id: int, market: Union[str, List] = '',
                         start_time: int = 0, end_time: int = 0,
                         page: int = 1, limit: int = 10, side: Optional[int] = 0):
        res = self.do_request('position.funding', user_id, market, start_time,
                              end_time, *_page_to_offset(page, limit), side)
        return ResultPage.from_response(res, page, limit)

    @_retry(2)
    def position_stop_loss_take_profit_history(self, user_id: int, position_id: int,
                                               page: int = 1, limit: int = 10):
        offset, limit = _page_to_offset(page, limit)
        return self.do_request('position.stoploss_takeprofit_history', user_id, position_id, offset, limit)

    def adjust_margin(self, user_id: int, market: str, margin_type: int,
                      amount: str):
        return self.do_request('position.adjust_margin', user_id, market,
                               margin_type, amount)

    @_retry(2)
    def position_list(self, market: str, side: int = 1, page: int = 1,
                      limit: int = 10):
        offset, limit = _page_to_offset(page, limit)
        return self.do_request('position.list', market, side, offset, limit)

    @_retry(2)
    def position_deals(self, user_id: int, position_id: int, page: int = 1,
                       limit: int = 10):
        offset, limit = _page_to_offset(page, limit)
        return self.do_request('position.deals', user_id, position_id, offset,
                               limit)

    def position_stop_loss(self, user_id: int, market: int, position_id: int,
                           stop_type: int, stop_loss_price: str, taker_fee_rate: str,
                           maker_fee_rate: str):
        return self.do_request('position.stop_loss', user_id, market, position_id,
                               stop_type, stop_loss_price, taker_fee_rate,
                               maker_fee_rate)

    def position_take_profit(self, user_id: int, market: int, position_id: int,
                             stop_type: int, take_profit_price: str, taker_fee_rate: str,
                             maker_fee_rate: str):
        return self.do_request('position.take_profit', user_id, market, position_id,
                               stop_type, take_profit_price, taker_fee_rate,
                               maker_fee_rate)

    def position_close_all(self, user_id: int, taker_fee_rate: str,
                           maker_fee_rate: str, source: str):
        return self.do_request('position.close_all', user_id, taker_fee_rate,
                               maker_fee_rate, source)

    def update_preference_special(self):
        return self.do_request('config.update_preference_special')

    @_retry(2)
    def query_history_all(self, user_id: int, asset: str = '',
                          business: Union[str, BalanceBusiness] = '',
                          start_time: int = 0, end_time: int = 0,
                          page: int = 1, limit: int = 100
                          ) -> ResultPage[Dict[str, Any]]:
        if isinstance(business, BalanceBusiness):
            business = business.value
        res = self.do_request('asset.history_all', user_id, asset, business,
                              start_time, end_time, *_page_to_offset(page, limit))

        return ResultPage.from_response(res, page, limit)

    @_retry(2)
    def get_asset_summary(self, asset: str):
        # TODO: server这个接口耗时过长，尽量不要使用, 目前只有合约对账使用了，由于server的快照信息中不包含transfer相关数据，
        # TODO: 还需要server优化处理
        return self.do_request('asset.summary', asset)

    @_retry(2)
    def get_fee_asset_summary(self, asset: str):
        return self.do_request('asset.fee_asset_summary', asset)

    @_retry(2)
    def query_order_book(self, market: str, side: int,
                         page: int = 1, limit: int = 100):
        offset, limit = _page_to_offset(page, limit)
        return self.do_request('order.book', market, side, offset, limit)

    def market_book_orders(self, market: str, side, page: int, limit: int
                           ) -> ResultPage[Dict[str, Any]]:
        res = self.do_request('order.book', market, side,
                              *_page_to_offset(page, limit))
        return ResultPage.from_response(res, page, limit, data_attr='records')

    @_retry(2)
    def query_stop_order_book(self, market: str, side: int,
                              page: int = 1, limit: int = 100):
        offset, limit = _page_to_offset(page, limit)
        return self.do_request('order.stop_book', market, side, offset, limit)

    @_retry(2)
    def query_latest_deals(self, market: str, limit: int = 100,
                           last_id: int = 0):
        last_id = max(0, last_id)
        return self.do_request('market.deals_ext', market, limit, last_id)

    @_retry(2)
    def query_net_rank(self, market: Union[str, List[str]],
                       start_time: int = 0, end_time: int = 0):
        if isinstance(market, str):
            market = [market]
        return self.do_request('trade.net_rank', market, start_time, end_time)

    @_retry(2)
    def query_amount_rank(self, market: Union[str, List[str]],
                          start_time: int = 0, end_time: int = 0):
        if isinstance(market, str):
            market = [market]
        return self.do_request('trade.amount_rank', market, start_time,
                               end_time)

    def users_trade_volume(self,
                           markets: Iterable[str],
                           user_ids: Iterable[int],
                           start_time: int,
                           end_time: int,
                           ):
        # 前闭后开
        return self.do_request('trade.users_volume', markets, user_ids,
                               start_time, end_time)

    def query_deal_summary(self, start_time: int, end_time: int, market: str | None = None):
        if market:
            return self.do_request('trade.deal_summary', start_time, end_time, market)
        return self.do_request('trade.deal_summary', start_time, end_time)

    def update_assets(self):
        return self.do_request('config.update_asset')

    def update_market(self):
        return self.do_request('config.update_market')

    def update_index(self):
        return self.do_request('config.update_index')

    @_retry(2)
    def position_limit_config(self) -> Dict[str, List[List[str]]]:
        return self.do_request('position.limit_config')

    def asset_query_business(self, user_id: int, asset: str,
                             business: Union[str, BalanceBusiness],
                             business_id: int) -> bool:

        if isinstance(business, BalanceBusiness):
            business = business.value
        return self.do_request('asset.query_business', user_id, asset, business, business_id)

    def set_settle_switch(self, user_id: int, status: int, market: str = None):
        """设置浮盈结算开关"""
        self.do_request('market.settle_switch', user_id, market, status)

    def get_auction_pending(self, user_id: int, market: str):
        """获取一键平仓相关信息"""
        return self.do_request('position.auction_pending', user_id, market)

    @_retry(3)
    def update_protect_duration(self, status: bool):
        """更新保护期状态，True-使server进入保护期，False-使server退出保护期"""
        return self.do_request('sys.protection', status)

    def get_protect_status(self) -> Dict:
        """查询server是否处于保护期，{'status': True} True-是，False-否"""
        return self.do_request('sys.protection_query')

    def update_prop_config(self):
        return self.do_request('config.update_prop')
    
    def add_prop_user(self, *user_ids: int):
        self.do_request('config.adjust_prop', {'user': {'add':user_ids, 'del': []},
                                               'market': {'add': [], 'del': []}})

    def remove_prop_user(self, *user_ids: int):
        self.do_request('config.adjust_prop', {'user': {'add': [], 'del': user_ids},
                                               'market': {'add': [], 'del': []}})

    def remove_prop_market(self, *markets: str):
        self.do_request('config.adjust_prop', {'user': {'add': [], 'del': []},
                                               'market': {'add': [], 'del': markets}})

    def market_self_deal(self, market: str, amount: AmountType,
                         price: AmountType, side: int):
        return self.do_request('market.self_deal',
                               market, side, str(amount), str(price))
    
    def market_book_orders_lite(self, market: str, page: int, limit: int, side: int = 0
                           ) -> ResultPage[Dict[str, Any]]:
        """
        side: 0: both, 1: buy, 2: sell
        当side为0时，limit为ask_orders和bid_orders分别取limit，
        当side为1或2时，limit为ask_orders或bid_orders取limit
        return:
        {
            'ask_orders': [{'user_id': 871, 'account': 0, 'price': '1000', 
            'amount': '26483.********', 'left': '26338.********'}, {...}], 
            'bid_orders': [{...}, {...}, {...}], 
            'ask_total': 2, 
            'bid_total': 3, 
            'offset': 0, 'limit': 3, 
            'last': '999.9'
            }
        """
        offset = (page - 1) * limit
        res = self.do_request('order.book_lite', market, side, offset, limit)
        return res

class _BaseDB:
    def __init__(self, **kwargs):
        self._db = pymysql.connect(**kwargs)

    @property
    def db(self):
        return self._db


class PerpetualSysHistory(_BaseDB):
    def __init__(self):
        super().__init__(**current_app.config.get('PERPETUAL_SYS_HISTORY'))


class ViaBTCPoolClient:
    RESPONSE_OK = 0
    MAX_LIMIT = 500

    def __init__(self, logger: Logger = None):
        self.config = config['CLIENT_CONFIGS']['viabtc_pool']
        self._client = RESTClient(
            self.config['url'],
            headers={
                "X-API-KEY": self.config['api_key'],
                "Content-Type": "application/json"
            },
            logger=logger)

    def _sign(self, msg):
        secret_key = self.config['secret_key']
        sign = HMAC(secret_key.encode('utf-8'),
                    msg.encode('utf-8'), hashlib.sha256).hexdigest()
        self._client._headers['X-SIGNATURE'] = sign

    @property
    def _default_request_data(self) -> dict:
        return dict(tonce=int(current_timestamp() * 1000))

    def get_orders(self,
                   status: str,
                   asset: str = None,
                   start_time: int = None,
                   end_time: int = None,
                   page: int = 1,
                   limit: int = MAX_LIMIT) -> dict:
        api = "/internal/account/external/withdraw/history"
        params = self._default_request_data
        params.update(dict(
            status=status,
            page=page,
            limit=limit,
        ))
        if asset:
            params['coin'] = asset

        if start_time:
            params['start_time'] = start_time

        if end_time:
            params['end_time'] = end_time

        params_str = "&".join(
            ["{}={}".format(key, value) for key, value in params.items()])
        self._sign(params_str)

        res_data = self._client.get(api, **params)
        if not res_data:
            return {}
        return res_data['data']

    def get_processing_orders(self,
                              asset: str = None,
                              start_time: int = None,
                              end_time: int = None) -> list:
        orders = {}
        page = 1
        while True:
            ret = self.get_orders('processing', asset, start_time,
                                  end_time, page, self.MAX_LIMIT)['data']
            orders.update({x['id']: x for x in ret})
            if len(ret) != self.MAX_LIMIT:
                break
            page += 1
        return list(orders.values())

    def notify_order_completed(self, order_id: int) -> bool:
        api = "/internal/account/external/withdraw/history"
        data = self._default_request_data
        data.update({
            "id": order_id,
            "status": 'completed',
        })
        self._sign(json_dumps(data))
        result = self._client.post(api, data)
        if result['code'] != self.RESPONSE_OK:
            return False
        return True

    def get_account_balance(self, asset: str = None) -> dict:
        api = "/internal/account/balance"
        params = self._default_request_data
        if asset:
            params['coin'] = asset
        params_str = "&".join(
            ["{}={}".format(key, value) for key, value in params.items()])
        self._sign(params_str)
        res_data = self._client.get(api, **params)
        if not res_data:
            return {}

        result = {}
        for item in res_data['data']:
            result[item['coin']] = Decimal(item['account_balance']) + Decimal(
                item['freezed_balance'])
        return result

    def get_account_history(self,
                            asset: str, business: str = None,
                            start_time: int = None, end_time: int = None,
                            page: int = 1, limit: int = 100) -> list:
        api = "/internal/account/balance/history"
        params = self._default_request_data
        params.update(dict(
            page=page,
            limit=limit,
            coin=asset
        ))
        if business:
            params['business'] = business

        if start_time:
            params['start_time'] = start_time

        if end_time:
            params['end_time'] = end_time

        params_str = "&".join(
            ["{}={}".format(key, value) for key, value in params.items()])
        self._sign(params_str)
        res_data = self._client.get(api, **params)
        if not res_data:
            return []
        return res_data['data']

    def accelerate_tx(self, tx_id: str) -> dict:
        data = {"key": self.config['accelerate_key'], "txid": tx_id}
        headers = {"accept": "application/json",
                   "content-type": "application/x-www-form-urlencoded"}
        with Headers(**headers):
            # noinspection PyTypeChecker
            return self._client.post('/internal/tools/txaccelerator/api/',
                                     data=data)

    @RESTClient.retry(3, timeout=5)
    def accelerate_unconfirmed_deposits(self, chain: str):
        deposits = Deposit.query.filter(
            Deposit.chain == chain,
            Deposit.type == Deposit.Type.ON_CHAIN,
            Deposit.status == Deposit.Status.PROCESSING,
            Deposit.confirmations == 0,
        ).all()
        for deposit in deposits:
            self.accelerate_tx(deposit.tx_id)

    @RESTClient.retry(3, timeout=5)
    def accelerate_unconfirmed_withdrawals(self, chain: str):
        withdrawals = Withdrawal.query.filter(
            Withdrawal.chain == chain,
            Withdrawal.type == Withdrawal.Type.ON_CHAIN,
            Withdrawal.status == Withdrawal.Status.CONFIRMING,
            Withdrawal.confirmations == 0
        ).all()
        for withdrawal in withdrawals:
            self.accelerate_tx(withdrawal.tx_id)

    def _require_account_check(self, email: str) -> dict:
        if not email:
            return {}

        api = "/internal/account/check"
        data = self._default_request_data
        data.update(
            dict(
                email=email,
            )
        )
        self._sign(json_dumps(data))
        result = self._client.post(api, data)
        if result['code'] != self.RESPONSE_OK:
            return {}

        res_data = result['data']

        if not res_data:
            return {}
        return res_data

    def is_ambassador(self, email: str) -> bool:
        res_data = self._require_account_check(email)
        if not res_data:
            return False
        return bool(res_data['is_exist']) and bool(res_data['is_active']) and bool(res_data['is_ambassador'])

    def is_pool_email(self, email: str) -> bool:
        res_data = self._require_account_check(email)
        if not res_data:
            return False
        return bool(res_data['is_exist']) and bool(res_data['is_active'])
