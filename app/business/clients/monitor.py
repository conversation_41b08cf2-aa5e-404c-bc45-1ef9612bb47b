# -*- coding: utf-8 -*-
import logging
import re
import socket
import inspect
from json import dumps as json_dumps
from timeit import default_timer
from traceback import format_exc
from contextlib import contextmanager
from logging import getLogger, LogRecord

from celery import Celery
from flask import current_app, request, make_response

from app import config

MONITOR_MAX_LENGTH = 64

_logger = getLogger(__name__)

"""
默认需要支持的 label，参考如下规范：

https://viabtc.yuque.com/docs/share/69ceb5a7-0db3-413d-9b22-1ff18907c58c?#

org: (先忽略，需要的时候再添加)
project: Coinex
service: CoinexCom
module: CoinexComWeb || CoinexComQuant || CoinexComJob || 
    CoinexComApi || CoinexComAdmin
"""


class MonitorMasterServerClient:

    def __init__(self, *, dummy: bool = False):
        """
        dummy: True only for debug.
        """
        conf = config['CLIENT_CONFIGS']['monitor_master_server']
        self._address = conf['host'], conf['port']
        self._dummy = dummy
        if not dummy:
            self._sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

    def _emit(self, data):
        if self._dummy:
            return
        data = json_dumps(data, ensure_ascii=False).encode('utf-8') + b'\n'
        # noinspection PyBroadException
        try:
            self._sock.sendto(data, self._address)
        except Exception:
            _logger.error(format_exc())

    @staticmethod
    def _to_labels_list(**labels):
        return ["{k}:{v}".format(k=k, v=v) for k, v in labels.items()]

    def increase(self, scope: str, key: str, host: str = '',
                 value: int = 1, **labels):
        host = "deprecated"  # 应该在 monitor_client 收集，此处废弃

        # monitor service max length is MONITOR_MAX_LENGTH
        scope = (scope or "UNKNOWN")[:MONITOR_MAX_LENGTH]
        key = key[:MONITOR_MAX_LENGTH]
        params = [scope, key, host, value]
        params.extend(self._to_labels_list(**labels))
        self._emit({
            'method': 'monitor.inc',
            'params': params
        })

    def set(self, scope: str, key: str, host: str, value: int, **labels):
        host = "deprecated"  # 应该在 monitor_client 收集，此处废弃

        # max length is MONITOR_MAX_LENGTH
        scope = (scope or "UNKNOWN")[:MONITOR_MAX_LENGTH]
        key = key[:MONITOR_MAX_LENGTH]
        params = [scope, key, host, value]
        params.extend(self._to_labels_list(**labels))
        self._emit({
            'method': 'monitor.set',
            'params': params
        })

    def observe(self, scope: str, key: str, host: str, value: float, **labels):
        host = "deprecated"  # 应该在 monitor_client 收集，此处废弃

        # max length is MONITOR_MAX_LENGTH
        scope = (scope or "UNKNOWN")[:MONITOR_MAX_LENGTH]
        key = key[:MONITOR_MAX_LENGTH]
        params = [scope, key, host, value]
        params.extend(self._to_labels_list(**labels))
        self._emit({
            'method': 'monitor.observe',
            'params': params
        })


monitor_client = MonitorMasterServerClient()


def monitor_wrap(func,
                 prefix: str,
                 scope: str,
                 labels: dict = None,
                 failed_checker=None):
    """
    func: which func to wraps
    prefix: type of index, such as 'sms_client', 'email_client'
    scope: work scope, suck as 'web', 'job', 'admin'
    labels: addition tags
    failed_checker: function to check res was failed.
        such as: 'lambda res: res is False'

    Usage:
        1. wrap the origin func with needed params
        2. call origin func with `with` statement

    example:
        monitored_send = monitor_wrap(
            handler.send,
            "sms_client",
            "job",
            labels={"sms_provider": provider_name},
            failed_checker=lambda s: s is False,
        )

        with monitored_send(country_code, number, text) as res:
            if res:
                break
    """
    @contextmanager
    def monitor(*args, **kwargs):
        # stub of start_time
        start_time = default_timer()
        try:
            monitor_client.increase(scope, prefix + "_total", **labels)

            res = func(*args, **kwargs)
            # failed will be divided with exception
            if failed_checker and failed_checker(res):
                monitor_client.increase(scope, prefix + "_failed", **labels)

            yield res
        except Exception:
            # todo: maybe can ignore some exception here
            monitor_client.increase(scope, prefix + "_exception", **labels)
            raise
        finally:
            # stub for end_time
            total_time = max(default_timer() - start_time, 0)

            monitor_client.observe(scope,
                                   prefix + "_duration_seconds",
                                   "host",
                                   total_time,
                                   **labels)
    return monitor


try:
    # try to convert http.HTTPStatus to int status codes
    from http import HTTPStatus


    def _to_status_code(response_status):
        if isinstance(response_status, HTTPStatus):
            return response_status.value
        else:
            return response_status
except ImportError:
    # otherwise simply use the status as is
    def _to_status_code(response_status):
        return response_status


class FlaskMonitor:

    def __init__(self,
                 app,
                 *,
                 default_labels=None,
                 prefix="flask_"):

        self._default_labels = default_labels or {}
        self._default_prefix = prefix or "flask_"
        self._slow_threshold = 20

        if app is not None:
            self.init_app(app)

    def init_app(self, app):
        self.export_defaults(
            app=app, prefix=self._default_prefix
        )

    def export_defaults(self, app=None, prefix='flask', **kwargs):
        if app is None:
            app = current_app

        labels = self._get_combined_labels(None)

        def before_request():
            request.prom_start_time = default_timer()

        def after_request(response):
            if hasattr(request, 'prom_start_time'):
                total_time = max(default_timer() - request.prom_start_time, 0)

                # group default HTTP metrics by this request property,
                # like `path`, `endpoint`, `url_rule`, etc.
                duration_group_name = "url_rule"
                group = getattr(request, duration_group_name)

                monitor_client.observe(
                    request.endpoint,
                    '%shttp_request_duration_seconds' % prefix,
                    "host",
                    total_time,
                    **dict(method=request.method,
                           status=_to_status_code(response.status_code),
                           path=group,
                           **labels.values_for(response))
                )
                if total_time > self._slow_threshold:
                    app.logger.warning("response too slow", extra={"path": group.rule, "consume": total_time})

            monitor_client.increase(
                request.endpoint,
                '%shttp_request_total' % prefix,
                "host",
                **dict(method=request.method,
                       status=_to_status_code(response.status_code),
                       **labels.values_for(response))
            )
            return response

        def teardown_request(exception=None):
            if not exception:
                return

            # group default HTTP metrics by this request property,
            # like `path`, `endpoint`, `url_rule`, etc.
            duration_group_name = "url_rule"
            group = getattr(request, duration_group_name)

            response = make_response('Exception: %s' % exception, 500)

            monitor_client.increase(
                request.endpoint,
                '%shttp_request_total' % prefix,
                "host",
                **dict(method=request.method,
                       status=_to_status_code(response.status_code),
                       **labels.values_for(response))
            )
            monitor_client.increase(
                request.endpoint,
                '%shttp_request_exceptions_total' % prefix,
                "host",
                **dict(method=request.method,
                       status=_to_status_code(response.status_code),
                       **labels.values_for(response))
            )

            if hasattr(request, 'prom_start_time'):
                total_time = max(default_timer() - request.prom_start_time, 0)

                monitor_client.observe(
                    request.endpoint,
                    '%shttp_request_duration_seconds' % prefix,
                    "host",
                    total_time,
                    **dict(method=request.method,
                           status=500,
                           path=group,
                           **labels.values_for(response))
                )
                if total_time > self._slow_threshold:
                    app.logger.warning("response too slow", extra={"path": group.rule, "consume": total_time})
            return

        app.before_request(before_request)
        app.after_request(after_request)
        app.teardown_request(teardown_request)

    def _get_combined_labels(self, labels):
        """
        Combines the given labels with static and default labels
        and wraps them into an object that can efficiently return
        the keys and values of these combined labels.
        """

        labels = labels.copy() if labels else dict()

        if self._default_labels:
            labels.update(self._default_labels.copy())

        def argspec(func):
            return inspect.getfullargspec(func)

        def label_value(f):
            if not callable(f):
                return lambda x: f
            if argspec(f).args:
                return lambda x: f(x)
            else:
                return lambda x: f()

        class CombinedLabels(object):
            def __init__(self, _labels):
                self.labels = _labels.items()

            def keys(self):
                return tuple(map(lambda k: k[0], self.labels))

            def has_keys(self):
                return len(self.labels) > 0

            def values_for(self, response):
                label_generator = tuple(
                    (key, label_value(call))
                    for key, call in self.labels
                ) if labels else tuple()

                return {key: value(response) for key, value in label_generator}

        return CombinedLabels(labels)


exception_pattern = re.compile(r"^(\w+)\(")


def get_exception_class(exception_name: str):
    m = exception_pattern.match(exception_name)
    return m.group(1)


class CeleryMonitor:

    def __init__(self,
                 app: Celery,
                 *,
                 logger_handler=None,
                 default_labels=None,
                 prefix="celery_"):
        self.app = app
        self.state = None
        self.name_cache = None
        self.logger_handler = logger_handler
        self.prefix = prefix
        self.client = self.app.broker_connection().channel().client
        # init queue timer from redis
        self.queue_timer = {queue.decode('utf-8'): 0 for queue in self.client.keys(b'*')
                            if self.client.type(queue) == b'list'}

        self._default_labels = {
            "name": lambda t: getattr(t, "name", "unknown"),
            "exception": lambda t: get_exception_class(getattr(t, "exception", "unknown")),
        }
        if default_labels:
            self._default_labels.update(**default_labels)

        self._default_prefix = prefix or "celery_"

        self.handlers = {
            "task-sent": self.track_task_event,
            "task-received": self.track_task_event,
            "task-started": self.track_task_event,
            "task-succeeded": self.track_task_event,
            "task-failed": self.track_task_event,
            "task-rejected": self.track_task_event,
            "task-revoked": self.track_task_event,
            "task-retried": self.track_task_event,

            "worker-heartbeat": self.track_worker_heartbeat,
            "worker-online": lambda event:
            self.track_worker_status(event, True),
            "worker-offline": lambda event:
            self.track_worker_status(event, False),
        }
        if app is not None:
            self.init_app(app)

    def init_app(self, app: Celery):
        if app is None:
            self.app = app

    def run(self):
        app = self.app

        self.state = app.events.State()

        with app.connection() as connection:
            recv = app.events.Receiver(connection, handlers=self.handlers)
            recv.capture(limit=None, timeout=None, wakeup=True)

    def track_task_event(self, event):
        self.state.event(event)
        task = self.state.tasks.get(event["uuid"])

        # try to track queue length period
        self._try_track_queue_length(event)

        if event["type"] not in self.handlers:
            return

        labels = {}
        for label_name, call in self._default_labels.items():
            # only task-failed has label `exception`
            if label_name == "exception" and event["type"] != "task-failed":
                continue

            # call maybe func or string.
            if callable(call):
                labels[label_name] = call(task)
            else:
                labels[label_name] = call

        # ignore locked exception, consider that was succeeded event
        if not ("exception" in labels and labels["exception"] == "Locked"):
            monitor_client.increase(
                event["type"],
                self.prefix + event["type"].replace("-", "_"),
                "host",
                hostname=event["hostname"],
                **labels,
            )
        match event["type"]:
            case "task-succeeded":
                monitor_client.observe(
                    event["type"],
                    self.prefix + "task_runtime",
                    "host",
                    task.runtime,
                    hostname=event["hostname"],
                    **labels,
                )
                if self.logger_handler:
                    task_name = event.get("task_name") or task.name
                    consume = task.runtime
                    msg = f"{task_name} succeeded in {consume}s"
                    r = LogRecord(name='', level=logging.INFO, pathname='', lineno=0, args=(),
                                  msg=msg, exc_info=None)
                    r.task = task_name
                    r.consume = consume
                    r.project = "Coinex"
                    r.service = "CoinexCom"
                    r.module = "CoinexComJob"
                    self.logger_handler.emit(r)

    def track_worker_status(self, event, is_online):
        hostname = event["hostname"]

        monitor_client.set(
            event["type"],
            self.prefix + "worker_up",
            "host",
            1 if is_online else 0,
            hostname=hostname,
        )

    def track_worker_heartbeat(self, event):
        worker_state = self.state.event(event)[0][0]

        monitor_client.set(
            event["type"],
            self.prefix + "worker_up",
            "host",
            1 if worker_state.alive else 0,
            hostname=event["hostname"],
        )

        monitor_client.set(
            event["type"],
            self.prefix + "worker_tasks_active",
            "host",
            worker_state.active or 0,
            hostname=event["hostname"],
        )

    def _need_track_queue_length(self, queue):
        last_time = self.queue_timer.get(queue, 0)
        # set next report time
        now = default_timer()
        return (now - last_time) > 1

    def _try_track_queue_length(self, event):
        queue, _ = event["hostname"].split("@", 1)
        if not queue:
            return

        # empty when restart, so wo need add it before track
        if queue not in self.queue_timer:
            self.queue_timer[queue] = 0

        for queue_name in self.queue_timer.keys():
            if not self._need_track_queue_length(queue_name):
                continue

            monitor_client.set(
                "task-pending",
                self.prefix + "worker_tasks_pending",
                "host",
                self.client.llen(queue_name),
                queue_name=queue_name,
            )
            # reset timer after report metrics
            self.queue_timer[queue_name] = default_timer()
