# -*- coding: utf-8 -*-
import base64
from datetime import datetime
import time
import hmac
import hashlib
import json
from collections import defaultdict
from decimal import Decimal
from enum import IntEnum, Enum
from typing import Any, Dict, List, Optional, Tuple, Type, Union

from flask import current_app

from app.assets.asset import asset_to_default_chain, get_asset_config
from app.common.constants import PrecisionEnum
from app.exceptions.basic import WalletDuplicateSubmission

from ...config import config
from ...exceptions import (DepositsSuspended,
                           FrequencyExceeded, InternalServerError,
                           InvalidAbnormalDeposit, InvalidAddress,
                           InvalidArgument, InvalidAssetCode, InvalidChainName,
                           InvalidWithdrawalAddress, OperationDenied,
                           RecordNotFound, ServiceUnavailable,
                           WalletUnderMaintenance, WithdrawalPrecisionExceeded,
                           WithdrawalsSuspended, DepositAddressNotUsed,
                           DepositAddressRenewalFrequencyExceeded,
                           AssetNotFound, ChainNotFound, DepositAddressRenewalDisabled)
from ...models import Deposit, Withdrawal
from ...utils import RESTClient, JsonRPC2Client, WhyNot, amount_to_str, quantize_amount
from .server import ResultPage
from ...assets import list_all_chains, has_chain


class WalletDepositStatus:
    # web status : wallet status
    TOO_SMALL = 'TOO_SMALL'
    PROCESSING = 'CREATED'
    CONFIRMING = 'SAFE'
    FINISHED = 'FINISHED'
    CANCELLED = 'CANCELLED'

    @classmethod
    def equals(cls, wallet_status: str, local_status: Deposit.Status) -> bool:
        return cls.to_local(wallet_status) == local_status

    @classmethod
    def to_local(cls, wallet_status: str) -> Deposit.Status:
        if wallet_status == cls.PROCESSING:
            return Deposit.Status.PROCESSING
        if wallet_status == cls.CONFIRMING:
            return Deposit.Status.CONFIRMING
        statuses = [v for k, v in cls.__dict__.items() if k.isupper()]
        if wallet_status not in statuses:
            raise ValueError(wallet_status)
        for e in Deposit.Status:
            if wallet_status == e.name:
                return e
        raise ValueError(wallet_status)


class WalletWithdrawalStatus:
    PROCESSING = 'CREATED'
    CONFIRMING = 'CONFIRMING'
    FINISHED = 'FINISHED'
    CANCELLED = 'CANCELLED'
    FAILED = 'FAILED'

    @classmethod
    def equals(cls, wallet_status: str, local_status: Withdrawal.Status) -> bool:
        return cls.to_local(wallet_status) == local_status

    @classmethod
    def to_local(cls, wallet_status: str) -> Withdrawal.Status:
        if wallet_status == cls.PROCESSING:
            return Withdrawal.Status.PROCESSING
        statuses = [v for k, v in cls.__dict__.items() if k.isupper()]
        if wallet_status not in statuses:
            raise ValueError(wallet_status)
        for e in Withdrawal.Status:
            if wallet_status == e.name:
                return e
        raise ValueError(wallet_status)


class WalletOnchainTxStatus(Enum):
    CREATED = 'CREATED'         # 未处理
    PROCESSING = 'PROCESSING'   # 处理中
    FINISHED = 'FINISHED'       # 已完成
    CANCELLED = 'CANCELLED'     # 已取消


class WalletResponseCode(IntEnum):
    OK = 0
    INTERNAL_SERVER_ERROR = 1
    SERVICE_UNAVAILABLE = 2
    SERVICE_UNDER_MAINTENANCE = 3
    OPERATION_DENIED = 100
    FREQUENCY_EXCEEDED = 101
    DEPOSITS_SUSPENDED = 102
    WITHDRAWALS_SUSPENDED = 103
    DEPOSIT_ADDRESS_RENEWAL_DISABLED = 104
    DEPOSIT_ADDRESS_NOT_USED = 105
    DEPOSIT_ADDRESS_RENEWAL_FREQUENCY_EXCEEDED = 106
    DUPLICATE_SUBMISSION = 107
    INVALID_ARGUMENT = 200
    INVALID_CHAIN_NAME = 201
    INVALID_ASSET_CODE = 202
    INVALID_ADDRESS = 203
    INVALID_ABNORMAL_DEPOSIT = 204
    WITHDRAWAL_AMOUNT_TOO_SMALL = 205
    WITHDRAWAL_PRECISION_EXCEEDED = 206
    INVALID_WITHDRAWAL_ADDRESS = 207
    RECORD_NOT_FOUND = 300
    CHAIN_NOT_FOUND = 301
    ASSET_NOT_FOUND = 302


_EXCEPTION_MAPPING: Dict[int, Type[Exception]] = {
    WalletResponseCode.INTERNAL_SERVER_ERROR: InternalServerError,
    WalletResponseCode.SERVICE_UNAVAILABLE: ServiceUnavailable,
    WalletResponseCode.SERVICE_UNDER_MAINTENANCE: WalletUnderMaintenance,
    WalletResponseCode.OPERATION_DENIED: OperationDenied,
    WalletResponseCode.FREQUENCY_EXCEEDED: FrequencyExceeded,
    WalletResponseCode.DEPOSITS_SUSPENDED: DepositsSuspended,
    WalletResponseCode.DUPLICATE_SUBMISSION: WalletDuplicateSubmission,
    WalletResponseCode.WITHDRAWALS_SUSPENDED: WithdrawalsSuspended,
    WalletResponseCode.DEPOSIT_ADDRESS_RENEWAL_DISABLED: DepositAddressRenewalDisabled,
    WalletResponseCode.DEPOSIT_ADDRESS_NOT_USED: DepositAddressNotUsed,
    WalletResponseCode.DEPOSIT_ADDRESS_RENEWAL_FREQUENCY_EXCEEDED: DepositAddressRenewalFrequencyExceeded,
    WalletResponseCode.INVALID_ARGUMENT: InvalidArgument,
    WalletResponseCode.INVALID_CHAIN_NAME: InvalidChainName,
    WalletResponseCode.INVALID_ASSET_CODE: InvalidAssetCode,
    WalletResponseCode.INVALID_ADDRESS: InvalidAddress,
    WalletResponseCode.INVALID_ABNORMAL_DEPOSIT: InvalidAbnormalDeposit,
    WalletResponseCode.WITHDRAWAL_AMOUNT_TOO_SMALL: InvalidArgument,
    WalletResponseCode.WITHDRAWAL_PRECISION_EXCEEDED: WithdrawalPrecisionExceeded,
    WalletResponseCode.INVALID_WITHDRAWAL_ADDRESS: InvalidWithdrawalAddress,
    WalletResponseCode.RECORD_NOT_FOUND: RecordNotFound,
    WalletResponseCode.CHAIN_NOT_FOUND: ChainNotFound,
    WalletResponseCode.ASSET_NOT_FOUND: AssetNotFound
}


def _token_create(salt: str, dept: str = 'web', exp: int = 30) -> str:

    def base64url_encode(input_):
        return base64.urlsafe_b64encode(input_).replace(b'=', b'')

    def simple_json_dumps(data):
        return json.dumps(data, separators=(',', ':')).encode()

    headers = {'typ': 'JWT', 'alg': 'HS256'}
    payload = {'dept': dept, 'exp': int(time.time() * 1000) + exp * 1000}

    segments = list()
    segments.append(base64url_encode(simple_json_dumps(headers)))
    segments.append(base64url_encode(simple_json_dumps(payload)))

    signing_input = b'.'.join(segments)
    signature = hmac.new(salt.encode(), signing_input, hashlib.sha256).digest()
    segments.append(base64url_encode(signature))

    return b'.'.join(segments).decode('utf-8')


jwt_token_create = _token_create


class WalletClient:

    def __init__(self):
        self._client = RESTClient(config['CLIENT_CONFIGS']['wallet']['url'] + "/director")
        self._salt = config['CLIENT_CONFIGS']['wallet']['salt']

    def _handle_response(self, r):
        code = r['code']
        if code == WalletResponseCode.OK:
            return r['data']
        if ex := _EXCEPTION_MAPPING.get(code):
            current_app.logger.error(json.dumps(r))
            raise ex
        raise ServiceUnavailable(message=r['message'])

    def _new_token(self):
        return _token_create(self._salt, exp=60)

    def get(self, path, **params):
        with RESTClient.Headers(token=self._new_token()):
            r = self._client.get(path, **params)
        return self._handle_response(r)

    def post(self, path, json):
        with RESTClient.Headers(token=self._new_token()):
            r = self._client.post(path, json)
        return self._handle_response(r)
    
    def put(self, path, json):
        with RESTClient.Headers(token=self._new_token()):
            r = self._client.put(path, json)
        return self._handle_response(r)

    def delete(self, path, **params):
        with RESTClient.Headers(token=self._new_token()):
            r = self._client.delete(path, **params)
        return self._handle_response(r)

    def test(self):
        return self.get("/ping/login_required")

    def get_expenses(self, start_ts: int, end_ts: int) -> defaultdict:
        """get withdraw expenses"""
        response = self.get('/statistics/tx-fees', start=start_ts, end=end_ts)
        result = defaultdict(Decimal)
        for _chain, _data in response.items():
            for __v in _data:
                _asset = __v["asset"]
                _amount = Decimal(__v["amount"])
                if _amount == Decimal():
                    continue
                result[_asset] += _amount
        return result

    def get_deposits(self, cursor: int, limit: int = 50
                     ) -> List[Dict[str, Any]]:
        """cursor is wallet_deposit_id"""
        return self.get('/deposits', min_id=cursor, limit=limit)['items']

    def get_deposit(self, wallet_deposit_id: int) -> Dict[str, Any]:
        return self.get(f'/deposits/{wallet_deposit_id}')

    def get_withdrawal(self, withdrawal_id: int) -> Dict[str, Any]:
        return self.get(f'/withdrawals/{withdrawal_id}')

    def send_withdrawal(self,
                        id_: int,
                        user_id: int,
                        asset: str,
                        chain: str,
                        address: str,
                        memo: Optional[str],
                        amount: Decimal,
                        attachment: Optional[Dict]):
        self.post('/withdrawals', {
            'request_id': id_,
            'user_id': user_id,
            'asset': asset,
            'chain': chain,
            'address': address,
            'memo': memo or '',
            'amount': amount_to_str(amount),
            'attachment': attachment or {}
        })

    def get_deposit_addresses(self, user_id: str, chain: Optional[str], page: int,
                              limit: int) -> ResultPage[Dict[str, Any]]:
        params = dict(
            user_id=user_id,
            page=page,
            limit=limit
        )
        if chain:
            params['chain'] = chain
        r = self.get('/deposit-addresses', **params)
        return ResultPage.from_response(r, page, limit, data_attr='items')

    def get_or_create_deposit_address(self, chain: str, user_id: int
                                      ) -> Tuple[str, str]:
        r = self.post('/deposit-addresses', {
            'user_id': user_id,
            'chain': chain,
            'renew': False
        })
        return r['address'], r['memo']

    def new_deposit_address(self, chain: str, user_id: int) -> Tuple[str, str]:
        r = self.post('/deposit-addresses', {
            'user_id': user_id,
            'chain': chain,
            'renew': True
        })
        return r['address'], r['memo']

    def is_coinex_address(self, chain: str, address: str, memo: str = "", attachment: Dict = None) -> Tuple[bool, bool]:
        # 校验充值找回提交的退回地址 (不能是CoinEx地址)
        payload = {
            "chain": chain,
            "address": address,
            "memo": memo,
        }
        if attachment:
            payload["attachment"] = attachment
        r = self._validate_withdrawal_address(payload)
        return r["is_valid"], bool(r.get("is_deposit_address"))

    def validate_withdrawal_address(self, chain: str, address: str,
                                    memo: str = '', attachment: Dict = None,
                                    asset: Optional[str] = None) -> Union[bool, WhyNot]:
        if len(address) > 256:  # TODO: change db address field from varchar(256) to varchar(512)
            return False
        payload = {
            'chain': chain,
            'address': address,
            'memo': memo,
            'attachment': attachment or {}
        }
        if asset:
            payload["asset"] = asset
        if attachment:
            payload['attachment'] = attachment
        r = self._validate_withdrawal_address(payload)
        return r['is_valid']  # here can return WhyNot(r['reason']) if is_valid=False

    def validate_address_info(self, chain: str, address: str,
                                    memo: str = '', attachment: Dict = None) -> Tuple[bool, bool, bool]:
        payload = {
            'chain': chain,
            'address': address,
            'memo': memo
        }
        if attachment:
            payload['attachment'] = attachment
        r = self._validate_withdrawal_address(payload)
        return r['is_valid'], bool(r.get('is_contract')), bool(r.get("is_deposit_address"))  # is_contract: bool or none

    def _validate_withdrawal_address(self, payload: Dict) -> Dict:
        """
        return
        {
        "is_contract": False,    # 是否合约地址
        "is_valid": True,   # 是否合法地址
        "is_deposit_address": True,  # 是否属于CoinEx充值地址
        "reason": ""    # 失败原因（地址非法或校验失败）
        }
        """
        r = self.post('/utils/withdrawal-address-validation', payload)
        return r

    def normalise_tx_ids(self, tx_ids: list[tuple[str, str]]) -> dict[str, str]:
        _cs = set(list_all_chains())
        f = lambda x: x and x in _cs
        formatted_tx_ids = [(chain, tx_id) for chain, tx_id in tx_ids if f(chain)]
        if formatted_tx_ids:
            r = self.post('/utils/normalise-tx-ids', {
                'tx_ids': formatted_tx_ids
            })['tx_ids']
        else:
            r = []
        ret = {}
        for chain, _ in tx_ids:
            if not f(chain):
                ret[chain] = ''
            else:
                ret[chain] = r.pop(0)
        return ret

    def normalise_addresses(self, addresses: list[tuple[str, str]]) -> dict[str, str]:
        _cs = set(list_all_chains())
        f = lambda x: x and x in _cs
        addrs = [(chain, addr) for chain, addr in addresses if f(chain)]
        if addrs:
            r = self.post('/utils/normalise-addresses', {
                'addresses': addrs
            })['addresses']
        else:
            r = []
        ret = {}
        for chain, _ in addresses:
            if not f(chain):
                ret[chain] = ''
            else:
                ret[chain] = r.pop(0)
        return ret

    def get_explorer_address_url(self, chain: str, address: str) -> str:
        if not chain or not has_chain(chain):
            return ''
        r = self.post('/utils/explorer-address-urls', {
            'addresses': [(chain, address)]
        })
        return r['urls'][0]

    def get_explorer_tx_url(self, chain: str, tx: str) -> str:
        if not chain or not has_chain(chain):
            return ''
        r = self.post('/utils/explorer-tx-urls', {
            'tx_ids': [(chain, tx)]
        })
        return r['urls'][0]

    def get_explorer_addresses_url(self, addresses: List[Tuple[str, str]]
                                   ) -> List[str]:
        # 兼容chain为空(内部转账)或已下架的情况
        _cs = set(list_all_chains())
        f = lambda x: x and x in _cs
        addrs = [(chain, addr) for chain, addr in addresses if f(chain)]
        if addrs:
            r = self.post('/utils/explorer-address-urls', {
                    'addresses': addrs
                })['urls']
        else:
            r = []
        urls = []
        for chain, _ in addresses:
            if not f(chain):
                urls.append('')
            else:
                urls.append(r.pop(0))
        return urls

    def get_explorer_txs_url(self, txs: List[Tuple[str, str]]) -> List[str]:
        # 兼容chain为空(内部转账)或已下架的情况
        _cs = set(list_all_chains())
        f = lambda x: x and x in _cs
        _txs = [(chain, tx) for chain, tx in txs if f(chain)]
        if _txs:
            r = self.post('/utils/explorer-tx-urls', {
                'tx_ids': _txs
            })['urls']
        else:
            r = []
        urls = []
        for chain, _ in txs:
            if not f(chain):
                urls.append('')
            else:
                urls.append(r.pop(0))
        return urls

    def get_account_balance(self, asset: str = '', chain: str = '',
                            account: str = '') -> List[Dict]:
        """"[{"asset":"","chain":"","balances":{"HOT":{"total":"","available":""},"DEPOSIT": ...,"COLD": ...}}, ...]"""
        return self.get('/balances', asset=asset, chain=chain, account=account)

    def get_node_statuses(self):
        """
        获取所有链节点是否正常
        {'BTC': {'is_healthy': True}}
        """
        return self.get('/maintenance/node-statuses')

    def get_assets(self) -> List[Dict[str, Any]]:
        """[{"code":"","name":"","chains":[{}, ...]}, ...]"""
        return self.get('/assets')

    def get_latest_block_heights(self) -> Dict[str, int]:
        """获取所有链的高度，从缓存查询"""
        r = self.get('/chains')
        return {x['name']: x['latest_block_height'] for x in r}

    def get_latest_block_height(self, chain: str) -> int:
        """从钱包节点查询节点高度，勿频繁调用"""
        r = self.get(f'/chains/{chain}')
        return r['latest_block_height']

    def get_addresses_balance(self, asset: str, chain: str,
                              addresses: List[str]) -> Dict[str, Decimal]:
        """从钱包节点查询地址余额，勿频繁调用"""
        r = self.get('/balances/addresses',
                     asset=asset, 
                     chain=chain,
                     addresses=','.join(addresses))
        return {k: quantize_amount(v, 8) for k, v in r.items()}

    def get_assets_circulation(self) -> Dict[str, Decimal]:
        """获取币种流通量数据,多条链取流通量最大值"""
        req = self.get_assets()
        result = dict()
        for _data in req:
            chains = _data["chains"]
            circulation = max([Decimal(_v["circulating_supply"]) for _v in chains])
            if circulation == Decimal():
                continue
            result[_data["code"]] = circulation
        return result

    def get_assets_auto_rewards(self) -> Dict[str, Decimal]:
        """TODO: 已废弃， 获取币种收益"""
        r = self.get('/balances/auto-rewards')
        return {k: Decimal(v) for k, v in r.items()}

    def get_wallet_assets_rewards(self, start_ts: int, end_ts: int) -> Dict[str, Decimal]:
        """获取币种收益"""
        r = self.get('/statistics/auto-rewards', start=start_ts, end=end_ts)
        return {k: Decimal(v) for k, v in r.items()}

    def balance_asset_auto_reward(self, req_id: int, asset: str, amount: Decimal):

        """TODO: 已废弃，币种收益平账"""
        self.post('/balances/auto-rewards', {'request_id': req_id, 'asset': asset, 'balanced_amount': amount_to_str(amount)})

    def get_abnormal_deposit_balance(self, end_time: int = None) -> Dict[str, Decimal]:
        params = {}
        if end_time:
            params['end'] = end_time
        r = self.get('/balances/abnormal', **params)
        return {k: Decimal(v) for k, v in r.items()}

    def validate_and_analysis_deposit_recovery(self,
        user_id: int,
        asset: str,
        chain: str,
        address: str,
        memo: str,
        amount: Decimal,
        tx_id: str,
    ) -> Dict[str, Any]:
        """ 效验参数 & 分析充值找回的找回类型  """
        r = self.post(
            "/deposit-recoveries/analysis",
            {
                "user_id": user_id,
                "asset": asset,
                "chain": chain,
                "address": address,
                "memo": memo,
                "amount": amount_to_str(amount),
                "tx_id": tx_id,
            },
        )
        self._fmt_decimal_val(r, keys=["amount"])
        return r

    def send_deposit_recovery(
        self,
        request_id: int,
        user_id: int,
        asset: str,
        chain: str,
        address: str,
        amount: Decimal,
        deduction: Optional[Decimal],
        tx_id: str,
        solution: str,
        sent_to: str,
    ) -> Dict[str, Any]:
        """ 尝试增加一条充值找回记录 """
        data = {
            "request_id": request_id,
            "user_id": user_id,
            "asset": asset,
            "chain": chain,
            "address": address,
            "amount": amount_to_str(amount),
            "tx_id": tx_id,
            "solution": solution,
            "sent_to": sent_to,
        }
        if deduction:
            data['deduction'] = amount_to_str(deduction)
        current_app.logger.warning(f'send_deposit_recovery data: {data}')
        return self.post(
            "/deposit-recoveries",
            data,
        )

    def get_deposit_recovery(self, request_id: int) -> Dict[str, Any]:
        """ 查询充值找回记录信息，request_id: 本地row_id """
        return self.get(f"/deposit-recoveries/{request_id}")

    def delete_deposit_recovery(self, request_id: int) -> Dict[str, Any]:
        """ （复审后再拒绝时）删除一条充值找回记录，request_id: 本地row_id """
        return self.delete(f"/deposit-recoveries/{request_id}")

    def send_abnormal_deposit(
        self,
        request_id: int,
        user_id: int,
        asset: str,
        chain: str,
        address: str,
        amount: Decimal,
        tx_id: str,
        vout: int,
        memo: str,
        remark: str,
    ) -> Dict[str, Any]:
        """ 尝试增加一条异常充值记录，request_id: 本地row_id """
        return self.put(
            f"/deposit-recoveries/abnormal-deposits/{request_id}",
            {
                "user_id": user_id,
                "asset": asset,
                "chain": chain,
                "address": address,
                "amount": amount_to_str(amount),
                "tx_id": tx_id,
                "vout": vout,
                "memo": memo,
                "remark": remark,
            },
        )

    def audit_abnormal_deposit(
        self,
        request_id: int,
        chain: str,
        asset: str,
        tx_id: str,
        address: str,
        user_id: int,
        auditor_id: int,
    ) -> Dict[str, Any]:
        """ 审核异常充值记录，request_id: 本地row_id """
        return self.post(
            "/deposit-recoveries/abnormal-deposit-audit",
            {
                "request_id": request_id,
                "chain": chain,
                "asset": asset,
                "tx_id": tx_id,
                "address": address,
                "user_id": user_id,
                "auditor_id": auditor_id,
            },
        )

    def get_abnormal_deposit(self, request_id: int) -> Dict[str, Any]:
        """ 查询异常充值记录信息，request_id: 本地row_id """
        return self.get(f"/deposit-recoveries/abnormal-deposits/{request_id}")
    
    def get_abnormal_deposit_by_tx(self, tx_id: str):
        return self.get('/abnormal-deposits', tx_id=tx_id)

    def get_chain_gas_fees(self, start_ts: int = None,
                           end_ts: int = None, by_chain: bool = True) -> Dict[str, List]:
        """获取时间段内所有链的手续费支出"""
        params = {}
        if start_ts:
            params['start'] = start_ts
        if end_ts:
            params['end'] = end_ts
        params['by_chain'] = by_chain
        r = self.get('/statistics/tx-fees', **params)
        self._fmt_decimal_val(r, keys=['amount'])
        return r

    def get_chain_gas_fee(self, chain: str, start_ts: int = None,
                          end_ts: int = None, by_chain: bool = True) -> List:
        """获取指定链的手续费支出"""
        params = {}
        if start_ts:
            params['start'] = start_ts
        if end_ts:
            params['end'] = end_ts
        params['by_chain'] = by_chain
        r = self.get('/statistics/tx-fees/{}'.format(chain), **params)
        self._fmt_decimal_val(r, keys=['amount'])
        return r

    def get_chain_tx_counts(self, start_ts: int = None, end_ts: int = None) -> Dict[str, Dict]:
        """获取时间段内所有链的交易数量（按币种划分）"""
        params = {}
        if start_ts:
            params['start'] = start_ts
        if end_ts:
            params['end'] = end_ts
        r = self.get('/statistics/asset-tx-counts', **params)
        self._fmt_decimal_val(r, keys=['amount'])
        return r

    def get_chain_tx_count(self, chain: str, start_ts: int = None,
                           end_ts: int = None) -> int:
        """获取指定链的交易数量（按币种划分）"""
        params = {}
        if start_ts:
            params['start'] = start_ts
        if end_ts:
            params['end'] = end_ts
        return self.get('/statistics/asset-tx-counts/{}'.format(chain), **params)

    def get_tx_counts(self, start_ts: int = None,
                      end_ts: int = None) -> Dict[str, int]:
        """获取时间段内所有链的交易数量"""
        params = {}
        if start_ts:
            params['start'] = start_ts
        if end_ts:
            params['end'] = end_ts
        return self.get('/statistics/tx-counts', **params)

    def get_tx_count(self, chain: str, start_ts: int = None,
                     end_ts: int = None) -> int:
        """获取指定链的交易数量"""
        params = {}
        if start_ts:
            params['start'] = start_ts
        if end_ts:
            params['end'] = end_ts
        return self.get('/statistics/tx-counts/{}'.format(chain), **params)

    @classmethod
    def _fmt_decimal_val(cls, data: Union[Dict, List], keys: List[str]):
        if isinstance(data, dict):
            for k, v in data.items():
                if isinstance(v, str) and k in keys:
                    data[k] = Decimal(v)
                elif isinstance(v, (dict, list)):
                    cls._fmt_decimal_val(v, keys)
        elif isinstance(data, list):
            for item in data:
                if isinstance(item, (dict, list)):
                    cls._fmt_decimal_val(item, keys)

    def get_staking_reward(self, asset: str, 
                           start_time: datetime, end_time: datetime) -> Dict:

        chain = asset_to_default_chain(asset)
        start_timestamp = int(start_time.timestamp())
        end_timestamp = int(end_time.timestamp())
        resp = self.get('/staking/reward', 
                        **{'start_time': start_timestamp, 
                           'end_time': end_timestamp,
                           'asset': asset,
                           'chain': chain})
        reward_detail = resp['amount_by_hour']
        reward_detail = {int(k): Decimal(v) for k, v in reward_detail.items()}
        return dict(
                amount=quantize_amount(resp['amount'], PrecisionEnum.COIN_PLACES),
                reward_detail=reward_detail
                )

    def get_staking_summary(self, asset: str, chain: str = None):

        chain = chain or asset_to_default_chain(asset)
        resp = self.get('/staking/summary', **{'asset': asset, 'chain': chain})
        return dict(
            pending_withdrawal_reward=quantize_amount(resp['pending_withdrawal_reward'], PrecisionEnum.COIN_PLACES),
            activated_staking_amount=Decimal(resp['activated_staking_amount']),
            staking_amount=Decimal(resp['staking_amount']),
            pending_staking_amount=Decimal(resp.get('pending_staking_amount', 0)),
            pending_unstaking_amount=Decimal(resp.get('pending_unstaking_amount', 0)),
            apr=Decimal(resp.get("apr") or 0),
        )

    def get_staking_status(self, asset: str, business_id: str):

        chain = asset_to_default_chain(asset)
        resp = self.get('/staking/status', **{'asset': asset,
                                              'chain': chain,
                                              'business_id': business_id})
        return resp

    def update_staking_status(self, asset: str, chain: str, amount: Decimal, business_id: str):
        
        class Status:
            GAIN = 'GAIN'
            LOSS = 'LOSS'
        
        class Type:
            GIVEN = 'GIVEN'
            TAKEN = 'TAKEN'
        if amount == Decimal():
            return dict(amount=0)
        status = Status.GAIN if amount > 0 else Status.LOSS
        conf = get_asset_config(asset)
        resp = self.post('/staking/report', {'amount': str(abs(amount)),
                                             'asset': asset,
                                             'chain': chain,
                                             'status': status,
                                             'target_size': str(conf.staking_bufsize),
                                             'business_id': business_id})
        if resp['type'] == Type.GIVEN:
            return dict(amount=abs(Decimal(resp['amount'])))
        else:
            return dict(amount=-abs(Decimal(resp['amount'])))

    def get_staking_assets(self) -> list[dict]:
        """[{asset, chain}]"""
        return self.get('/staking/available-chains')

    def get_abnormal_issuance_assets(self) -> list:
        """
        [{'asset': 'MODE',
          'chain': 'MODE',
          'cur_amount': '***********',
          'updated_at': 1737514663,
          'his_amount': '***********',
          'his_updated_at': 1737514663}]
        """
        return self.get('/assets/increased-total-supplies')

    def send_onchain_order(
            self,
            request_id: str,
            exchanger_id: str,
            chain: str,
            from_identity: str,
            from_amount: Decimal,
            to_identity: str,
            to_amount: Decimal,
            from_address: str,
            to_address: str,
            slippage: Decimal,
            swap_order_id: str,
            tx_data: dict,
    ):
        payload = {
            'request_id': request_id,
            'chain': chain,
            'from_identity': from_identity,
            'from_amount': amount_to_str(from_amount),
            'exchanger_id': exchanger_id,
            'to_identity': to_identity,
            'to_amount': amount_to_str(to_amount),
            'from_address': from_address,
            'to_address': to_address,
            'slippage': amount_to_str(slippage),
            'order_id': swap_order_id,
            'tx_data': tx_data
        }
        self.post('/onchain-trading/swap-orders', payload)

    def get_onchain_orders(self, request_ids: list):
        """
        tx_id: 交易ID, 如果未上链则为null
        received_amount: 实际到账金额, 在状态为FINISHED有值, 其他情况为null
        confirmations: 交易确认数，>0: 已上链 , <=0: 未上链
        status: 订单状态, 有CREATED(未处理), PROCESSING(处理中), FINISHED(已完成), CANCELLED(已取消)
        [
          {
              "request_id": "2",
              "tx_id": null,
              "received_amount": null,
              "confirmations": 0,
              "status": "PROCESSING",
          },
        ]
        """
        resp = self.get('/onchain-trading/swap-orders', **{'request_ids': ','.join(request_ids)})
        return resp['items']

    def get_onchain_asset_statistics(self):
        resp = self.get('/onchain-trading/asset-statistics')
        return resp['items']

    def get_onchain_gas_fee(self, chain: str):
        resp = self.get('/onchain-trading/gas-fee', **{'chain': chain})
        return resp['fee']

    def get_custody_assets(self, custody_name: str) -> List[Dict]:
        return self.get('/custody/assets', custody_name=custody_name)

    def create_custody_transfer(
        self,
        direction,
        asset: str,
        chain: str,
        request_id: str,
        amount: Decimal,
        custody_name: str,
    ) -> None:
        payload = {
            'custody_name': custody_name.lower(),
            'direction': direction,
            'asset': asset,
            'chain': chain,
            'request_id': request_id,
            'amount': amount_to_str(amount),
        }

        try:
            self.post('/custody/transfers', payload)
            return True
        except WalletDuplicateSubmission:
            current_app.logger.warning(f'create_custody_transfer duplicate submission: {payload}')
            return False

    def get_custody_transfer(self, request_id: str) -> Dict:
        return self.get(f'/custody/transfers/{request_id}')


class WalletManagerClient:

    def __init__(self, group: int, chain: str):
        if chain in config['TEE_SIGN_CHAINS']:
            conf = config['CLIENT_CONFIGS']['wallet_manager_tee']
        else:
            conf = config['CLIENT_CONFIGS']['wallet_manager']
        self._client = JsonRPC2Client(conf[group]['url'])
        self._salt = conf[group]['salt']

    def _new_token(self):
        return _token_create(self._salt, exp=60)

    def post(self, method: str, data: dict):
        with RESTClient.Headers(token=self._new_token()):
            return self._client.post(method, data)

    def tss_sign_pre(self, data: dict):
        """tss双签第一步，协商R值"""
        return self.post('tss_sign_pre', data)

    def sign_tx(self, sign_func: str, tx_data: dict):
        return self.post(sign_func, tx_data)


class CoinexWalletClient:

    def __init__(self):
        conf = config['CLIENT_CONFIGS']['coinex_wallet']
        headers = {}
        for field in ['CF-Access-Client-Secret', 'CF-Access-Client-Id']:
            if conf.get(field):
                headers[field] = conf[field]
        self._client = RESTClient(conf['url'], headers=headers)

    def get_chain_info(self):
        """
        [{"chain":"BTC","support_token":false}]
        """
        path = '/res/chains'
        data = self._client.get(path)
        if data.get('code') != 0:
            raise ServiceUnavailable(message=data['message'])
        return data['data']

