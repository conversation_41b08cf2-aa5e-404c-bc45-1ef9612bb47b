# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import datetime, timedelta, date
from decimal import Decimal, ROUND_UP
from typing import Iterable, Tuple, Optional, Dict, List, Set

from flask import current_app
from flask_babel import gettext as _
from pyroaring import BitMap
from sqlalchemy import func
from sqlalchemy.exc import IntegrityError

from app.assets.asset import get_asset_config
from app import config

from app.business import ServerClient, TradeLogDB, SPOT_ACCOUNT_ID, CacheLock, LockKeys
from app.business.alert import send_alert_notice
from app.business.prices import PriceManager
from app.business.site import SiteSettings
from app.business.utils import get_asset_price_by_datetime
from app.common import (
    ServerBalanceType,
    BalanceBusiness,
    PrecisionEnum,
)
from app.exceptions import (
    InvestmentInterestError,
    InvalidArgument,
    InvestmentAccountNotOpen,
    InvestmentAccountNotFound,
    InvestmentTransferError,
    TransferNotAllowed,
)
from app.models import (
    AssetInvestmentConfig,
    MarginReceivableInterestHistory,
    InvestmentTransferHistory,
    db,
    User,
    UserInvestmentSummary,
    UserHourInterestHistory,
    UserHourInterestDetail,
    InterestStatisticTime,
)
from app.models.daily import DailyInvestmentReport
from app.models.equity_center import UserDailyIncEquityHistory
from app.models.investment import (
    InterestType,
    UserDayInterestDetail,
    UserDayInterestHistory,
)
from app.models.pledge import PledgeInterestHistory
from app.models.user import SubAccount
from app.utils import quantize_amount, batch_iter
from app.utils.amount import amount_to_str, quantize_amount_non_zero
from app.utils.date_ import date_to_datetime, datetime_to_str, now, convert_datetime, today, today_datetime, next_month

from app.caches.investment import (
    InvIncEquityTotalIncomeCache,
    InvIncEquityYesIncomeCache,
    InvestmentConfigCache,
    Investment7DaysEARCache,
    InvestmentYesterdayARRCache,
    InvestmentIncTotalIncomeCache,
)
from app.utils.http_client import BaseHTTPClient


class InvestmentUserAccountsHelper(object):
    def __init__(self, user_id: int, is_sub_account: bool = False):
        self.user_id = user_id
        self.is_sub_account = is_sub_account

    def get_accounts_info(self):
        from app.business.equity_center.inv_increase import IncreaseEquityHelper

        result = {}
        seven_days_info = Investment7DaysEARCache().read_data()
        yesterday_rate_info = InvestmentYesterdayARRCache().read_data()
        invest_config = InvestmentConfigCache().get_all_config()
        base_rates = {asset: Decimal(config.get("base_rate", 0)) for asset, config in invest_config.items()}
        balances = ServerClient().get_user_balances(self.user_id, account_id=AssetInvestmentConfig.ACCOUNT_ID)

        investment_assets = set(invest_config.keys())
        price_map = PriceManager.assets_to_usd()

        sum_model = UserInvestmentSummary
        query = sum_model.query.filter(sum_model.user_id == self.user_id).all()
        user_sum_map = {v.asset: v.amount for v in query}

        # todo 将理财加息券的数据 合并到 UserInvestmentSummary
        inc_total_income = InvestmentIncTotalIncomeCache(self.user_id).get_user_income()

        inc_eq_incomes = InvIncEquityTotalIncomeCache(self.user_id).get_user_income()
        inc_eq_yes_income = InvIncEquityYesIncomeCache(self.user_id).get_user_income()

        rows = self.get_user_date_interest(self.user_id, today() - timedelta(days=1))
        user_yes_map = {r.asset: r.interest_amount for r in rows}

        asset_inc_eq_info = IncreaseEquityHelper.get_user_show_inc_info(self.user_id)

        for asset in investment_assets:
            asset_config = invest_config[asset]
            balance = Decimal(balances.get(asset, {}).get("available", Decimal()))
            rule_map = asset_config.get("rule_map") or {}
            ladder_rule = rule_map.get(AssetInvestmentConfig.ConfigType.LADDER.name, {})
            fixed_rule = rule_map.get(AssetInvestmentConfig.ConfigType.FIXED.name, {})
            fixed_rate = Decimal(fixed_rule.get("rate", 0))
            base_rate = Decimal(base_rates.get(asset, 0))
            result[asset] = {
                "coin_type": asset,
                "asset": asset,
                "amount": balance,
                "amount_usd": balance * price_map.get(asset, Decimal()),
                "min_amount": asset_config.get("min_amount", 0),  # 最小申购金额
                "all_income": user_sum_map.get(asset, 0) + inc_eq_incomes.get(asset, 0),
                "yday_income": user_yes_map.get(asset, 0) + inc_eq_yes_income.get(asset, 0),
                "yday_inc_eq_income": inc_eq_yes_income.get(asset, 0),
                "inc_all_income": inc_total_income.get(asset, 0),
                "day_rate": base_rate + fixed_rate,
                "ladder_rule": AssetInvestmentConfig.ladder_rule_show(ladder_rule, self.is_sub_account),  # 阶梯加息规则
                "fixed_rate": fixed_rate,  # 固定加息利率
                "base_rate": base_rate,  # 基础利率
                # 加息权益数据
                **IncreaseEquityHelper.unpack_user_asset_incr_info(asset, asset_inc_eq_info),
                # 以下字段已废弃，兼容保留
                "yesterday_rate": Decimal(yesterday_rate_info.get(asset, Decimal())),  # 昨日收益率
                "7th_pa": Decimal(seven_days_info.get(asset, Decimal())),  # 七日年化
                "Seventh_pa": Decimal(seven_days_info.get(asset, Decimal())),  # 七日年化
                "inc_yday_income": 0,  # 理财加息券最日收益
            }
        return result

    def calc_show_rate(self, base_rate: Decimal, fixed_rate: Decimal, ladder_rate: Decimal, is_sub_account: bool = False) -> Decimal:
        tmp_rate = base_rate + fixed_rate
        return tmp_rate + ladder_rate if not is_sub_account else tmp_rate

    def get_user_date_interest(self, user_id: int, date: date):
        model = UserDayInterestHistory
        query = (
            model.query.filter(
                model.user_id == user_id,
                model.report_date == date,
                model.status == model.Status.SUCCESS,
            )
            .with_entities(
                model.asset,
                model.interest_amount,
            )
            .all()
        )
        return query

    def get_trans_history(
        self,
        page: int,
        limit: int,
        asset: Optional[str] = None,
        opt: Optional[InvestmentTransferHistory.OptType] = None,
    ):
        model = InvestmentTransferHistory
        query = model.query.filter(
            model.user_id == self.user_id,
            model.status == model.StatusType.SUCCESS,
        ).order_by(model.id.desc())
        if asset:
            query = query.filter(model.asset == asset)
        if opt:
            query = query.filter(model.opt_type == opt)
        records = query.paginate(page, limit, error_out=False)
        result = [
            dict(
                create_time=r.created_at,
                coin_type=r.asset,
                opt_type=r.opt_type,
                amount=r.amount,
                status=r.status,
            )
            for r in records.items
        ]
        return {"page": page, "limit": limit, "total": records.total, "data": result}

    def get_interest_history(
        self,
        page: int,
        limit: int,
        asset: Optional[str] = None,
    ):
        model = UserDayInterestHistory
        query = model.query.filter(
            model.user_id == self.user_id,
            model.status == model.Status.SUCCESS,
        ).order_by(model.report_date.desc())
        if asset:
            query = query.filter(model.asset == asset)
        records = query.paginate(page, limit, error_out=False)
        result = [
            dict(
                create_time=r.created_at,
                coin_type=r.asset,
                amount=r.interest_amount,
                opt_type=InvestmentTransferHistory.OptType.INTEREST,  # 兼容旧版app
                status=InvestmentTransferHistory.StatusType.SUCCESS,  # 兼容旧版app
            )
            for r in records.items
        ]
        return {"page": page, "limit": limit, "total": records.total, "data": result}


class BalanceTransferOperation(object):
    def __init__(self, user: User, transfer_from: int, transfer_to: int, asset: str, amount: Decimal):
        self.user = user
        self.user_id = user.id
        self.transfer_from = transfer_from
        self.transfer_to = transfer_to
        # validate transfer.
        self.validate_account_id()
        self.asset = asset
        self.amount = quantize_amount(amount, PrecisionEnum.COIN_PLACES)
        self.investment_account_id = AssetInvestmentConfig.ACCOUNT_ID
        self.server_client = ServerClient(current_app.logger)
        self.check_asset_valid(self.asset)

    def validate_account_id(self):
        if self.transfer_to not in [SPOT_ACCOUNT_ID, AssetInvestmentConfig.ACCOUNT_ID]:
            raise InvalidArgument(self.transfer_to)

        if self.transfer_from not in [SPOT_ACCOUNT_ID, AssetInvestmentConfig.ACCOUNT_ID]:
            raise InvalidArgument(self.transfer_from)

        if self.transfer_from == self.transfer_to:
            raise InvalidArgument(self.transfer_from)

    def get_user_balance(self, account_id: int) -> Decimal:
        result = self.server_client.get_user_balances(self.user_id, self.asset, account_id=account_id)
        return Decimal(result.get(self.asset, {}).get("available", Decimal()))

    def get_user_real_invest_balance(self):
        return self.get_user_balance(AssetInvestmentConfig.ACCOUNT_ID)

    def transfer(self):
        if (conf := get_asset_config(self.asset)) and not conf.account_transfer_enabled:
            raise TransferNotAllowed
        cfg = InvestmentConfigCache().get_asset_config(self.asset)
        min_amount = Decimal(cfg.get("min_amount", 0))
        if self.transfer_from == SPOT_ACCOUNT_ID:
            if self.amount < min_amount:
                raise InvalidArgument(message=_("不得低于最小申购数量 %(min_amount)s %(asset)s", min_amount=min_amount, asset=self.asset))
        else:
            # 剩余数量小于最小数量
            remain_amount = self.get_user_real_invest_balance() - self.amount
            if remain_amount != 0 and remain_amount < min_amount:
                raise InvalidArgument(message=_("赎回后剩余数量不能低于 %(min_amount)s %(asset)s", min_amount=min_amount, asset=self.asset))

        if self.transfer_from == SPOT_ACCOUNT_ID and self.transfer_to == AssetInvestmentConfig.ACCOUNT_ID:
            if self.asset in SiteSettings.forbidden_investment_transfer_in_assets:
                raise TransferNotAllowed
            self.transfer_to_invest()
        elif self.transfer_from == AssetInvestmentConfig.ACCOUNT_ID and self.transfer_to == SPOT_ACCOUNT_ID:
            self.transfer_to_spot()
        else:
            raise InvestmentAccountNotFound

    def transfer_to_invest(self):
        from app.business.user import require_user_not_only_withdrawal
        from app.business.credit import credit_user_has_unflat_asset

        require_user_not_only_withdrawal(self.user)

        if credit_user_has_unflat_asset(self.user):
            # 授信用户不允许划转资产到理财账户
            raise TransferNotAllowed(message=_("授信用户不允许划转资产到理财账户。"))
        if self.get_user_balance(account_id=SPOT_ACCOUNT_ID) < self.amount:
            raise InvalidArgument
        record = self.add_balance_history(InvestmentTransferHistory.OptType.IN, InvestmentTransferHistory.StatusType.CREATE)
        self.do_transfer(record)

    def transfer_to_spot(self):
        total_investment_amount = self.get_user_real_invest_balance()
        if total_investment_amount < self.amount:
            raise InvalidArgument

        record = self.add_balance_history(InvestmentTransferHistory.OptType.OUT, InvestmentTransferHistory.StatusType.CREATE)
        self.do_transfer(record)

    def add_balance_history(
        self, opt_type: InvestmentTransferHistory.OptType, status: InvestmentTransferHistory.StatusType
    ) -> InvestmentTransferHistory:
        balance_log = InvestmentTransferHistory(
            user_id=self.user_id,
            investment_account_id=self.investment_account_id,
            asset=self.asset,
            amount=-self.amount if opt_type == InvestmentTransferHistory.OptType.OUT else self.amount,
            opt_type=opt_type,
            status=status,
        )
        db.session.add(balance_log)
        db.session.commit()
        return balance_log

    def check_asset_valid(self, asset: str):
        model = AssetInvestmentConfig
        row = model.query.filter(model.asset == asset, model.status == model.StatusType.OPEN).first()
        if not row:
            raise InvestmentAccountNotOpen
        return row.id

    @classmethod
    def do_transfer(cls, history: InvestmentTransferHistory):
        from app.business.account import AccountTransferLogHelper

        OptType = InvestmentTransferHistory.OptType
        if history.opt_type == OptType.IN:
            transfer_from = SPOT_ACCOUNT_ID
            transfer_to = AssetInvestmentConfig.ACCOUNT_ID
        elif history.opt_type == OptType.OUT:
            transfer_from = AssetInvestmentConfig.ACCOUNT_ID
            transfer_to = SPOT_ACCOUNT_ID
        else:
            raise ValueError(f"do_transfer invalid opt_type {history.opt_type.name}")

        history_id = history.id
        client = ServerClient(current_app.logger)
        try:
            client.add_user_balance(
                user_id=history.user_id,
                asset=history.asset,
                business=history.get_trans_business(),
                business_id=history_id,
                amount=Decimal(-abs(history.amount)),
                detail={"remark": "investment for transfer"},
                account_id=transfer_from,
            )
        except Exception as e:
            current_app.logger.error(f"InvestmentTransfer {history_id} deduct failed: {e!r}")
            raise InvestmentTransferError

        history.status = InvestmentTransferHistory.StatusType.DEDUCTED
        db.session.commit()

        try:
            client.add_user_balance(
                user_id=history.user_id,
                asset=history.asset,
                business=history.get_trans_business(),
                business_id=history_id,
                amount=Decimal(abs(history.amount)),
                detail={"remark": "investment for transfer"},
                account_id=transfer_to,
            )
        except Exception as e:
            current_app.logger.error(f"InvestmentTransfer {history_id} add failed: {e!r}")
            raise InvestmentTransferError

        history.status = InvestmentTransferHistory.StatusType.SUCCESS
        history.success_at = now()
        db.session.commit()
        AccountTransferLogHelper.add_log_by_transfer(history)


class DailyInvestmentReporter(object):
    @classmethod
    def daily_user_investment_interest_report(cls, start_date: date, end_date: date):
        report_date = start_date
        assets = AssetInvestmentConfig.query.all()
        config_map = {row.asset: row for row in assets}
        open_assets = {row.asset for row in assets if row.status == row.StatusType.OPEN}

        investment_user_asset_map = defaultdict(set)
        asset_interest_dict = defaultdict(Decimal)

        # 检查今日理财是否计息完毕
        statis_row = InterestStatisticTime.query.filter(InterestStatisticTime.day_interest_date >= report_date).first()
        if not statis_row:
            current_app.logger.warning(f"{report_date} 理财统计尚未完成 ")
            return False

        interest_model = UserDayInterestHistory
        query = interest_model.query.filter(
            interest_model.report_date == report_date,
        ).all()

        for row in query:
            investment_user_asset_map[row.asset].add(row.user_id)
            asset_interest_dict[row.asset] += row.interest_amount
        asset_user_count = defaultdict(set)

        st_dt = date_to_datetime(report_date)
        et_dt = date_to_datetime(end_date)
        asset_user_balance_map = InvestmentDataProc.get_dt_asset_user_snap_map(et_dt)
        total_balance_map = defaultdict(Decimal)
        for asset, user_balance in asset_user_balance_map.items():
            asset_user_count[asset].update(user_balance.keys())
            total_balance_map[asset] += sum(user_balance.values())

        interest_data = InvestmentDataProc.get_margin_pledge_interest_data(st_dt, et_dt)
        base_day_rates_data = InvestmentDataProc.calc_day_base_rate(interest_data, total_balance_map)

        prices = get_asset_price_by_datetime(convert_datetime(now(), "hour"))
        day_model = DailyInvestmentReport
        # 取昨天的日报
        last_rows = (
            day_model.query.filter(
                day_model.report_date == report_date - timedelta(days=1),
            )
            .order_by(day_model.asset.desc())
            .all()
        )
        last_asset_row_map = {row.asset: row for row in last_rows}
        asset_interest_map = cls._calc_interest_map(report_date, prices)

        all_asset = set(open_assets) | set(asset_interest_dict.keys())
        for asset in all_asset:
            site_user_ids = asset_user_count.get(asset, set())
            interest_user_list = investment_user_asset_map[asset]
            user_count = len(site_user_ids)
            interest_user_count = len(investment_user_asset_map[asset])
            site_user_bitmap = BitMap()
            site_interest_user_bitmap = BitMap()
            site_user_bitmap.update(site_user_ids)
            site_interest_user_bitmap.update(interest_user_list)
            last_side_row = last_asset_row_map.get(asset)
            if last_side_row:
                site_history_user_bitmap: BitMap = (
                    BitMap.deserialize(last_side_row.site_history_user_bitmap) if last_side_row.site_history_user_bitmap else BitMap([])
                )
                site_history_interest_user_bitmap: BitMap = (
                    BitMap.deserialize(last_side_row.site_history_interest_user_bitmap)
                    if last_side_row.site_history_interest_user_bitmap
                    else BitMap([])
                )
                increase_user_bitmap = site_user_bitmap.difference(site_history_user_bitmap)  # 删除老的
                increase_interest_user_bitmap = site_interest_user_bitmap.difference(site_history_interest_user_bitmap)
                increase_investment_user = increase_user_bitmap
                increase_interest_user = investment_user_asset_map[asset] - set(site_history_interest_user_bitmap)
            else:
                site_history_user_bitmap = BitMap()  # 历史理财资产人数
                site_history_interest_user_bitmap = BitMap()  # 历史理财收益人数
                increase_user_bitmap = BitMap()
                increase_interest_user_bitmap = BitMap()
                increase_investment_user = set()
                increase_interest_user = set()
            site_history_user_bitmap.update(site_user_ids)
            site_history_interest_user_bitmap.update(interest_user_list)

            day_rate = asset_interest_dict[asset] / total_balance_map[asset] if total_balance_map[asset] > 0 else Decimal()
            record = DailyInvestmentReport.get_or_create(report_date=report_date, asset=asset)
            record.day_rate = day_rate
            record.amount = quantize_amount(total_balance_map[asset], PrecisionEnum.COIN_PLACES)
            record.usd = total_balance_map[asset] * prices.get(asset, 0)
            record.investment_interest_usd = prices.get(asset, 0) * asset_interest_dict[asset]
            record.investment_interest_amount = asset_interest_dict[asset]
            record.user_count = user_count
            record.interest_user_count = interest_user_count
            record.increase_investment_user = len(increase_investment_user)
            record.increase_interest_user = len(increase_interest_user)
            record.increase_user_bitmap = increase_user_bitmap.serialize()
            record.site_cur_user_bitmap = BitMap(site_user_ids).serialize()
            record.site_cur_interest_user_bitmap = BitMap(interest_user_list).serialize()
            record.increase_interest_user_bitmap = increase_interest_user_bitmap.serialize()
            record.site_history_user_bitmap = site_history_user_bitmap.serialize()
            record.site_history_interest_user_bitmap = site_history_interest_user_bitmap.serialize()
            record.seven_day_rate = 0

            rule_map = config_map[asset].rule_map or {} if asset in config_map else {}
            fixed_rate = Decimal(rule_map.get(AssetInvestmentConfig.ConfigType.FIXED.name, {}).get("rate", 0))
            record.base_rate = base_day_rates_data.get(asset, 0) * 365 + fixed_rate

            # 更新动态字段
            for key, value in asset_interest_map.get(asset, {}).items():
                setattr(record, key, value)
            db.session.add(record)

        # 更新七日年化收益率
        db.session.flush()
        report_data = DailyInvestmentReport.query.filter(DailyInvestmentReport.report_date == report_date).all()
        seven_days_info = Investment7DaysEARCache.get_data()
        for item in report_data:
            item.seven_day_rate = seven_days_info.get(item.asset, Decimal())

        db.session.commit()

    @classmethod
    def _calc_interest_map(cls, report_date: date, prices: Dict[str, Decimal]) -> Dict:
        model = UserDayInterestDetail
        query = model.query.filter(
            model.report_date == report_date,
        ).all()
        result = defaultdict(lambda: defaultdict(Decimal))
        field_map = DailyInvestmentReport.interest_type_field_map()
        
        inc_eq_model = UserDailyIncEquityHistory
        inc_eq_query = inc_eq_model.query.filter(
            inc_eq_model.report_date == report_date,
        ).group_by(
            inc_eq_model.asset,
        ).with_entities(
            inc_eq_model.asset,
            func.sum(inc_eq_model.interest_amount).label("total_interest"),
        ).all()
        
        for row in query:
            result[row.asset][field_map[row.interest_type]] += row.interest_amount * prices.get(row.interest_asset, 0)
        # 计算活期理财权益补贴
        for row in inc_eq_query:
            result[row.asset][DailyInvestmentReport.inc_eq_interest_usd.name] += row.total_interest * prices.get(row.asset, 0)
        return result

class InvestmentDataProc:
    """理财小时计息数据源获取"""

    @classmethod
    def get_asset_hour_rate(cls, hour: datetime) -> Dict[str, Decimal]:
        # 获取指定小时0分的用户理财余额快照
        total_interest = cls.get_margin_pledge_interest_data(hour, hour + timedelta(hours=1))
        asset_user_balance = cls.get_dt_asset_user_snap_map(hour)
        asset_total_balance = defaultdict(Decimal)
        for asset, user_balance in asset_user_balance.items():
            asset_total_balance[asset] += sum(user_balance.values())

        model = AssetInvestmentConfig
        config_rows = model.query.filter(model.status == model.StatusType.OPEN).all()
        valid_asset = set(i.asset for i in config_rows)

        asset_rate = dict()
        for asset, balance in asset_total_balance.items():
            if asset in valid_asset:
                rate = total_interest[asset] / balance * InvestmentHourlyBase.YEAR_HOURS
                asset_rate[asset] = quantize_amount(rate, PrecisionEnum.COIN_PLACES)
        return asset_rate

    @classmethod
    def calc_day_base_rate(cls, interest_data: Dict[str, Decimal], asset_total_balance: Dict[str, Decimal]) -> Dict[str, Decimal]:
        result = {}
        for asset in interest_data.keys():
            balance = asset_total_balance.get(asset, Decimal())
            if balance > 0:
                day_rate = interest_data.get(asset, Decimal()) / balance
                result[asset] = day_rate
        return result

    @classmethod
    def get_dt_balance_snapshot(cls, report_hour: datetime) -> Dict[int, Dict[str, Decimal]]:
        """
        获取指定小时0分的用户理财余额快照
        """
        # 获取快照时间戳
        ts = int(report_hour.replace(minute=0, second=0, microsecond=0).timestamp())
        # 获取快照表
        try:
            table = TradeLogDB.slice_balance_table(ts, interval=3600)
        except Exception:
            # 尝试获取快照前半小时的快照
            table = TradeLogDB.slice_balance_table(ts - 1800, interval=1800)
        if not table:
            current_app.logger.error(f"快照表不存在: {ts}")
            raise InvestmentInterestError(message=f"快照表不存在: {ts}")

        result = table.select(
            "user_id",
            where=f"account={AssetInvestmentConfig.ACCOUNT_ID} and t in ({ServerBalanceType.FROZEN.value})",
        )
        if result:
            raise InvestmentInterestError(message=f"理财账户正在冻结中: {ts}")

        # 查询理财账户余额，只统计AVAILABLE的资产（注意LOCK的资产是定期理财的，不能用于活期计息）
        results = table.select(
            "user_id",
            "asset",
            "SUM(`balance`) `balance`",
            where=f"account={AssetInvestmentConfig.ACCOUNT_ID} and t = {ServerBalanceType.AVAILABLE.value}",
            group_by="`user_id`, `asset`",
        )
        return results

    @classmethod
    def get_dt_asset_user_snap_map(cls, report_hour: datetime) -> Dict[str, Dict[int, Decimal]]:
        rows = cls.get_dt_balance_snapshot(report_hour)
        valid_asset = AssetInvestmentConfig.get_valid_assets()
        asset_user_balance = defaultdict(lambda: defaultdict(Decimal))
        for user_id, asset, balance in rows:
            if balance > 0 and asset in valid_asset:
                asset_user_balance[asset][user_id] = quantize_amount(Decimal(balance), 8)
        return asset_user_balance

    @classmethod
    def get_margin_interest_data(cls, start: datetime, end: datetime) -> Dict[str, Decimal]:
        """
        获取杠杆应收利息数据
        """
        from app.business import BusinessSettings

        margin_interest_rate = BusinessSettings.margin_interest_investment_percent
        assert 0 <= margin_interest_rate <= 1

        model = MarginReceivableInterestHistory
        records = (
            model.query.with_entities(model.asset, func.sum(model.amount * margin_interest_rate).label("sum_amount"))
            .filter(
                model.created_at >= start,
                model.created_at < end,
            )
            .group_by(model.asset)
            .all()
        )

        result = defaultdict(Decimal)
        for record in records:
            result[record.asset] = record.sum_amount or Decimal()

        return dict(result)

    @classmethod
    def get_pledge_interest_data(cls, start: datetime, end: datetime) -> Dict[str, Decimal]:
        """
        获取质押借贷利息数据
        """
        from app.business import BusinessSettings

        pledge_interest_rate = BusinessSettings.pledge_interest_investment_percent
        assert 0 <= pledge_interest_rate <= 1

        model = PledgeInterestHistory
        records = (
            model.query.filter(
                model.created_at >= start,
                model.created_at < end,
            )
            .with_entities(
                model.loan_asset,
                func.sum(model.amount * pledge_interest_rate).label("sum_amount"),
            )
            .group_by(
                model.loan_asset,
            )
            .all()
        )

        result = defaultdict(Decimal)
        for record in records:
            result[record.loan_asset] = record.sum_amount or Decimal()

        return dict(result)

    @classmethod
    def get_margin_pledge_interest_data(cls, start: datetime, end: datetime) -> Dict[str, Decimal]:
        """
        获取杠杆和质押利息数据
        """
        margin_interest_data = cls.get_margin_interest_data(start, end)
        pledge_interest_data = cls.get_pledge_interest_data(start, end)

        total_interest_data = defaultdict(Decimal)
        for asset, amount in margin_interest_data.items():
            total_interest_data[asset] += amount
        for asset, amount in pledge_interest_data.items():
            total_interest_data[asset] += amount

        return total_interest_data

    @classmethod
    def get_valid_assets_config(cls) -> Dict[str, AssetInvestmentConfig]:
        """获取所有开启的理财币种"""
        model = AssetInvestmentConfig
        config_query = model.query.filter(model.status == model.StatusType.OPEN)
        return {v.asset: v for v in config_query}

    @classmethod
    def get_sub_account_ids(cls, user_ids: Set[int]) -> Set[int]:
        sub_account_ids = set()
        model = SubAccount
        for batch_ids in batch_iter(user_ids, 2000):
            sub_rows = (
                model.query.filter(
                    model.main_user_id.in_(batch_ids),
                )
                .with_entities(model.user_id)
                .all()
            )
            sub_account_ids.update(i.user_id for i in sub_rows)
        return sub_account_ids

    @classmethod
    def get_asset_min_amount(cls) -> Dict:
        """每个币种最新 最小申购数量（1u）"""
        prices = PriceManager.assets_to_usd()
        ret = {}
        model = AssetInvestmentConfig
        for asset, price in prices.items():
            min_amount = model.ASSET_MIN_AMOUNT_MAP.get(asset, model.DEFAULT_MIN_AMOUNT)
            ret[asset] = quantize_amount_non_zero(min_amount / price, 1, ROUND_UP) if price else Decimal()
        return ret

class InvestmentHourlyBase:
    """理财小时利息计算器基类"""

    YEAR_HOURS = Decimal("365") * Decimal("24")
    BUS_TYPE: InterestStatisticTime.BusType
    HOUR_MODEL: db.Model

    def get_exists_set(self, user_ids: set[int], report_hour: datetime, unique_field=("user_id", "asset")) -> Set:
        """获取用户小时利息记录"""
        ret = set()
        model = self.HOUR_MODEL
        for batch_ids in batch_iter(user_ids, 2000):
            rows = model.query.filter(
                model.report_hour == report_hour,
                model.user_id.in_(batch_ids),
            ).all()
            ret.update(tuple(getattr(row, field) for field in unique_field) for row in rows)
        return ret

    def save_hour_statistic_time(self, report_hour: datetime):
        """保存小时统计时间"""
        model = InterestStatisticTime
        row = model.get_or_create(bus_type=self.BUS_TYPE)
        row.hour_interest_time = report_hour
        db.session_add_and_commit(row)

    def process_hourly_interest(self, report_hour: datetime):
        raise NotImplementedError

    def run(self, report_hour: datetime):
        self.process_hourly_interest(report_hour)
        self.save_hour_statistic_time(report_hour)


class InvestmentHourlyProc(InvestmentHourlyBase):
    """站内理财小时利息计算器"""

    BUS_TYPE = InterestStatisticTime.BusType.SITE
    HOUR_MODEL = UserHourInterestHistory

    def process_hourly_interest(self, report_hour: datetime):
        """
        计算指定小时的所有用户利息
        """
        # 获取计算小时0分的快照
        current_app.logger.info(f"计算 {datetime_to_str(report_hour)} 小时的所有用户利息")
        asset_user_balance = InvestmentDataProc.get_dt_asset_user_snap_map(report_hour)
        asset_total_balance = defaultdict(Decimal)
        user_ids = set()
        for asset, user_balance in asset_user_balance.items():
            asset_total_balance[asset] += sum(user_balance.values())
            user_ids.update(user_balance.keys())

        # 获取本小时的总利息
        start_time = report_hour
        end_time = report_hour + timedelta(hours=1)
        total_interest = InvestmentDataProc.get_margin_pledge_interest_data(start_time, end_time)

        asset_rate = dict()
        for asset, balance in asset_total_balance.items():
            if asset in total_interest and balance > 0:
                asset_rate[asset] = quantize_amount(total_interest[asset] / balance, PrecisionEnum.COIN_PLACES)
            else:
                asset_rate[asset] = Decimal()

        sub_account_ids = InvestmentDataProc.get_sub_account_ids(user_ids)
        asset_config = InvestmentDataProc.get_valid_assets_config()

        # 计算用户利息
        asset_user_interest = defaultdict(lambda: defaultdict(dict))
        for asset, user_balance in asset_user_balance.items():
            for user_id, balance in user_balance.items():
                if asset not in asset_config:
                    continue

                config = asset_config[asset]
                base_rate = asset_rate[asset]
                is_sub_account = user_id in sub_account_ids
                rule_map = config.rule_map or {}
                interest_data = self.calculate_user_hourly_interest(balance, base_rate, rule_map, is_sub_account)
                asset_user_interest[asset][user_id] = interest_data

        self._save_hourly_interest(asset_user_interest, report_hour)

        return asset_user_interest

    def _save_hourly_interest(self, asset_user_interest: Dict[str, Dict[int, Dict[str, Decimal]]], report_hour: datetime):
        batch_size = 500

        user_ids = set()
        for user_interests in asset_user_interest.values():
            user_ids.update(user_interests.keys())

        exist_set = self.get_exists_set(user_ids, report_hour, ("user_id", "asset"))
        exist_detail_set = self.get_exists_detail_set(user_ids, report_hour)

        for asset, user_interests in asset_user_interest.items():
            user_ids = list(user_interests.keys())
            for batch_ids in batch_iter(user_ids, batch_size):
                insert_rows = []
                for user_id in batch_ids:
                    interest_data = user_interests[user_id]
                    total_interest = sum(i.get("amount", Decimal()) for i in interest_data.values())
                    if total_interest and (user_id, asset) not in exist_set:
                        total_row = UserHourInterestHistory(
                            report_hour=report_hour,
                            user_id=user_id,
                            asset=asset,
                            interest_asset=asset,
                            interest_amount=total_interest,
                        )
                        insert_rows.append(total_row)

                    for i_type in InterestType:
                        if not (data := interest_data.get(i_type)):
                            continue
                        if not (amount := data["amount"]):
                            continue
                        if (user_id, asset, i_type) in exist_detail_set:
                            continue

                        detail_row = UserHourInterestDetail(
                            report_hour=report_hour,
                            user_id=user_id,
                            asset=asset,
                            interest_type=i_type,
                            interest_asset=asset,
                            interest_amount=amount,
                            rate=data["rate"],
                            balance=data["balance"],
                        )
                        insert_rows.append(detail_row)

                if insert_rows:
                    try:
                        db.session.bulk_save_objects(insert_rows)
                        db.session.commit()
                    except IntegrityError as e:
                        current_app.logger.error(f"InvestmentHourlyProc save interest IntegrityError: {e!r}")

    def get_exists_detail_set(self, user_ids: set[int], report_hour: datetime) -> Set:
        """获取用户小时利息记录"""
        ret = set()
        model = UserHourInterestDetail
        for batch_ids in batch_iter(user_ids, 2000):
            rows = (
                model.query.filter(
                    model.report_hour == report_hour,
                    model.user_id.in_(batch_ids),
                )
                .with_entities(
                    model.user_id,
                    model.asset,
                    model.interest_type,
                )
                .all()
            )
            ret.update((row.user_id, row.asset, row.interest_type) for row in rows)
        return ret

    def calculate_user_hourly_interest(
        self,
        balance: Decimal,
        base_rate: Decimal,
        rule: Dict,
        is_sub_account: bool,
    ) -> Dict[InterestType, Dict]:
        """
        计算单个用户单个币种的小时利息，利息只保留8位有效小数
        """

        # 计算基础利息
        base_data = self._calculate_base_interest(balance, base_rate)

        # 计算阶梯补贴
        ladder_rule = rule.get(AssetInvestmentConfig.ConfigType.LADDER.name, {})
        ladder_data = self._calculate_ladder_interest(balance, ladder_rule, is_sub_account) if ladder_rule else {}

        # 计算固定补贴
        fixed_rule = rule.get(AssetInvestmentConfig.ConfigType.FIXED.name, {})
        fixed_data = self._calculate_fixed_interest(balance, fixed_rule) if fixed_rule else {}

        data = {
            InterestType.BASE: base_data,
            InterestType.LADDER: ladder_data,
            InterestType.FIXED: fixed_data,
        }
        return data

    def _calculate_base_interest(self, balance: Decimal, rate: Decimal) -> Dict:
        """计算基础利息"""
        # 基础利息 = 余额 * 小时利率
        assert rate >= 0
        base_interest = quantize_amount(balance * rate, PrecisionEnum.COIN_PLACES)
        data = dict(
            amount=base_interest,
            rate=rate * self.YEAR_HOURS,
            balance=balance,
        )
        return data

    def _calculate_ladder_interest(self, balance: Decimal, rule: Dict, is_sub_account: bool) -> Dict:
        """计算阶梯补贴利息"""

        # 计算阶梯利息
        amount_limit = Decimal(rule["limit"])
        ladder_balance = min(balance, amount_limit)
        rate = Decimal(rule["rate"])
        data = dict(
            amount=Decimal(),
            rate=rate,
            balance=ladder_balance,
        )
        if is_sub_account:
            return data

        if amount_limit > 0 and rate > 0:
            # 计算该阶梯的计息余额
            # 阶梯利息 = 余额 * 年化利率 / 365 / 24
            ladder_interest = quantize_amount(ladder_balance * rate / self.YEAR_HOURS, PrecisionEnum.COIN_PLACES)
            data["amount"] = quantize_amount(ladder_interest, PrecisionEnum.COIN_PLACES)
            return data

        return data

    def _calculate_fixed_interest(self, balance: Decimal, rule: Dict) -> Dict:
        """计算固定补贴利息"""
        rate = Decimal(str(rule["rate"]))
        data = dict(
            amount=Decimal(),
            rate=rate,
            balance=balance,
        )

        # 计算固定利息
        if rate > 0:
            # 固定利息 = 余额 * 年化利率 / 365 / 24
            fixed_interest = quantize_amount(balance * rate / self.YEAR_HOURS, PrecisionEnum.COIN_PLACES)
            data["amount"] = quantize_amount(fixed_interest, PrecisionEnum.COIN_PLACES)

        return data

    def notify_hour_interest_data(self, report_hour: datetime):
        model = UserHourInterestDetail
        rows = (
            model.query.filter(model.report_hour == report_hour)
            .group_by(
                model.asset,
                model.interest_type,
            )
            .with_entities(
                model.asset,
                model.interest_type,
                func.sum(model.interest_amount).label("total_interest"),
            )
            .all()
        )

        # 开启了补贴才通知
        cfg_model = AssetInvestmentConfig
        cfg_rows = cfg_model.query.filter(
            cfg_model.status == cfg_model.StatusType.OPEN,
        ).all()

        valid_assets = {
            i.asset for i in cfg_rows if i.rule_map and (Decimal(i.rule_map.get(cfg_model.ConfigType.LADDER.name, {}).get("rate", 0)) > 0)
        }

        new_datas = defaultdict(lambda: defaultdict(Decimal))
        for row in rows:
            if row.asset not in valid_assets:
                continue
            new_datas[row.asset]["总利息"] += row.total_interest
            new_datas[row.asset][row.interest_type.value] += row.total_interest
        prices = PriceManager.assets_to_usd(valid_assets)
        content = f"当前 {report_hour} 小时利息 (USD) 计算情况:\n"
        for asset, interest_type_map in new_datas.items():
            content += f"{asset}："
            for interest_type, interest_amount in interest_type_map.items():
                content += f"{interest_type}：{amount_to_str(interest_amount * prices[asset], 2)}, "
            content += "\n"

        send_alert_notice(content, config["ADMIN_CONTACTS"].get("invest_notice"))
        return content


class InvestmentDailyBase:
    """理财日利息计算器基类"""

    BUS_TYPE: InterestStatisticTime.BusType
    DAY_MODEL: db.Model

    def check_day_hour_interest_ready(self, report_date: date):
        """检查昨日小时利息是否都已处理"""
        last_hour = self._get_day_last_hour(report_date)
        model = InterestStatisticTime
        row = model.get_or_create(bus_type=self.BUS_TYPE)
        if not row or row.hour_interest_time < last_hour:
            raise ValueError(f"{report_date} 昨日 小时利息 未处理")
        yes_day = report_date - timedelta(days=1)
        if row.day_interest_date and row.day_interest_date != yes_day:
            raise ValueError(f"{yes_day} 日利息 未完全处理, {report_date} 无法执行")

    def _get_day_last_hour(self, report_date: date) -> datetime:
        """获取昨日小时"""
        return date_to_datetime(report_date) + timedelta(hours=23)

    def save_day_statistic_time(self, report_date: date):
        """保存日统计时间"""
        model = InterestStatisticTime
        row = model.get_or_create(bus_type=self.BUS_TYPE)
        row.day_interest_date = report_date
        db.session_add_and_commit(row)

    @classmethod
    def get_today_snapshot(self, report_date: date):
        snap_date = report_date + timedelta(days=1)
        return InvestmentDataProc.get_dt_asset_user_snap_map(date_to_datetime(snap_date))

    def filter_zero_balance_record(self, user_asset_item: Dict, snap_data: Dict) -> Iterable:
        # 获取今日0点的理财快照，用于检查用户该币种活期账户资产是否为0
        new_ret = defaultdict(lambda: defaultdict(Decimal))
        for asset, user_interests in user_asset_item.items():
            for user_id, item in user_interests.items():
                if not self._is_user_asset_zero(user_id, asset, snap_data):
                    new_ret[asset][user_id] = item
        return new_ret

    def _is_user_asset_zero(self, user_id: int, asset: str, asset_user_snap_map: Dict[str, Dict[int, Decimal]]) -> bool:
        """
        检查用户今日0点 币种活期理财账户 资产是否为0
        """
        balance = asset_user_snap_map.get(asset, {}).get(user_id, Decimal())
        return balance <= 0

    def get_exists_daily_set(self, user_ids: set[int], report_date: date, unique_field=("user_id", "asset")) -> set:
        """获取用户日利息记录"""
        ret = set()
        model = self.DAY_MODEL
        for batch_ids in batch_iter(user_ids, 2000):
            rows = (
                model.query.filter(
                    model.report_date == report_date,
                    model.user_id.in_(batch_ids),
                )
                .all()
            )
            ret.update(tuple(getattr(row, field) for field in unique_field) for row in rows)
        return ret

    def process_daily_interest(self, report_date: date):
        raise NotImplementedError

    def run(self, report_date: date):
        self.check_day_hour_interest_ready(report_date)
        self.process_daily_interest(report_date)
        self.save_day_statistic_time(report_date)


class InvestmentDailyProc(InvestmentDailyBase):
    """理财日利息聚合器"""
    BUS_TYPE = InterestStatisticTime.BusType.SITE
    DAY_MODEL = UserDayInterestHistory

    def process_daily_interest(self, report_date: date):
        """
        处理指定日期的日利息
        """
        current_app.logger.info(f"聚合 {report_date} 日的所有用户利息")

        asset_user_snap_map = self.get_today_snapshot(report_date)

        asset_user_interests = self.aggregate_daily_interest(report_date)
        valid_interests = self.filter_zero_balance_record(asset_user_interests, asset_user_snap_map)

        user_asset_details = self._aggregate_daily_detail_interest(report_date)
        valid_details = self.filter_zero_balance_record(user_asset_details, asset_user_snap_map)
        self._save_daily_interest(valid_interests, report_date)
        self._save_daily_interest_detail(valid_details, report_date)

    def _save_daily_interest(self, user_asset_interest: Dict[str, Dict[int, Decimal]], report_date: date):
        batch_size = 2000
        all_user_ids = set()
        for user_interests in user_asset_interest.values():
            all_user_ids.update(user_interests.keys())
        exist_set = self.get_exists_daily_set(all_user_ids, report_date, ("user_id", "asset"))

        for asset, user_interests in user_asset_interest.items():
            user_ids = list(user_interests.keys())
            for batch_ids in batch_iter(user_ids, batch_size):
                insert_rows = []
                for user_id in batch_ids:
                    total_interest = user_interests[user_id]
                    if total_interest > 0 and (user_id, asset) not in exist_set:
                        # 创建日利息记录
                        daily_record = UserDayInterestHistory(
                            report_date=report_date,
                            user_id=user_id,
                            asset=asset,
                            interest_asset=asset,
                            interest_amount=total_interest,
                        )
                        insert_rows.append(daily_record)
                if insert_rows:
                    db.session.bulk_save_objects(insert_rows)
                    db.session.commit()

    def aggregate_daily_interest(self, report_date: date):
        start_hour = date_to_datetime(report_date)
        end_hour = start_hour + timedelta(hours=24)
        hour_model = UserHourInterestHistory
        # 汇总24小时利息详情
        hour_records = (
            hour_model.query.filter(hour_model.report_hour >= start_hour, hour_model.report_hour < end_hour)
            .group_by(
                hour_model.user_id,
                hour_model.asset,
            )
            .with_entities(
                hour_model.user_id,
                hour_model.asset,
                func.sum(hour_model.interest_amount).label("total_interest"),
            )
            .all()
        )

        # 用户每日利息
        user_asset_interest = defaultdict(lambda: defaultdict(Decimal))
        for row in hour_records:
            user_asset_interest[row.asset][row.user_id] = row.total_interest

        return user_asset_interest

    def get_exists_detail_daily_set(self, user_ids: set[int], report_date: date) -> set:
        """获取用户日利息详情记录"""
        ret = set()
        model = UserDayInterestDetail
        for batch_ids in batch_iter(user_ids, 2000):
            rows = (
                model.query.filter(
                    model.report_date == report_date,
                    model.user_id.in_(batch_ids),
                )
                .with_entities(
                    model.user_id,
                    model.asset,
                    model.interest_type,
                )
                .all()
            )
            ret.update((row.user_id, row.asset, row.interest_type) for row in rows)
        return ret

    def _aggregate_daily_detail_interest(self, report_date: date):
        start_hour = date_to_datetime(report_date)
        end_hour = start_hour + timedelta(hours=24)
        detail_model = UserHourInterestDetail
        hour_details = (
            detail_model.query.filter(detail_model.report_hour >= start_hour, detail_model.report_hour < end_hour)
            .group_by(
                detail_model.user_id,
                detail_model.asset,
                detail_model.interest_type,
            )
            .with_entities(
                detail_model.user_id,
                detail_model.asset,
                detail_model.interest_type,
                func.sum(detail_model.interest_amount).label("total_interest"),
            )
            .all()
        )
        user_asset_details = defaultdict(lambda: defaultdict(lambda: defaultdict(Decimal)))
        for row in hour_details:
            user_asset_details[row.asset][row.user_id][row.interest_type] = row.total_interest

        return user_asset_details

    def _save_daily_interest_detail(
        self,
        user_asset_details: Dict[str, Dict[int, Dict[InterestType, Decimal]]],
        report_date: date,
    ):
        """保存日利息详情记录"""
        batch_size = 1000
        all_user_ids = set()
        for user_interests in user_asset_details.values():
            all_user_ids.update(user_interests.keys())

        exist_detail_set = self.get_exists_detail_daily_set(all_user_ids, report_date)

        for asset, user_interests in user_asset_details.items():
            user_ids = list(user_interests.keys())
            for batch_ids in batch_iter(user_ids, batch_size):
                insert_rows = []
                for user_id in batch_ids:
                    daily_details = self._aggregate_interest_details(
                        user_asset_details[asset][user_id], report_date, user_id, asset, exist_detail_set
                    )
                    insert_rows.extend(daily_details)

                if insert_rows:
                    db.session.bulk_save_objects(insert_rows)
                    db.session.commit()

    def _aggregate_interest_details(
        self,
        hourly_details: Dict[InterestType, Decimal],
        report_date: date,
        user_id: int,
        asset: str,
        exist_detail_set: Set[Tuple[int, str, InterestType]],
    ):
        """
        生成利息详情记录
        """
        daily_details = []
        for interest_type, interest in hourly_details.items():
            if interest > 0 and (user_id, asset, interest_type) not in exist_detail_set:
                daily_detail = UserDayInterestDetail(
                    report_date=report_date,
                    user_id=user_id,
                    asset=asset,
                    interest_type=interest_type,
                    interest_asset=asset,
                    interest_amount=interest,
                )
                daily_details.append(daily_detail)

        return daily_details

    def clear_fragment_data(self):
        """清理理财账户碎片余额"""
        asset_user_snap_map = InvestmentDataProc.get_dt_asset_user_snap_map(now())
        model = AssetInvestmentConfig
        config_rows = model.get_open_configs()
        min_amount_map = {row.asset: row.min_amount for row in config_rows}

        need_trans_user_ids = set()
        for asset, user_snap_map in asset_user_snap_map.items():
            if asset not in min_amount_map:
                continue
            min_amount = min_amount_map[asset]
            for user_id, amount in user_snap_map.items():
                if amount < min_amount:
                    need_trans_user_ids.add(user_id)

        for user_ids in batch_iter(need_trans_user_ids, 100):
            user_map = {i.id: i for i in User.query.filter(User.id.in_(user_ids)).all()}
            # 获取最新资产
            user_balances = ServerClient().get_users_balances(model.ACCOUNT_ID, user_ids)

            for user_id, balances in user_balances.items():
                for asset, balance in balances.items():
                    if asset not in min_amount_map:
                        continue
                    if balance["frozen"]:
                        continue
                    min_amount = min_amount_map[asset]
                    amount = balance["available"]
                    if not amount or amount >= min_amount:
                        continue
                    operation = BalanceTransferOperation(
                        user=user_map[user_id],
                        transfer_from=model.ACCOUNT_ID,
                        transfer_to=SPOT_ACCOUNT_ID,
                        asset=asset,
                        amount=amount,
                    )
                    operation.transfer()


class InvestmentDailyPayoutBase:
    """理财日利息发放处理器基类"""

    BUS_TYPE: InterestStatisticTime.BusType

    def check_day_interest_ready(self, report_date: date):
        """检查能否进行发放"""
        model = InterestStatisticTime
        row = model.get_or_create(bus_type=self.BUS_TYPE)
        if not row or row.day_interest_date < report_date:
            raise ValueError(f"{report_date} 日利息未处理, 无法发放")

    def get_day_interest_pending_rows(self, report_date: date) -> List:
        raise NotImplementedError

    def update_interest_statistic_time(self, report_date: date):
        """更新利息统计时间"""
        # 确保今天没有 pending 数据才更新
        if self.get_day_interest_pending_rows(report_date):
            return False

        model = InterestStatisticTime
        row = model.get_or_create(bus_type=self.BUS_TYPE)
        row.day_payout_date = report_date
        db.session.commit()
        return True

    def process_daily_interest_payout(self, report_date: date) -> bool:
        pass

    def run(self, report_date: date):
        self.check_day_interest_ready(report_date)
        result = self.process_daily_interest_payout(report_date)
        self.update_interest_statistic_time(report_date)
        return result


class InvestmentDailyPayoutProc(InvestmentDailyPayoutBase):
    """理财日利息发放处理器"""

    BUS_TYPE = InterestStatisticTime.BusType.SITE

    def process_daily_interest_payout(self, report_date: date) -> bool:
        # 聚合日利息
        current_app.logger.info(f"发放 {report_date} 日的所有用户利息")
        interest_results = self.get_day_interest_pending_rows(report_date)
        self._payout_interest(interest_results)

        current_app.logger.info(f"日利息处理完成: {report_date}, 处理条数: {len(interest_results)}")
        return True

    def get_day_interest_pending_rows(self, report_date: date) -> List[UserDayInterestHistory]:
        """获取日利息结果"""
        model = UserDayInterestHistory
        return model.query.filter(model.report_date == report_date, model.status == model.Status.PENDING).all()

    def _payout_interest(self, interest_rows: List[UserDayInterestHistory]):
        """发放利息"""
        client = ServerClient()
        retry = BaseHTTPClient.retry(3)
        # 性能优化：解除 sqlalchemy 跟踪
        db.session.expunge_all()
        for row in interest_rows:
            error = f"send interest to user {row.user_id} {row.interest_asset} {row.interest_amount}"
            try:
                # 调用server接口发放利息
                retry(client.add_user_balance)(
                    user_id=row.user_id,
                    asset=row.interest_asset,
                    amount=str(row.interest_amount),
                    business=BalanceBusiness.INVESTMENT_INTEREST,
                    business_id=row.id,
                    detail={"remark": "investment for interest"},
                    account_id=AssetInvestmentConfig.ACCOUNT_ID,  # 发放到理财账户
                )
                # 更新发放状态
                self._success_payout(row)
            except Exception as e:
                current_app.logger.error(f"{error} error: {e}")

    def _success_payout(self, row: UserDayInterestHistory):
        model = UserDayInterestHistory
        model.query.filter(model.id == row.id).update(
            {
                model.status: model.Status.SUCCESS,
                model.payout_at: now(),
            },
            synchronize_session=False,
        )
        db.session.commit()

    @classmethod
    def update_user_investment_summary_data(cls, report_date: date):
        current_app.logger.info(f"更新用户累计利息数据: {report_date}")
        model = UserDayInterestHistory
        if not model.query.filter(model.report_date == report_date).first():
            return
        sum_query = (
            model.query.filter(
                model.report_date == report_date,
                model.status == model.Status.SUCCESS,
            )
            .group_by(model.user_id, model.asset)
            .with_entities(
                func.sum(model.interest_amount).label("total_amount"),
                model.user_id,
                model.asset,
            )
            .all()
        )
        sum_result = defaultdict(Decimal)
        for v in sum_query:
            sum_result[(v.user_id, v.asset)] += v.total_amount
        all_records = UserInvestmentSummary.query.all()
        old_keys = set()
        for _batch_records in batch_iter(all_records, 5000):
            for _record in _batch_records:
                _record: UserInvestmentSummary
                old_keys.add((_record.user_id, _record.asset))
                _key = (_record.user_id, _record.asset)
                if _key not in sum_result:
                    continue
                _record.amount += sum_result[(_record.user_id, _record.asset)]
                _record.report_date = report_date
        new_keys = set(sum_result.keys()) - old_keys
        new_records = []
        for _key in new_keys:
            _uid, _asset = _key
            new_records.append(UserInvestmentSummary(report_date=report_date, user_id=_uid, asset=_asset, amount=sum_result[_key]))
        for batch_new_records in batch_iter(new_records, 3000):
            db.session.bulk_save_objects(batch_new_records)
        db.session.commit()


class InvestmentScheduleBase:
    HOUR_PROC: InvestmentHourlyBase
    DAY_PROC: InvestmentDailyBase
    PAYOUT_PROC: InvestmentDailyPayoutBase
    BUS_TYPE: InterestStatisticTime.BusType

    def hour_interest_schedule(self):
        """小时计息任务"""
        row = InterestStatisticTime.query.filter(InterestStatisticTime.bus_type == self.BUS_TYPE).first()
        if row and row.hour_interest_time:
            start_hour = row.hour_interest_time + timedelta(hours=1)
        else:
            start_hour = today_datetime()
        now_hour = now().replace(minute=0, second=0, microsecond=0)
        # 当前小时计算传入上一个小时0分的时间
        processor = self.HOUR_PROC()
        while start_hour < now_hour:
            processor.run(start_hour)
            start_hour += timedelta(hours=1)

    def day_interest_schedule(self):
        """日累计计息任务"""
        processor = self.DAY_PROC()
        row = InterestStatisticTime.query.filter(InterestStatisticTime.bus_type == self.BUS_TYPE).first()
        if row.day_interest_date:
            start_date = row.day_interest_date + timedelta(days=1)
        else:
            # 获取日利息未处理的最大日期
            model = UserDayInterestHistory
            start_date = model.query.order_by(model.report_date.desc()).first().report_date
        while start_date < today():
            processor.run(start_date)
            start_date += timedelta(days=1)

    def day_payout_schedule(self):
        """日利息发放任务"""
        processor = self.PAYOUT_PROC()
        row = InterestStatisticTime.query.filter(InterestStatisticTime.bus_type == self.BUS_TYPE).first()
        if row.day_payout_date:
            start_date = row.day_payout_date + timedelta(days=1)
        else:
            # 获取日利息未发放的最大日期
            start_date = self.get_payout_pending_date()
        while start_date < today():
            processor.run(start_date)
            start_date += timedelta(days=1)

    def get_payout_pending_date(self) -> date:
        raise NotImplementedError


class InvestmentSchedule(InvestmentScheduleBase):
    """站内理财任务"""

    BUS_TYPE = InterestStatisticTime.BusType.SITE
    HOUR_PROC = InvestmentHourlyProc
    DAY_PROC = InvestmentDailyProc
    PAYOUT_PROC = InvestmentDailyPayoutProc

    def update_conifg_rate_schedule(self):
        """定期更新配置数据"""
        rate_map = InvestmentDataProc.get_asset_hour_rate(now() - timedelta(hours=1))
        model = AssetInvestmentConfig
        with CacheLock(LockKeys.investment_config()):
            db.session.rollback()
            config_rows = model.query.filter(model.status == model.StatusType.OPEN).all()
            for row in config_rows:
                row.base_rate = rate_map.get(row.asset, Decimal())
        db.session.commit()
        InvestmentConfigCache.reload()
        return rate_map

    def update_conifg_min_amount_schedule(self):
        """定期更新配置数据"""
        min_amount_map = InvestmentDataProc.get_asset_min_amount()
        model = AssetInvestmentConfig
        with CacheLock(LockKeys.investment_config()):
            db.session.rollback()
            config_rows = model.query.all()
            for row in config_rows:
                if row.asset in min_amount_map:
                    row.min_amount = min_amount_map[row.asset]
        db.session.commit()
        InvestmentConfigCache.reload()
        return min_amount_map

    def day_payout_schedule(self):
        """日利息发放任务"""
        super().day_payout_schedule()
        self.update_summary_schedule()

    def get_payout_pending_date(self) -> date:
        """获取日利息未发放的最大日期"""
        model = UserDayInterestHistory
        return model.query.filter(model.status == model.Status.PENDING).order_by(model.report_date).first().report_date

    def update_summary_schedule(self):
        model = UserInvestmentSummary
        # summary 表所有更改一起提交，有数据表示当天已完成
        start_date = model.query.order_by(model.report_date.desc()).first().report_date + timedelta(days=1)
        while start_date < today():
            InvestmentDailyPayoutProc.update_user_investment_summary_data(start_date)
            start_date += timedelta(days=1)

    def clear_fragment_data_schedule(self):
        InvestmentDailyProc().clear_fragment_data()


def calc_month_report_rate(row):
    # 月报年化利率：(理财收益/30)/理财市值*365
    # 2024年后的数据展示
    if row.report_date.year < 2024:
        return 0
    days = (next_month(row.report_date.year, row.report_date.month) - row.report_date).days
    if not row.investment_interest_usd or not row.usd:
        return 0
    return quantize_amount(row.investment_interest_usd / Decimal(days) / row.usd * Decimal("365"), 4)
