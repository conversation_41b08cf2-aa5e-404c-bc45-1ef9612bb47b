# -*- coding: utf-8 -*-

import json
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from typing import Op<PERSON>

from Crypto.Hash import SHA256
from Crypto.PublicKey import RSA
from Crypto.Signature import PKCS1_v1_5
from flask import current_app
from sqlalchemy import func

from . import <PERSON>acheLock, LockKeys, send_credited_deposit_notice_email, PriceManager, UserSettings
from .clients.biz_monitor import biz_monitor
from .email import (
    send_sent_withdrawal_notice_email,
    send_cancelled_withdrawal_notice_email, send_pending_deposit_notice_email,
    send_privacy_asset_deposit_require_kyc_notice_email,
)
from .gift import add_deposit_gift_task
from .kyt import DepositRiskAssessmentManager
from .push import send_credited_deposit_push, send_withdrawal_notice_push
from .risk_control.base import get_deposit_withdrawal_rc_whitelist, batch_add_risk_user
from .voice import send_user_asset_balance_in_disabled_notice
from ..assets.asset import try_get_asset_chain_config
from ..caches.operation import UserAccumulatedAssetDepositNoticeCache
from ..caches import UserDailyDepositAmountCache
from ..caches.user import UserBalanceSumCache
from ..common import BalanceBusiness, BalanceEvent
from ..common import ProducerTopics
from ..config import config
from ..models import Deposit, Withdrawal, db, User, AssetAccumulatedDepositConfigProportion, \
    SpecialAssetAccumulatedDepositConfigProportion, CoinInformation, RiskControlMobileNoticeConfig, RiskUser
from ..producer import exactly_once_producer
from ..utils import datetime_to_time
from ..assets import get_asset_chain_config, get_asset_config
from ..business import SiteSettings
from .alert import send_alert_notice
from .clients import (ServerClient, WalletClient, WalletDepositStatus,
                      WalletWithdrawalStatus)
from ..models.wallet import WithdrawalFeeHistory, DepositAudit, AssetChainSignConfig, WithdrawalCancel
from ..utils import timestamp_to_datetime, now, amount_to_str, current_timestamp, format_percent


def new_deposit(
        wallet_deposit_id: int,
        user_id: int,
        type_: str,
        asset: str,
        chain: str,
        address: str,
        memo: Optional[str],
        amount: Decimal,
        tx_id: str,
        vout: int,
        confirmations: int,
        status: str,
        signature: str,
        senders: list[str] = None,
        attachment: Optional[dict] = None,
                ) -> Deposit | None:
    from app.business.abnormal_deposit import AbnormalDepositBusiness
    # 充值签名校验，暂时只记录错误日志，不作处理。异常充值暂不校验。
    if type_ != Deposit.Type.ABNORMAL.name and not _verify_deposit_signature(wallet_deposit_id, asset, chain, user_id, amount, signature):
        current_app.logger.error(f"new deposit {wallet_deposit_id}-{asset}-{chain} signature error")

    if status == WalletDepositStatus.TOO_SMALL:
        local_status = Deposit.Status.TOO_SMALL
    elif status in (WalletDepositStatus.PROCESSING, WalletDepositStatus.CONFIRMING, WalletDepositStatus.FINISHED):
        local_status = Deposit.Status.PROCESSING
    elif status == WalletDepositStatus.CANCELLED:
        local_status = Deposit.Status.CANCELLED
    else:
        raise ValueError(f"unkown wallet deposit<{wallet_deposit_id}> status: {status}")

    local_attachment = None
    if type_ == Deposit.Type.ABNORMAL.name and isinstance(attachment, dict):
        attachment_str = json.dumps({str(k): str(v) for k, v in attachment.items()})
        if len(attachment_str) <= 1024:
            local_attachment = attachment_str

    if type_ == Deposit.Type.ABNORMAL.name and AbnormalDepositBusiness.find_new_apply_by_deposit_info(
            user_id=user_id,
            tx_id=tx_id,
            chain=chain,
            asset=asset,
    ):
        """
        找回申请提交接口，已存在的tx_id 且状态是非取消、拒绝的，不允许新增提交
        对于 同时存在多笔 chain、asset、tx_id相同，vout不同的 ABNORMAL wallet_deposit 的情况：
            如果user_id相同，则 多条 wallet_deposit，生成的充值记录都是财务帐号的
            如果user_id不同，则 找回订单 只会匹配 1条 wallet_deposit，其他的 wallet_deposit 正常生成充值记录
        """
        # 满足 type是钱包的异常充值 & Web侧有对应的找回订单记录，则 给财务账号生成充值记录入账
        real_user_id = AbnormalDepositBusiness.get_finance_user_id()
    else:
        real_user_id = user_id
    row = Deposit(
        wallet_deposit_id=wallet_deposit_id,
        user_id=real_user_id,
        type=Deposit.Type.ON_CHAIN,
        asset=asset,
        chain=chain,
        address=address,
        amount=amount,
        memo=memo or '',
        tx_id=tx_id,
        vout=vout,
        confirmations=confirmations,
        status=local_status,
        attachment=local_attachment,
    )
    db.session_add_and_commit(row)
    DepositRiskAssessmentManager.new_risk_assessment_request(row, senders=senders)
    if local_status == Deposit.Status.PROCESSING:
        if status == WalletDepositStatus.CONFIRMING:
            _confirm_deposit(row.id, confirmations)
        elif status == WalletDepositStatus.FINISHED:
            _confirm_deposit(row.id, confirmations, finished=True)
        elif status == WalletDepositStatus.PROCESSING:
            send_pending_deposit_notice_email.delay(row.id)
    return row


def _verify_deposit_signature(dep_id: int, asset: str, chain: str, user_id: int, amount: Decimal, signature: str):
    row = AssetChainSignConfig.query.filter(AssetChainSignConfig.asset == asset, AssetChainSignConfig.chain == chain).first()
    if not row:
        current_app.logger.error(f"{asset}-{chain} signature info not found")
        return False
    pk = RSA.import_key(config['DEPOSIT_SIGN_PUB_KEY'][row.group])
    payload = {'id': dep_id, 'asset': asset, 'chain': chain, 'user_id': user_id, 'amount': amount_to_str(amount)}
    payload = json.dumps(payload, separators=',:', sort_keys=True)
    message = SHA256.new(payload.encode())
    return PKCS1_v1_5.new(pk).verify(message, bytes.fromhex(signature))


def transition_deposit_status(
        deposit: Deposit,
        wallet_status: str,
        confirmations: int,
        senders: list[str] = None,
):
    DepositRiskAssessmentManager.new_risk_assessment_request(deposit, senders=senders)
    local_status = deposit.status
    if WalletDepositStatus.equals(wallet_status, local_status):
        if deposit.confirmations != confirmations:
            deposit.confirmations = confirmations
            db.session.commit()
        return
    ex = ValueError(f'cannot transition deposit<{deposit.id}> from {local_status} to {wallet_status}')
    if local_status == Deposit.Status.TOO_SMALL:
        if wallet_status == WalletDepositStatus.PROCESSING:
            _accept_deposit(deposit.id, confirmations)
        elif wallet_status == WalletDepositStatus.CONFIRMING:
            _accept_deposit(deposit.id, confirmations)
            _confirm_deposit(deposit.id, confirmations)
        elif wallet_status == WalletDepositStatus.FINISHED:
            _accept_deposit(deposit.id, confirmations)
            _confirm_deposit(deposit.id, confirmations, finished=True)
        elif wallet_status == WalletDepositStatus.CANCELLED:
            _cancel_deposit(deposit.id, confirmations)
        else:
            raise ex
    elif local_status == Deposit.Status.PROCESSING:
        if wallet_status == WalletDepositStatus.CONFIRMING:
            _confirm_deposit(deposit.id, confirmations)
        elif wallet_status == WalletDepositStatus.FINISHED:
            _confirm_deposit(deposit.id, confirmations, finished=True)
        elif wallet_status == WalletDepositStatus.CANCELLED:
            _cancel_deposit(deposit.id, confirmations)
        else:
            raise ex
    elif local_status == Deposit.Status.CONFIRMING:
        if wallet_status == WalletDepositStatus.FINISHED:
            _finish_deposit(deposit.id, confirmations)
        elif wallet_status == WalletDepositStatus.CANCELLED:
            _cancel_deposit(deposit.id, confirmations)
        else:
            raise ex
    else:
        raise ex


def _accept_deposit(deposit_id: int, confirmations: int):
    """too_small -> processing"""
    with CacheLock(LockKeys.deposit(deposit_id)):
        db.session.rollback()
        deposit = Deposit.query.get(deposit_id)
        if deposit.status != Deposit.Status.TOO_SMALL:
            raise ValueError('invalid status, maybe reentry')
        deposit.confirmations = confirmations
        deposit.status = Deposit.Status.PROCESSING
        db.session.commit()


def re_accept_deposit(deposit_id: int):
    """cancelled -> processing，用于手动修正，非正常业务状态流转"""
    with CacheLock(LockKeys.deposit(deposit_id)):
        db.session.rollback()
        deposit = Deposit.query.get(deposit_id)
        if deposit.status != Deposit.Status.CANCELLED:
            raise ValueError('invalid status')
        deposit.confirmations = 0
        deposit.status = Deposit.Status.PROCESSING
        db.session.commit()


def _deposit_need_audit(deposit) -> bool:
    # 不提前return 同时创建多笔审核记录
    is_need_manual_audit = _deposit_need_manual_audit(deposit)
    is_need_privacy_audit = _privacy_asset_deposit_need_audit(deposit)
    kyt_need_audit = _kyt_need_audit(deposit)
    return is_need_manual_audit or is_need_privacy_audit or kyt_need_audit


def _deposit_need_manual_audit(deposit) -> bool:
    row = DepositAudit.query.filter(
        DepositAudit.deposit_id == deposit.id,
        DepositAudit.type == DepositAudit.Type.MANUAL_AUDIT,
    ).first()
    if row:
        if row.status == DepositAudit.Status.AUDITED:
            return False
        return True
    ac_conf = get_asset_chain_config(deposit.asset, deposit.chain)
    if not ac_conf.deposits_require_manual_audit:
        return False

    start_time = now() - timedelta(hours=24)
    dep_amount = Deposit.query.filter(Deposit.asset == deposit.asset,
                                      Deposit.chain == deposit.chain,
                                      Deposit.user_id == deposit.user_id,
                                      Deposit.created_at >= start_time,
                                      Deposit.status != Deposit.Status.CANCELLED) \
                              .with_entities(func.sum(Deposit.amount)).scalar() or Decimal()
    if dep_amount >= ac_conf.deposits_manual_audit_threshold:
        if row:
            row.status = DepositAudit.Status.AUDIT_REQUIRED
        else:
            row = DepositAudit(
                deposit_id=deposit.id,
                status=DepositAudit.Status.AUDIT_REQUIRED,
                type=DepositAudit.Type.MANUAL_AUDIT,
                user_id=deposit.user_id,
            )
            db.session.add(row)
        db.session.commit()
        return True
    return False


def _cancel_deposit_audit(deposit_id: int):
    rows = DepositAudit.query.filter(
        DepositAudit.deposit_id == deposit_id,
        DepositAudit.status == DepositAudit.Status.AUDIT_REQUIRED,
    ).all()
    for row in rows:
        row.status = DepositAudit.Status.CANCELLED


def _deposit_asset_disabled_by_risk_control(deposit) -> bool:
    user_settings = UserSettings(deposit.user_id)
    if not user_settings.balance_in_assets_disabled_by_risk_control:
        return False
    return deposit.asset in user_settings.balance_in_assets_disabled_by_risk_control


def _deposit_disabled_by_risk_control(deposit) -> bool:
    ac_conf = try_get_asset_chain_config(deposit.asset, deposit.chain)
    if not ac_conf:
        return False
    return ac_conf.deposits_disabled_by_rc


def _privacy_asset_deposit_need_audit(deposit: Deposit) -> bool:
    """ 充值隐私币，且用户未完成KYC """
    row = DepositAudit.query.filter(
        DepositAudit.deposit_id == deposit.id,
        DepositAudit.type == DepositAudit.Type.PRIVACY_WITHOUT_KYC,
    ).first()
    if row:
        # 优先判断Admin的审核状态，不管用户是否KYC
        if row.status == DepositAudit.Status.AUDITED:
            return False
        else:
            # 待审核、已取消（已取消的不会进行入帐）
            return True

    asset_cfg = get_asset_config(deposit.asset)
    if not asset_cfg.is_privacy:
        return False
    user = User.query.get(deposit.user_id)
    if user.kyc_status == User.KYCStatus.PASSED:
        return False

    row = DepositAudit(
        deposit_id=deposit.id,
        status=DepositAudit.Status.AUDIT_REQUIRED,
        type=DepositAudit.Type.PRIVACY_WITHOUT_KYC,
        user_id=deposit.user_id,
    )
    db.session.add(row)
    db.session.commit()
    send_privacy_asset_deposit_require_kyc_notice_email.delay(deposit.id)
    return True


def _user_deposit_by_rc(deposit: Deposit) -> bool:

    def _get_asset_thresholds():
        model = AssetAccumulatedDepositConfigProportion
        rows = model.query.order_by(model.rank_max.asc()).all()
        ret = []
        for row in rows:
            if row.user_period is None or row.asset_period is None:
                continue
            ret.append(
                {
                    'rank_min': row.rank_min * model.UNIT,
                    'rank_max': row.rank_max * model.UNIT,
                    'user_period_dp': row.user_period_dp,
                    'user_period': row.user_period,
                }
            )
        return ret

    def _get_special_asset_threshold(_asset):
        model = SpecialAssetAccumulatedDepositConfigProportion
        row = model.query.filter(
            model.status == model.Status.OPEN,
            model.asset == _asset,
        ).first()
        if not row:
            return
        return {
            'user_period_dp': row.user_period_dp,
            'user_period': row.user_period,
        }

    def _get_asset_circulation(_asset) -> Decimal | None:
        model = CoinInformation
        row = model.query.with_entities(
            model.code,
            model.circulation,
        ).filter(
            model.status == model.Status.VALID,
            model.code == _asset,
        ).first()
        return row.circulation if row else None

    def _calculate_threshold(_asset):
        circulation_usd = circulation * PriceManager.asset_to_usd(_asset)
        thresholds.sort(key=lambda d: d['rank_max'])
        for idx, _threshold in enumerate(thresholds, start=1):
            if _threshold['rank_min'] <= circulation_usd < _threshold['rank_max']:
                return _threshold, f'{idx}档'
        return

    def _get_last_24h_deposit_amount():
        start_time = now() - timedelta(hours=24)
        model = Deposit
        dep_amount = model.query.filter(
            model.asset == deposit.asset,
            model.chain == deposit.chain,
            model.user_id == deposit.user_id,
            model.created_at >= start_time,
            model.status != model.Status.CANCELLED
        ).with_entities(func.sum(model.amount)).scalar() or Decimal()
        return dep_amount

    def _send_notice():
        msg = f'''
【用户维度单笔充值风控】
币种：{asset} ({rank_desc})
用户 {user_id} {msg_body}
用户充值和提现受限，请及时处理
'''
        send_alert_notice(
            msg,
            config["ADMIN_CONTACTS"].get("customer_service"),
            at=config["ADMIN_CONTACTS"]["slack_at"].get("accumulated_deposit_notice")
        )
        mobiles = RiskControlMobileNoticeConfig.get_mobiles(
            RiskControlMobileNoticeConfig.MobileNoticeEventType.ASSET_BALANCE_IN_DISABLED
        )
        for mobile in mobiles:
            send_user_asset_balance_in_disabled_notice.delay(mobile)

    result = deposit
    notice_cache = UserAccumulatedAssetDepositNoticeCache(deposit.user_id, deposit.asset)
    current_ts = current_timestamp(to_int=True)
    if notice_cache.check_ignore(
            current_ts,
            24 * 60 * 60
    ):
        return False
    # TODO：delay a task？
    user_id = result.user_id
    if user_id in get_deposit_withdrawal_rc_whitelist():
        return False
    asset = result.asset
    circulation = _get_asset_circulation(asset)
    if not circulation:
        return False
    threshold = _get_special_asset_threshold(asset)
    rank_desc = '特殊币种'
    if not threshold:
        thresholds = _get_asset_thresholds()
        threshold_tuple = _calculate_threshold(asset)
        if not threshold_tuple:
            return False
        threshold, rank_desc = threshold_tuple
    deposit_amount = _get_last_24h_deposit_amount()  # cost: 0.04s
    if deposit_amount < circulation * threshold['user_period_dp']:
        return False

    check_proportion = (deposit_amount / circulation) if circulation else Decimal()
    value_p = format_percent(check_proportion, 6)
    period_p = format_percent(threshold['user_period_dp'], 6)
    msg_body = f'''（这笔充值+近24H充值数量）：{amount_to_str(deposit_amount)}，
币种流通量为：{amount_to_str(circulation)}，
占比为：{value_p}（阈值{period_p}）；
'''
    detail = '用户' + msg_body
    _send_notice()
    batch_add_risk_user(
        [deposit.user_id],
        RiskUser.Reason.ASSET_BALANCE_IN_DISABLED,
        detail,
        source=asset,
    )
    notice_cache.gen(current_ts)
    return True


def _kyt_need_audit(deposit: Deposit) -> bool:
    row = DepositAudit.query.filter(
        DepositAudit.deposit_id == deposit.id,
        DepositAudit.type.in_(DepositAudit.KYT_TYPES),
    ).first()
    if not row:
        return False
    if row.status == DepositAudit.Status.AUDITED:
        return False
    return True


def _send_deposit_mission_msg(deposit: Deposit):
    if deposit.type != Deposit.Type.ON_CHAIN:
        return
    message = dict(
        event_data=dict(
            amount=deposit.amount,
            amount_asset=deposit.asset,
            order_type="deposit"
        ),
        biz_type=Deposit.__tablename__,
        biz_id=deposit.id,
        timestamp=datetime_to_time(deposit.created_at),
        user_id=deposit.user_id
    )
    exactly_once_producer.send_message(ProducerTopics.DEPOSIT_REWARD, message)


def _confirm_deposit(deposit_id: int, confirmations: int, finished: bool = False):
    """processing -> confirming/finished"""
    if SiteSettings.deposits_disabled_by_risk_control:
        return
    with CacheLock(LockKeys.deposit(deposit_id)):
        db.session.rollback()
        deposit = Deposit.query.get(deposit_id)
        if deposit.status != Deposit.Status.PROCESSING:
            raise ValueError('invalid status, maybe reentry')
        if deposit.is_from_deleted_address and deposit.created_at > now() - timedelta(days=3):
            # 旧地址充值记录，延迟到账
            return
        if DepositRiskAssessmentManager.is_processing_risk_assessment(deposit_id):
            return
        if _deposit_disabled_by_risk_control(deposit):
            return
        if _deposit_asset_disabled_by_risk_control(deposit):
            return
        if _deposit_need_audit(deposit):
            return
        if _user_deposit_by_rc(deposit):
            return
        server_client = ServerClient()
        duplicate_update = not server_client.add_user_balance(
                    deposit.user_id,
                    deposit.asset,
                    deposit.amount,
                    BalanceBusiness.DEPOSIT,
                    deposit.id)
        deposit.confirmations = confirmations
        deposit.status = Deposit.Status.CONFIRMING if not finished else Deposit.Status.FINISHED
        deposit.confirmed_at = datetime.utcnow()
        db.session.commit()
        if not duplicate_update:
            add_deposit_gift_task.delay(
                deposit.user_id, deposit.asset, str(deposit.amount))
            if deposit.type is not Deposit.Type.ABNORMAL:
                send_credited_deposit_notice_email.delay(deposit.id)
                send_credited_deposit_push.delay(deposit.id, current_timestamp(to_int=True))
            UserBalanceSumCache(deposit.user_id).delete()
            UserDailyDepositAmountCache(
                deposit.user_id
            ).add_amount(
                deposit.asset,
                deposit.amount,
                deposit.created_at,
            )
            biz_monitor.increase_counter(
                BalanceEvent.DEPOSIT_COUNT
            )

            biz_monitor.increase_uniq_counter(
                BalanceEvent.DEPOSIT_NUM,
                value=[deposit.user_id]
            )
            _send_deposit_mission_msg(deposit)


def _finish_deposit(deposit_id: int, confirmations: int):
    """confirming -> finished"""
    with CacheLock(LockKeys.deposit(deposit_id)):
        db.session.rollback()
        deposit = Deposit.query.get(deposit_id)
        if deposit.status != Deposit.Status.CONFIRMING:
            raise ValueError('invalid status, maybe reentry')
        deposit.confirmations = confirmations
        deposit.status = Deposit.Status.FINISHED
        db.session.commit()


def _cancel_deposit(deposit_id: int, confirmations: int):
    """too_small/processing -> cancelled, confirming/finished -> cancelled/exception"""
    with CacheLock(LockKeys.deposit(deposit_id)):
        db.session.rollback()
        deposit = Deposit.query.get(deposit_id)
        if deposit.status in (Deposit.Status.TOO_SMALL, Deposit.Status.PROCESSING):
            _cancel_deposit_audit(deposit_id)
            deposit.confirmations = confirmations
            deposit.status = Deposit.Status.CANCELLED
            db.session.commit()
        elif deposit.status in (Deposit.Status.CONFIRMING, Deposit.Status.FINISHED):
            cancelled = False
            # noinspection PyBroadException
            try:
                server_client = ServerClient()
                r = server_client.get_user_balances(deposit.user_id, deposit.asset)
                if r[deposit.asset]['available'] >= deposit.amount:
                    ServerClient().add_user_balance(deposit.user_id,
                                                    deposit.asset,
                                                    -deposit.amount,
                                                    BalanceBusiness.DEPOSIT_CANCELLATION,
                                                    deposit.id)
                    cancelled = True
            except Exception:
                pass

            if cancelled:
                deposit.confirmations = confirmations
                deposit.status = Deposit.Status.CANCELLED
                db.session.commit()
            else:
                deposit.confirmations = confirmations
                deposit.status = Deposit.Status.EXCEPTION
                db.session.commit()
                send_alert_notice(f"钱包充值被取消，请及时处理。id：{deposit.id}，用户id：{deposit.user_id}，"
                                  f"币种：{deposit.asset}-{deposit.chain}，金额：{deposit.amount}",
                                  config["ADMIN_CONTACTS"]["web_notice"],
                                  at=config["ADMIN_CONTACTS"]["slack_at"].get("web_notice"))
        else:
            raise ValueError('invalid status, maybe reentry')


def send_withdrawal(withdrawal_id: int):
    with CacheLock(LockKeys.withdrawal(withdrawal_id)):
        db.session.rollback()
        withdrawal = Withdrawal.query.get(withdrawal_id)
        if withdrawal.status != Withdrawal.Status.AUDITED:
            raise ValueError
        WalletClient().send_withdrawal(
            id_=withdrawal.id,
            user_id=withdrawal.user_id,
            asset=withdrawal.asset,
            chain=withdrawal.chain,
            address=withdrawal.address,
            memo=withdrawal.memo,
            amount=withdrawal.amount,
            attachment=json.loads(withdrawal.attachment) if withdrawal.attachment else None
        )
        withdrawal.status = Withdrawal.Status.PROCESSING
        db.session.commit()


def transition_withdrawal_status(withdrawal: Withdrawal,
                                 wallet_status: str,
                                 confirmations: int,
                                 tx_id: str,
                                 sent_at: Optional[int]):
    local_status = withdrawal.status
    if WalletWithdrawalStatus.equals(wallet_status, local_status):
        if withdrawal.confirmations != confirmations:
            withdrawal.confirmations = confirmations
            db.session.commit()
        return
    ex = ValueError(f'cannot transition withdrawal<{withdrawal.id}> from {local_status} to {wallet_status}')
    if local_status in (Withdrawal.Status.PROCESSING,
                        Withdrawal.Status.CONFIRMING,
                        Withdrawal.Status.FAILED):
        if wallet_status in (WalletWithdrawalStatus.PROCESSING,
                             WalletWithdrawalStatus.CONFIRMING,
                             WalletWithdrawalStatus.FAILED,
                             WalletWithdrawalStatus.FINISHED):
            _update_withdrawal(withdrawal.id, WalletWithdrawalStatus.to_local(wallet_status), confirmations, tx_id, sent_at)
        elif wallet_status == WalletWithdrawalStatus.CANCELLED:
            _cancel_withdrawal(withdrawal.id, confirmations, tx_id, sent_at, cancel_type=WithdrawalCancel.CancelType.WALLET)
        else:
            raise ex
    else:
        raise ex


def _update_withdrawal(withdrawal_id: int, to: Withdrawal.Status, confirmations: int, tx_id: str, sent_at: Optional[int]):
    """processing/confirming/failed -> processing/configming/failed/finished"""
    with CacheLock(LockKeys.withdrawal(withdrawal_id)):
        db.session.rollback()
        withdrawal = Withdrawal.query.get(withdrawal_id)
        if withdrawal.status not in (Withdrawal.Status.PROCESSING,
                                     Withdrawal.Status.CONFIRMING,
                                     Withdrawal.Status.FAILED):
            raise ValueError('invalid status, maybe reentry')
        old_status = withdrawal.status
        withdrawal.status = to
        withdrawal.tx_id = tx_id
        withdrawal.confirmations = confirmations
        if sent_at:
            withdrawal.sent_at = timestamp_to_datetime(sent_at)
        db.session.commit()
        if old_status == Withdrawal.Status.PROCESSING \
                and to in (Withdrawal.Status.CONFIRMING, Withdrawal.Status.FINISHED):
            send_sent_withdrawal_notice_email.delay(withdrawal_id)
            send_withdrawal_notice_push.delay(withdrawal_id, current_timestamp(to_int=True))

            biz_monitor.increase_counter(
                BalanceEvent.WITHDRAWAL_COUNT,
            )

            biz_monitor.increase_uniq_counter(
                BalanceEvent.WITHDRAWAL_NUM,
                value=[withdrawal.user_id],
            )


def _cancel_withdrawal(withdrawal_id: int, confirmations: int, tx_id: str, sent_at: Optional[int],
                       cancel_type: WithdrawalCancel.CancelType = WithdrawalCancel.CancelType.SYSTEM,
                       cancel_user_id: int = WithdrawalCancel.SYSTEM_USER_ID):
    """processing/confirming/failed --> cancelled"""
    from .wallet import add_withdrawal_amount_to_cache
    from .wallet import WithdrawalHelper

    with CacheLock(LockKeys.withdrawal(withdrawal_id)):
        db.session.rollback()
        withdrawal = Withdrawal.query.get(withdrawal_id)
        if withdrawal.status not in (Withdrawal.Status.PROCESSING, Withdrawal.Status.CONFIRMING, Withdrawal.Status.FAILED):
            raise ValueError('invalid status, maybe reentry')
        try:
            batch_params = []
            if withdrawal.fee > 0:
                batch_params.append(dict(
                    user_id=withdrawal.user_id,
                    asset=withdrawal.fee_asset,
                    amount=withdrawal.fee,
                    business=BalanceBusiness.WITHDRAWAL_FEE_CANCELLATION,
                    business_id=withdrawal_id,
                ))
            batch_params.append(dict(
                user_id=withdrawal.user_id,
                asset=withdrawal.asset,
                amount=withdrawal.amount,
                business=BalanceBusiness.WITHDRAWAL_CANCELLATION,
                business_id=withdrawal_id,
            ))
            ServerClient().batch_add_user_balance(batch_params)

        except Exception:
            successful = False
        else:
            successful = True

        withdrawal.status = Withdrawal.Status.CANCELLED if successful \
            else Withdrawal.Status.CANCELLATION_FAILED
        withdrawal.tx_id = tx_id
        withdrawal.confirmations = confirmations
        if sent_at:
            withdrawal.sent_at = timestamp_to_datetime(sent_at)
        if successful:
            db.session.add(WithdrawalFeeHistory(
                withdrawal_id=withdrawal.id,
                user_id=withdrawal.user_id,
                asset=withdrawal.fee_asset,
                amount=-withdrawal.fee,
                fee_type=WithdrawalFeeHistory.FeeType.FEE_BACK
            ))
        WithdrawalHelper.add_cancel_record(withdrawal_id, withdrawal.user_id, cancel_type, cancel_user_id)
        db.session.commit()
        add_withdrawal_amount_to_cache(withdrawal.user_id, withdrawal.asset, -withdrawal.amount, withdrawal.created_at)
        send_cancelled_withdrawal_notice_email.delay(withdrawal_id)
        do_withdrawal_canceled_action(withdrawal_id)


def cancel_withdrawal(withdrawal_id: int, *, accquire_lock: bool = True,
                      cancel_type: WithdrawalCancel.CancelType = WithdrawalCancel.CancelType.SYSTEM,
                      cancel_user_id: int = WithdrawalCancel.SYSTEM_USER_ID) -> bool:
    """created/audit_required/audited -> cancelled"""
    if accquire_lock:
        with CacheLock(LockKeys.withdrawal(withdrawal_id)):
            return _cancel_withdrawal_no_lock(withdrawal_id, cancel_type, cancel_user_id)
    else:
        return _cancel_withdrawal_no_lock(withdrawal_id, cancel_type, cancel_user_id)


def _cancel_withdrawal_no_lock(withdrawal_id,
                               cancel_type: WithdrawalCancel.CancelType = WithdrawalCancel.CancelType.SYSTEM,
                               cancel_user_id: int = WithdrawalCancel.SYSTEM_USER_ID):
    from .wallet import add_withdrawal_amount_to_cache, WithdrawalHelper
    from .risk_control.withdrawal import WithdrawalAuditHelper

    db.session.rollback()
    withdrawal = Withdrawal.query.get(withdrawal_id)
    if withdrawal.status not in (Withdrawal.Status.CREATED,
                                 Withdrawal.Status.AUDIT_REQUIRED,
                                 Withdrawal.Status.AUDITED):
        return False

    WithdrawalAuditHelper.cancel_withdrawal_audit(withdrawal.id)

    successful = True
    if withdrawal.status in ((Withdrawal.Status.AUDIT_REQUIRED,
                              Withdrawal.Status.AUDITED)):
        try:
            batch_params = []
            if withdrawal.fee > 0:
                batch_params.append(dict(
                    user_id=withdrawal.user_id,
                    asset=withdrawal.fee_asset,
                    amount=withdrawal.fee,
                    business=BalanceBusiness.WITHDRAWAL_FEE_CANCELLATION,
                    business_id=withdrawal_id,
                ))
            batch_params.append(dict(
                user_id=withdrawal.user_id,
                asset=withdrawal.asset,
                amount=withdrawal.amount,
                business=BalanceBusiness.WITHDRAWAL_CANCELLATION,
                business_id=withdrawal_id,
            ))
            ServerClient().batch_add_user_balance(batch_params)

        except Exception:
            successful = False

    withdrawal.status = Withdrawal.Status.CANCELLED if successful \
        else Withdrawal.Status.CANCELLATION_FAILED

    if successful:
        db.session.add(WithdrawalFeeHistory(
            withdrawal_id=withdrawal.id,
            user_id=withdrawal.user_id,
            asset=withdrawal.fee_asset,
            amount=-withdrawal.fee,
            fee_type=WithdrawalFeeHistory.FeeType.FEE_BACK
        ))
    WithdrawalHelper.add_cancel_record(withdrawal_id, withdrawal.user_id, cancel_type, cancel_user_id)
    db.session.commit()
    add_withdrawal_amount_to_cache(withdrawal.user_id, withdrawal.asset, -withdrawal.amount, withdrawal.created_at)
    do_withdrawal_canceled_action(withdrawal.id)
    return successful


def do_withdrawal_canceled_action(withdrawal_id: int):
    from app.business.direct_fiat.helper import notify_direct_fiat_withdrawal_canceled

    notify_direct_fiat_withdrawal_canceled.delay(withdrawal_id)


def retry_cancellation_failed_withdrawal(withdrawal_id: int) -> bool:
    with CacheLock(LockKeys.withdrawal(withdrawal_id)):
        db.session.rollback()
        withdrawal = Withdrawal.query.get(withdrawal_id)
        if withdrawal.status != Withdrawal.Status.CANCELLATION_FAILED:
            return False
        if withdrawal.approved_by_user_at is None:
            return False
        batch_params = []
        server_client = ServerClient()
        if withdrawal.fee > 0:
            params = dict(
                user_id=withdrawal.user_id,
                asset=withdrawal.fee_asset,
                business=BalanceBusiness.WITHDRAWAL_FEE_CANCELLATION,
                business_id=withdrawal_id,
            )
            if not server_client.asset_query_business(**params):
                params['amount'] = withdrawal.fee
                batch_params.append(params)

        params = dict(
            user_id=withdrawal.user_id,
            asset=withdrawal.asset,
            business=BalanceBusiness.WITHDRAWAL_CANCELLATION,
            business_id=withdrawal_id,
        )
        if not server_client.asset_query_business(**params):
            params['amount'] = withdrawal.amount
            batch_params.append(params)
        if batch_params:
            server_client.batch_add_user_balance(batch_params)

        withdrawal.status = Withdrawal.Status.CANCELLED
        db.session.add(WithdrawalFeeHistory(
            withdrawal_id=withdrawal.id,
            user_id=withdrawal.user_id,
            asset=withdrawal.fee_asset,
            amount=-withdrawal.fee,
            fee_type=WithdrawalFeeHistory.FeeType.FEE_BACK
        ))
        db.session.commit()
        return True
