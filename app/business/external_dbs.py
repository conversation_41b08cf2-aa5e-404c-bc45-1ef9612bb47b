# -*- coding: utf-8 -*-
import time
from datetime import date, datetime, timedelta
from enum import Enum

from dateutil.relativedelta import relativedelta
from collections import defaultdict
from decimal import Decimal
from functools import partial
from typing import Tuple, List, Dict, Iterable, Type, Optional, Union, Generator
from logging import getLogger

from pymysql.err import ProgrammingError
from sqlalchemy import func

from app.models.staking import StakingAccount

from .clients.server import SPOT_ACCOUNT_ID, MAX_ORDER_ACCOUNT_ID
from ..models import AssetInvestmentConfig, AssetPrice, CreditAssetHistory

from ..caches import PerpetualCoinTypeCache
from ..common import AccountBalanceType, PerpetualMarketType, PrecisionEnum
from ..exceptions import InvalidArgument, DataNotReady
from ..utils import (
    ExternalDB, DefaultDictWithArg, current_timestamp,
    timestamp_to_datetime, quantize_amount, amount_to_str,
    next_month, timestamp_to_date, ExternalTable, today_timestamp_utc,
)
from .prices import PriceManager

_logger = getLogger(__name__)


class ExchangeLogDB(ExternalDB):

    config = 'EXCHANGE_LOG_MYSQL'
    tables = {
        'user_balance': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'user_id': 'int(10) unsigned NOT NULL',
                'account': 'int(10) NOT NULL DEFAULT 0',
                'asset': 'varchar(30) NOT NULL',
                'balance': 'decimal(34, 16) NOT NULL',
                'locked_balance': 'decimal(34, 16) NOT NULL',
                'balance_usd': 'decimal(30, 16) NOT NULL',
                'margin_due_amount': 'decimal(34, 16) NOT NULL',
                'margin_due_amount_usd': 'decimal(30, 16) NOT NULL'
            },
            (
                'UNIQUE KEY `user_account_asset_uniq`'
                ' (`user_id`, `account`, `asset`)',
                'KEY `asset` (`asset`)'
            )
        ),
        'user_max_position': ExternalDB.TableConfig(
            {
                'id': 'int(20) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'user_id': 'int(10) unsigned NOT NULL',
                'max_usd': 'decimal(34, 2) NOT NULL',
                'update_ts': 'int(12) NOT NULL',
            },
            (
                'UNIQUE KEY `user_id_uniq` (`user_id`)',
                'KEY `update_ts` (`update_ts`)',
            )
        ),
        'user_account_balance': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'user_id': 'int(10) unsigned NOT NULL',
                'account_type': 'varchar(30) NOT NULL',
                'asset': 'varchar(30) NOT NULL',
                'balance': 'decimal(34, 16) NOT NULL',  # includes margin loan assets.
                'balance_usd': 'decimal(30, 16) NOT NULL',
            },
            (
                'UNIQUE KEY `user_account_asset_uniq`'
                ' (`user_id`, `account_type`, `asset`)',
                'KEY `asset` (`asset`)',
                'KEY `idx_type_balance_user_id` (`account_type`, balance DESC, `user_id`)',
            )
        ),
        'user_top_balance_rank': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'user_id': 'int(10) unsigned NOT NULL',
                'account_type': 'varchar(30) NOT NULL',
                'asset': 'varchar(30) NOT NULL',
                'balance': 'decimal(34, 16) NOT NULL',
                'balance_usd': 'decimal(30, 16) NOT NULL',
            },  # 存储top1000排名的资产, user_id=0时为该维度下的总资产数据
            (
                'KEY `idx_account_type_asset` (`account_type`, `asset`)',
            )
        ),
        'user_account_balance_sum': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'user_id': 'int(10) unsigned NOT NULL',
                'balance_usd': 'decimal(30, 16) NOT NULL',
            },
            (
                'KEY `user_id` (`user_id`)',
            )
        ),
        'user_balance_sum': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'user_id': 'int(10) unsigned NOT NULL',
                'account': 'int(10) NOT NULL DEFAULT 0',
                'balance_usd': 'decimal(30, 16) NOT NULL',
                'margin_due_amount_usd': 'decimal(30, 16) NOT NULL'
            },
            (
                'UNIQUE KEY `user_account_uniq` (`user_id`, `account`)',
            )
        ),
        'user_slice_balance': ExternalDB.TableConfig(
            # this table needs to ensure compatibility
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'report_date': 'date NOT NULL',
                'user_id': 'int(10) unsigned NOT NULL',
                'account': 'int(10) NOT NULL DEFAULT 0',
                'asset': 'varchar(30) NOT NULL',
                'balance': 'decimal(34, 16) NOT NULL',
                'lock_balance': 'decimal(34, 16) NOT NULL',
                'market_value': 'decimal(30, 16) NOT NULL',  # balance usd
                'margin_unflat_amount': 'decimal(34, 16) NOT NULL',
                'margin_unflat_amount_usd': 'decimal(30, 16) NOT NULL'
            },
            (
                'UNIQUE KEY `user_id_report_date_asset_account`'
                ' (`user_id`, `report_date`, `asset`, `account`)',
                'KEY `asset` (`asset`)'
            )
        ),
        'user_slice_balance_log': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'report_date': 'date NOT NULL',
                'table': 'varchar(64) NOT NULL',
                'status': 'varchar(30) NOT NULL',
                'remark': 'varchar(128) DEFAULT NULL',
            },
            (
                'UNIQUE KEY `report_date_table`',
                ' (`report_date`, `table`)'
            )
        ),
        'user_margin': ExternalDB.TableConfig(  # not used
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'user_id': 'int(10) unsigned NOT NULL',
                'account': 'int(10) NOT NULL DEFAULT 0',
                'market': 'varchar(30) NOT NULL',
                'asset': 'varchar(30) NOT NULL',
                'loan_amount': 'decimal(34, 16) NOT NULL',
                'interest_amount': 'decimal(34, 16) NOT NULL',
                'loan_amount_usd': 'decimal(30, 16) NOT NULL',
                'interest_amount_usd': 'decimal(30, 16) NOT NULL',
            },
            (
                'KEY `user_id` (`user_id`)',
            )
        ),
        'user_amm': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'user_id': 'int(10) unsigned NOT NULL',
                'asset': 'varchar(30) NOT NULL',
                'amount': 'decimal(34, 16) NOT NULL',
                'usd': 'decimal(30, 16) NOT NULL',
            },
            (
                'KEY `user_id` (`user_id`)',
            )
        ),
        'user_pledge': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'user_id': 'int(10) unsigned NOT NULL',
                'account': 'int(10) NOT NULL',
                'asset': 'varchar(30) NOT NULL',
                'total_unflat_amount': 'decimal(34, 16) NOT NULL',
                'interest_amount': 'decimal(34, 16) NOT NULL',
                'total_unflat_amount_usd': 'decimal(30, 16) NOT NULL',
                'interest_amount_usd': 'decimal(30, 16) NOT NULL',
            },
            (
                'KEY `user_id` (`user_id`)',
            )
        ),
        'user_pre_trading': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'user_id': 'int(10) unsigned NOT NULL',
                'issue_asset': 'varchar(30) NOT NULL',
                'issue_amount': 'decimal(34, 16) NOT NULL',
                'pledge_asset': 'varchar(30) NOT NULL',
                'pledge_amount': 'decimal(34, 16) NOT NULL',
                'issue_usd': 'decimal(30, 16) NOT NULL',
                'pledge_usd': 'decimal(30, 16) NOT NULL',
            },
            (
                'KEY `user_id` (`user_id`)',
            )
        ),
        'user_slice_perpetual_balance': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'report_date': 'date NOT NULL',
                'user_id': 'int(10) unsigned NOT NULL',
                'asset': 'varchar(30) NOT NULL',
                'balance': 'decimal(34, 16) NOT NULL',
                'balance_usd': 'decimal(30, 16) NOT NULL',
            },
            (
                'UNIQUE KEY `user_id_report_date_asset`'
                ' (`user_id`, `report_date`, `asset`)',
                'KEY `asset` (`asset`)'
            )
        ),
        'user_slice_account_balance_sum': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'report_date': 'date NOT NULL',
                'user_id': 'int(10) unsigned NOT NULL',
                'account_type': 'varchar(30) NOT NULL',
                'balance_usd': 'decimal(30, 16) NOT NULL',
            },
            (
                'UNIQUE KEY `user_report_date_account_uniq` (`user_id`, `report_date`, `account_type`)',
                'KEY `report_date` (`report_date`)'
            )
        ),

        'user_account_type_balance_sum': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'user_id': 'int(10) unsigned NOT NULL',
                'account_type': 'varchar(30) NOT NULL',
                'balance_usd': 'decimal(30, 16) NOT NULL',
            },
            (
                'UNIQUE KEY `user_account_uniq` (`user_id`, `account_type`)',
            )
        ),
        'user_slice_profit_loss_snapshot': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'report_date': 'date NOT NULL',
                'user_id': 'int(10) unsigned NOT NULL',
                'account_type': 'varchar(30) NOT NULL',
                'balance_usd': 'decimal(30, 16) NOT NULL',
                'last_day_usd': 'decimal(30, 16) NOT NULL',
                'net_deposit_usd': 'decimal(30, 16) NOT NULL DEFAULT 0',
                'net_transfer_in_usd': 'decimal(30, 16) NOT NULL DEFAULT 0',
                'total_deposit_usd': 'decimal(30, 16) NOT NULL DEFAULT 0',
                'total_transfer_in_usd': 'decimal(30, 16) NOT NULL DEFAULT 0',
                'profit_usd': 'decimal(30, 16) NOT NULL DEFAULT 0',
                'profit_rate': 'decimal(30, 16) NOT NULL DEFAULT 0',
            }, (
                'UNIQUE KEY `user_report_date_account_uniq` (`user_id`, `report_date`, '
                '`account_type`)',
                'KEY `report_date` (`report_date`)'
            )
        ),
        # 币种维度盈亏快照
        'user_slice_asset_profit_loss_snapshot': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'report_date': 'date NOT NULL',
                'user_id': 'int(10) unsigned NOT NULL',
                'asset': 'varchar(30) NOT NULL',
                'account_type': 'varchar(30) NOT NULL',
                'balance_usd': 'decimal(30, 16) NOT NULL',
                'last_day_usd': 'decimal(30, 16) NOT NULL',
                'net_transfer_in_usd': 'decimal(30, 16) NOT NULL DEFAULT 0',
                'total_transfer_in_usd': 'decimal(30, 16) NOT NULL DEFAULT 0',
                'profit_usd': 'decimal(30, 16) NOT NULL DEFAULT 0',
                'profit_rate': 'decimal(30, 16) NOT NULL DEFAULT 0',
            }, (
                'UNIQUE KEY `user_report_date_asset_uniq` (`user_id`, `report_date`, '
                '`asset`)',
            )
        ),
        # 用户每日交易额统计表
        'user_slice_account_deal_value_sum': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'report_date': 'date NOT NULL',
                'user_id': 'int(10) unsigned NOT NULL',
                'account_type': 'varchar(30) NOT NULL',
                'deal_usd': 'decimal(30, 16) NOT NULL',
            }, (
                'UNIQUE KEY `user_report_date_account_uniq` (`user_id`, `report_date`, '
                '`account_type`)',
                'KEY `report_date` (`report_date`)'
            )
        ),
        # 现货深度快照表
        'spot_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'base_asset_rate': 'decimal(30, 16) NOT NULL',
                'quote_asset_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `depth` (`depth`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        # 合约深度快照表
        'perpetual_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'money_rate': 'decimal(30, 16) NOT NULL',
                'stock_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        # CoinEx现货全量深度快照表
        'spot_full_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'base_asset_rate': 'decimal(30, 16) NOT NULL',
                'quote_asset_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `depth` (`depth`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        # CoinEx合约全量深度快照表
        'perpetual_full_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'money_rate': 'decimal(30, 16) NOT NULL',
                'stock_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        # 第三方交易所合约深度快照表
        'binance_perpetual_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'money_rate': 'decimal(30, 16) NOT NULL',
                'stock_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'okx_perpetual_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'money_rate': 'decimal(30, 16) NOT NULL',
                'stock_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'huobi_perpetual_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'money_rate': 'decimal(30, 16) NOT NULL',
                'stock_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'bybit_perpetual_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'money_rate': 'decimal(30, 16) NOT NULL',
                'stock_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'kucoin_perpetual_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'money_rate': 'decimal(30, 16) NOT NULL',
                'stock_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'gate_perpetual_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'money_rate': 'decimal(30, 16) NOT NULL',
                'stock_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'bitget_perpetual_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'money_rate': 'decimal(30, 16) NOT NULL',
                'stock_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'bingx_perpetual_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'money_rate': 'decimal(30, 16) NOT NULL',
                'stock_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),

        # 第三方交易所现货深度快照表
        'binance_spot_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'base_asset_rate': 'decimal(30, 16) NOT NULL',
                'quote_asset_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'okx_spot_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'base_asset_rate': 'decimal(30, 16) NOT NULL',
                'quote_asset_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'huobi_spot_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'base_asset_rate': 'decimal(30, 16) NOT NULL',
                'quote_asset_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'bybit_spot_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'base_asset_rate': 'decimal(30, 16) NOT NULL',
                'quote_asset_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'kucoin_spot_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'base_asset_rate': 'decimal(30, 16) NOT NULL',
                'quote_asset_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'gate_spot_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'base_asset_rate': 'decimal(30, 16) NOT NULL',
                'quote_asset_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'mexc_spot_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'base_asset_rate': 'decimal(30, 16) NOT NULL',
                'quote_asset_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'bitget_spot_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'base_asset_rate': 'decimal(30, 16) NOT NULL',
                'quote_asset_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'lbank_spot_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'base_asset_rate': 'decimal(30, 16) NOT NULL',
                'quote_asset_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'bitmart_spot_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'base_asset_rate': 'decimal(30, 16) NOT NULL',
                'quote_asset_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'bingx_spot_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'base_asset_rate': 'decimal(30, 16) NOT NULL',
                'quote_asset_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'coinw_spot_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'base_asset_rate': 'decimal(30, 16) NOT NULL',
                'quote_asset_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
        'xtcom_spot_depth': ExternalDB.TableConfig(
            {
                'id': 'int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY',
                'depth': 'decimal(30, 16) NOT NULL ',
                'market': 'varchar(32) NOT NULL',
                'base_asset_rate': 'decimal(30, 16) NOT NULL',
                'quote_asset_rate': 'decimal(30, 16) NOT NULL',
                'data': 'MEDIUMTEXT NOT NULL Comment "市场深度数据"',
                'snapshot_ts': 'int(10) NOT NULL Comment "快照时间戳，精确到分钟"',
            },
            (
                'KEY `market` (`market`)',
                'KEY `snapshot_ts` (`snapshot_ts`)',
            )
        ),
    }

    USER_SLICE_BALANCE_TABLE_COUNT = 200
    USER_SLICE_PERPETUAL_BALANCE_TABLE_COUNT = 50
    USER_SLICE_ACCOUNT_BALANCE_SUM_TABLE_COUNT = 200
    USER_SLICE_PROFIT_LOSS_SNAPSHOT_TABLE_COUNT = 200
    USER_SLICE_ACCOUNT_DEAL_VALUE_SUM_TABLE_COUNT = 200
    USER_SLICE_ASSET_PROFIT_LOSS_SNAPSHOT_TABLE_COUNT = 200

    @classmethod
    def user_slice_balance_hash(cls, user_id: int) -> int:
        return user_id % cls.USER_SLICE_BALANCE_TABLE_COUNT

    @classmethod
    def user_slice_perpetual_balance_hash(cls, user_id: int) -> int:
        return user_id % cls.USER_SLICE_PERPETUAL_BALANCE_TABLE_COUNT

    @classmethod
    def user_slice_account_balance_sum_hash(cls, user_id: int) -> int:
        return user_id % cls.USER_SLICE_ACCOUNT_BALANCE_SUM_TABLE_COUNT

    @classmethod
    def user_slice_profit_loss_snapshot_hash(cls, user_id: int) -> int:
        return user_id % cls.USER_SLICE_PROFIT_LOSS_SNAPSHOT_TABLE_COUNT
    
    @classmethod
    def user_slice_account_deal_value_sum_hash(cls, user_id: int) -> int:
        return user_id % cls.USER_SLICE_ACCOUNT_DEAL_VALUE_SUM_TABLE_COUNT

    @classmethod
    def user_slice_asset_profit_loss_hash(cls, user_id: int) -> int:
        return user_id % cls.USER_SLICE_ASSET_PROFIT_LOSS_SNAPSHOT_TABLE_COUNT

    @classmethod
    def user_balance_table(cls, timestamp: int):
        if not isinstance(timestamp, int):
            raise TypeError('`timestamp` must be an integer')
        timestamp = timestamp - timestamp % 3600
        return cls.table_from_config(
            f'user_balance_{timestamp}', 'user_balance')

    @classmethod
    def user_max_position_table(cls) -> ExternalTable:
        return cls.table_from_config(
            f'user_max_position', 'user_max_position')

    @classmethod
    def user_account_balance_table(cls, timestamp: int):
        if not isinstance(timestamp, int):
            raise TypeError('`timestamp` must be an integer')
        timestamp = timestamp - timestamp % (24 * 3600)
        return cls.table_from_config(
            f'user_account_balance_{timestamp}', 'user_account_balance')

    @classmethod
    def user_top_balance_rank_table(cls, timestamp: int):
        if not isinstance(timestamp, int):
            raise TypeError(f'`timestamp` must be an integer')
        timestamp = timestamp - timestamp % (24 * 3600)
        return cls.table_from_config(
            f'user_top_balance_rank_{timestamp}', 'user_top_balance_rank')

    @classmethod
    def user_account_balance_sum_table(cls, timestamp: int):
        if not isinstance(timestamp, int):
            raise TypeError('`timestamp` must be an integer')
        timestamp = timestamp - timestamp % (24 * 3600)
        return cls.table_from_config(
            f'user_account_balance_sum_{timestamp}', 'user_account_balance_sum')

    @classmethod
    def user_account_type_balance_sum_table(cls, timestamp: int):
        if not isinstance(timestamp, int):
            raise TypeError('`timestamp` must be an integer')
        timestamp = timestamp - timestamp % (24 * 3600)
        return cls.table_from_config(
            f'user_account_type_balance_sum_{timestamp}', 'user_account_type_balance_sum')

    @classmethod
    def user_balance_sum_table(cls, timestamp: int):
        if not isinstance(timestamp, int):
            raise TypeError('`timestamp` must be an integer')
        timestamp = timestamp - timestamp % 3600
        return cls.table_from_config(
            f'user_balance_sum_{timestamp}', 'user_balance_sum')

    @classmethod
    def user_slice_balance_table(cls, idx: int):
        return cls.table_from_config(
            f'user_slice_balance_{idx}', 'user_slice_balance')

    @classmethod
    def user_slice_balance_log_table(cls):
        return cls.table_from_config('user_slice_balance_log')

    @classmethod
    def user_margin_table(cls, timestamp: int):
        if not isinstance(timestamp, int):
            raise TypeError('`timestamp` must be an integer')
        timestamp = timestamp - timestamp % (24 * 3600)
        return cls.table_from_config(
            f'user_margin_{timestamp}', 'user_margin')

    @classmethod
    def user_amm_table(cls, timestamp: int):
        if not isinstance(timestamp, int):
            raise TypeError('`timestamp` must be an integer')
        timestamp = timestamp - timestamp % (24 * 3600)
        return cls.table_from_config(
            f'user_amm_{timestamp}', 'user_amm')

    @classmethod
    def user_pledge_table(cls, timestamp: int):
        if not isinstance(timestamp, int):
            raise TypeError('`timestamp` must be an integer')
        timestamp = timestamp - timestamp % (24 * 3600)
        return cls.table_from_config(
            f'user_pledge_{timestamp}', 'user_pledge')

    @classmethod
    def user_pre_trading_table(cls, timestamp: int):
        if not isinstance(timestamp, int):
            raise TypeError('`timestamp` must be an integer')
        timestamp = timestamp - timestamp % (24 * 3600)
        return cls.table_from_config(
            f'user_pre_trading_{timestamp}', 'user_pre_trading')

    @classmethod
    def user_slice_perpetual_balance_table(cls, idx: int):
        return cls.table_from_config(
            f'user_slice_perpetual_balance_{idx}', 'user_slice_perpetual_balance')

    @classmethod
    def user_slice_account_balance_sum_table(cls, idx: int):
        return cls.table_from_config(
            f'user_slice_account_balance_sum_{idx}', 'user_slice_account_balance_sum')

    @classmethod
    def user_slice_profit_loss_snapshot_table(cls, idx: int):
        return cls.table_from_config(
            f'user_slice_profit_loss_snapshot_{idx}', 'user_slice_profit_loss_snapshot')
    
    @classmethod
    def user_slice_account_deal_value_sum_table(cls, idx: int):
        return cls.table_from_config(
            f'user_slice_account_deal_value_sum_{idx}', 'user_slice_account_deal_value_sum'
        )

    @classmethod
    def user_slice_asset_profit_loss_snapshot_table(cls, idx: int):
        return cls.table_from_config(
            f'user_slice_asset_profit_loss_snapshot_{idx}', 'user_slice_asset_profit_loss_snapshot')

    @classmethod
    def spot_depth_snapshot_table(cls, timestamp: int):
        if not isinstance(timestamp, int):
            raise TypeError('`timestamp` must be an integer')
        timestamp = timestamp - timestamp % (24 * 3600)
        return cls.table_from_config(
            f'spot_depth_{timestamp}', 'spot_depth')

    @classmethod
    def perpetual_depth_snapshot_table(cls, timestamp: int):
        if not isinstance(timestamp, int):
            raise TypeError('`timestamp` must be an integer')
        timestamp = timestamp - timestamp % (24 * 3600)
        return cls.table_from_config(
            f'perpetual_depth_{timestamp}', 'perpetual_depth')

    @classmethod
    def spot_full_depth_snapshot_table(cls, timestamp: int):
        if not isinstance(timestamp, int):
            raise TypeError('`timestamp` must be an integer')
        timestamp = timestamp - timestamp % (24 * 3600)
        return cls.table_from_config(
            f'spot_full_depth_{timestamp}', 'spot_full_depth')

    @classmethod
    def perpetual_full_depth_snapshot_table(cls, timestamp: int):
        if not isinstance(timestamp, int):
            raise TypeError('`timestamp` must be an integer')
        timestamp = timestamp - timestamp % (24 * 3600)
        return cls.table_from_config(
            f'perpetual_full_depth_{timestamp}', 'perpetual_full_depth')

    @classmethod
    def third_exchange_perpetual_depth_snapshot_table(cls, timestamp: int, table: str):
        if not isinstance(timestamp, int):
            raise TypeError('`timestamp` must be an integer')
        timestamp = timestamp - timestamp % (24 * 3600)
        table = table.lower()
        return cls.table_from_config(
            f'{table}_perpetual_depth_{timestamp}', f'{table}_perpetual_depth')

    @classmethod
    def third_exchange_spot_depth_snapshot_table(cls, timestamp: int, table: str):
        if not isinstance(timestamp, int):
            raise TypeError('`timestamp` must be an integer')
        timestamp = timestamp - timestamp % (24 * 3600)
        table = table.lower()
        return cls.table_from_config(
            f'{table}_spot_depth_{timestamp}', f'{table}_spot_depth')

    @classmethod
    def check_table_is_finished_by_logtable(cls, report_date: date, table_name: str):
        report_date_str = report_date.strftime("%Y-%m-%d")
        _where = f"`report_date` = '{report_date_str}' and " \
                 f"`table` = '{table_name}' and `status` = 'finish'"
        log_table = ExchangeLogDB.user_slice_balance_log_table()
        return bool(log_table.select(where=_where))

    @classmethod
    def check_table_is_finished_by_count(cls, timestamp: int, table_name: str):
        timestamp = timestamp - timestamp % (24 * 3600)
        table = cls.table(f"{table_name}_{timestamp}")
        if not table.exists():
            return False
        last_count = table.count()
        if last_count == 0:
            return False
        time.sleep(5)
        after_count = table.count()
        if after_count != last_count:
            return False
        return True

    @classmethod
    def get_user_balances(cls, timestamp: int, asset: str,
                          user_id: int = None):
        table = cls.user_balance_table(timestamp)

        where = f"`asset` = '{asset}' AND `account` = 0"
        group_by = 'user_id'
        having = '`balance` > 0'
        if user_id:
            where = f"{where} and `user_id` = {user_id}"
            group_by = None
            having = None

        records = table.select(
            'user_id',
            'balance',
            where=where,
            group_by=group_by,
            having=having
        )
        return dict(records)

    @classmethod
    def get_user_account_type_balances(cls,
                                       timestamp: int, asset: str,
                                       account_types: Optional[List[str]],
                                       user_ids: Optional[List[int]] = None) -> Dict[int, Decimal]:
        table = cls.user_account_balance_table(timestamp)
        group_by = 'user_id'
        if not account_types:
            where = f"`asset` = '{asset}'"
        elif len(account_types) == 1:
            where = f"`asset` = '{asset}' AND `account_type` = '{account_types[0]}'"
        else:
            where = f"`asset` = '{asset}' AND `account_type` in {tuple(account_types)}"

        if user_ids:
            where = add_users_in_where(where, user_ids)
        records = table.select(
            'user_id',
            'SUM(balance) as total_balance',
            where=where,
            group_by=group_by,
        )
        return dict(records)

    @classmethod
    def sum_user_account_type_balances(cls,
                                       timestamp: int,
                                       account_types: Optional[List[str]],
                                       user_ids: Optional[List[int]] = None) -> Dict[int, Decimal]:
        table = cls.user_account_balance_table(timestamp)
        group_by = 'user_id'
        if not account_types:
            where = None
        elif len(account_types) == 1:
            where = f"`account_type` = '{account_types[0]}'"
        else:
            where = f"`account_type` in {tuple(account_types)}"

        if user_ids:
            where = add_users_in_where(where, user_ids)
        records = table.select(
            'user_id',
            'SUM(balance_usd) as total_balance_usd',
            where=where,
            group_by=group_by,
        )
        return dict(records)

    @classmethod
    def sum_user_balances(cls, timestamp: int, asset: str,
                          account: int = None, user_id: int = None):
        table = cls.user_balance_table(timestamp)

        where = f"`asset` = '{asset}'"
        if user_id is not None:
            where = f'{where} AND `user_id` = {user_id} '
        if account is not None:
            where = f'{where} AND `account` = {account}'
        return table.select(
            'balance',
            where=where
        )[0][0] or Decimal()

    @classmethod
    def sum_user_market_value(cls, timestamp: int, user_id: int):
        table = cls.user_balance_sum_table(timestamp)
        where = f'`user_id` = {user_id}'
        return table.select(
            'SUM(balance_usd) - SUM(margin_due_amount_usd)',
            where=where
        )[0][0] or Decimal()

    @classmethod
    def get_account_balances(cls, timestamp: int, account: int):
        table = cls.user_balance_table(timestamp)

        columns = ('user_id', 'asset', 'balance', 'locked_balance',
                   'balance_usd')
        records = table.select(
            *columns,
            where=f"`account` = {account} AND `asset` != 'ALL'"
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def daily_table_synced(cls, table: ExternalTable, check_count: int = True) -> bool:
        if not table.exists():
            return False
        if table.count() == 0:
            return False
        name, ts = table.name.rsplit("_", 1)
        ts = int(ts)
        yesterday_ts = ts - 86400
        yesterday_table = cls.table_from_config(
            f'{name}_{yesterday_ts}', name)
        if not yesterday_table.exists():
            return True
        current_count = table.count()
        yesterday_count = yesterday_table.count()
        if yesterday_count == 0:
            return True

        # 数据库条数变化超过40%则证明备份有问题
        if check_count and abs(current_count - yesterday_count) / yesterday_count > Decimal("0.4"):
            return False
        return True

    @classmethod
    def slice_table_synced(cls, table_name: str, log_date_str: str) -> bool:
        log_table = ExchangeLogDB.user_slice_balance_log_table()
        where = (f"`report_date` = '{log_date_str}' and "
                 f"`table` = '{table_name}' and `status` = 'finish'")
        if not log_table.select(where=where):
            return False
        return True

    @classmethod
    def get_slice_table_latest_date(cls, table_name: str) -> date:
        log_table = ExchangeLogDB.user_slice_balance_log_table()
        where = f"`table` = '{table_name}' and `status` = 'finish' order by report_date desc limit 1"
        result = log_table.select(
            "report_date",
            where=where
        )
        return result[0][0] if result else None

    @classmethod
    def insert_log(cls, table_name: str, log_date_str: str):
        log_table = cls.user_slice_balance_log_table()
        log_table.insert(log_date_str, table_name, 'create', '')
        log_table.flush()

    @classmethod
    def update_log_finished(cls, table_name: str, log_date_str: str):
        log_table = ExchangeLogDB.user_slice_balance_log_table()
        where = (f"`report_date` = '{log_date_str}' and "
                 f"`table` = '{table_name}'")
        log_table.update(where, status='finish')

    @classmethod
    def clean_slice_table(cls, table_name: str,
                          log_date_str: str,
                          snap_date_str: str, total_table_count: int):
        # delete log record
        log_table = cls.user_slice_balance_log_table()
        where = (f"`report_date` = '{log_date_str}' and "
                 f"`table` = '{table_name}'")
        log_table.delete(where=where)
        # delete slice data.
        for _idx in range(total_table_count):
            table = cls.table_from_config(f'{table_name}_{_idx}', f'{table_name}')
            delete_where = f"`report_date`='{snap_date_str}'"
            table.delete(where=delete_where)
            table.flush()

    @classmethod
    def sync_user_balances(cls, timestamp: int = None):
        if timestamp is None:
            timestamp = current_timestamp(to_int=True)
        table = cls.user_balance_table(timestamp)

        if cls.daily_table_synced(table):
            return
        # 重新生成
        if table.exists():
            table.drop()
        table.create_if_not_exists()

        balance_usd_sum = defaultdict(lambda: defaultdict(Decimal))
        margin_due_usd_sum = defaultdict(lambda: defaultdict(Decimal))

        from .margin.helper import MarginHelper
        margin_due_assets = MarginHelper.group_margin_loan_order()

        prices = PriceManager.assets_to_usd()
        quantize = partial(quantize_amount, decimals=16, precision=34)

        for (user_id,
             asset,
             account,
             balance,
             locked_balance) in TradeLogDB.get_slice_balances(timestamp):
            balance = quantize(balance)
            asset_to_usd = prices.get(asset) or Decimal()
            balance_usd = quantize(balance * asset_to_usd)
            margin_due_amount = margin_due_assets.pop(
                (account, user_id, asset), 0)
            margin_due_amount_usd = quantize(margin_due_amount * asset_to_usd)
            table.insert(
                user_id, account, asset, balance, locked_balance, balance_usd,
                margin_due_amount, margin_due_amount_usd)
            balance_usd_sum[user_id][account] += balance_usd
            margin_due_usd_sum[user_id][account] += margin_due_amount_usd

        for (account, user_id, asset), margin_due_amount in margin_due_assets.items():
            asset_to_usd = prices.get(asset, Decimal())
            margin_due_amount_usd = quantize(margin_due_amount * asset_to_usd)
            table.insert(user_id, account, asset, 0, 0, 0, margin_due_amount,
                         margin_due_amount_usd)
            margin_due_usd_sum[user_id][account] += margin_due_amount_usd

        table.flush()

        sum_table = cls.user_balance_sum_table(timestamp)
        if sum_table.exists():
            sum_table.drop()
        sum_table.create_if_not_exists()

        for user_id, accounts in balance_usd_sum.items():
            for account, balance_usd in accounts.items():
                margin_due_amount_usd = margin_due_usd_sum[user_id][account]
                sum_table.insert(
                    user_id, account, balance_usd, margin_due_amount_usd)
        sum_table.flush()
        return True

    @classmethod
    def sync_user_account_balances(cls, timestamp: int = None):
        from app.business.amm import get_all_user_amm_assets
        from app.business.pledge.helper import is_pledge_account

        if timestamp is None:
            timestamp = current_timestamp(to_int=True)
        table = cls.user_account_balance_table(timestamp)
        if cls.daily_table_synced(table):
            return
        if table.exists():
            table.drop()
        table.create_if_not_exists()
        """
        {
            user_id: {
                account_type: {
                    asset: Decimal('123')
                }
            }
        }
        """
        account_balance_sum = defaultdict(lambda: defaultdict(lambda: defaultdict(Decimal)))
        account_balance_usd_sum = defaultdict(lambda: defaultdict(lambda: defaultdict(Decimal)))
        prices = PriceManager.assets_to_usd()

        for (user_id,
             asset,
             account,
             balance) in TradeLogDB.get_user_balances(timestamp):
            asset_price = prices.get(asset, Decimal()) or Decimal()
            if account == SPOT_ACCOUNT_ID:
                account_balance_sum[user_id][AccountBalanceType.SPOT.name][asset] += balance
                account_balance_usd_sum[user_id][AccountBalanceType.SPOT.name][asset] += balance * asset_price
            elif account == AssetInvestmentConfig.ACCOUNT_ID:
                account_balance_sum[user_id][AccountBalanceType.INVESTMENT.name][asset] += balance
                account_balance_usd_sum[user_id][AccountBalanceType.INVESTMENT.name][asset] += balance * asset_price
            elif SPOT_ACCOUNT_ID < account <= MAX_ORDER_ACCOUNT_ID:
                account_balance_sum[user_id][AccountBalanceType.MARGIN.name][asset] += balance
                account_balance_usd_sum[user_id][AccountBalanceType.MARGIN.name][asset] += balance * asset_price
            elif is_pledge_account(account):
                account_balance_sum[user_id][AccountBalanceType.PLEDGE.name][asset] += balance
                account_balance_usd_sum[user_id][AccountBalanceType.PLEDGE.name][asset] += balance * asset_price
            elif account == StakingAccount.ACCOUNT_ID:
                account_balance_sum[user_id][AccountBalanceType.STAKING.name][asset] += balance
                account_balance_usd_sum[user_id][AccountBalanceType.STAKING.name][asset] += balance * asset_price

        for (user_id, asset, balance) in PerpetualLogDB.get_user_balances(timestamp):
            asset_price = prices.get(asset, Decimal()) or Decimal()
            account_balance_sum[user_id][AccountBalanceType.PERPETUAL.name][
                asset] += balance
            account_balance_usd_sum[user_id][AccountBalanceType.PERPETUAL.name][
                asset] += balance * asset_price

        for user_id, amm_asset_balances in get_all_user_amm_assets().items():
            for asset, balance in amm_asset_balances.items():
                asset_price = prices.get(asset, Decimal()) or Decimal()
                account_balance_sum[user_id][AccountBalanceType.AMM.name][asset] += balance
                account_balance_usd_sum[user_id][AccountBalanceType.AMM.name][asset] += balance * asset_price

        for user_id, detail in account_balance_sum.items():
            for account_type, assets_data in detail.items():
                for asset, balance in assets_data.items():
                    balance_usd = account_balance_usd_sum[user_id][account_type][asset]
                    table.insert(user_id, account_type, asset, balance, balance_usd)

        table.flush()

    @classmethod
    def sync_user_top_balance_rank(cls, timestamp: int = None):
        cls.drop_xdays_ago_table(table_func=cls.user_top_balance_rank_table, days=30)
        if timestamp is None:
            timestamp = current_timestamp(to_int=True)
        source_table = cls.user_account_balance_table(timestamp)
        if not source_table.exists():
            return
        table = cls.user_top_balance_rank_table(timestamp)
        if cls.daily_table_synced(table):
            return
        if table.exists():
            table.drop()
        table.create_if_not_exists()

        def iter_rows():
            limit = 10000
            last = 0
            while True:
                rows = source_table.select("id,user_id,account_type,asset,balance,balance_usd",
                                           where=f"id>{last}", order_by="id", limit=limit)
                yield from rows
                if len(rows) < limit:
                    break
                last = rows[-1][0]
        # 按账户-币种维度，账户维度，币种维度，以及总资产维度统计
        account_asset_balances = defaultdict(list)
        account_balances = defaultdict(Decimal)
        asset_balances = defaultdict(lambda : [0, 0])
        total_balances = defaultdict(Decimal)
        for _,user_id,account_type,asset,balance,balance_usd in iter_rows():
            if user_id <= 0:
                continue
            account_asset_balances[(account_type, asset)].append((user_id, account_type, asset, balance, balance_usd))
            account_balances[(account_type, user_id)] += balance_usd
            asset_balances[(asset, user_id)][0] += balance
            asset_balances[(asset, user_id)][1] += balance_usd
            total_balances[user_id] += balance_usd

        account_balances2 = defaultdict(list)
        for (account_type, user_id), balance_usd in account_balances.items():
            account_balances2[account_type].append((user_id, account_type, 'ALL', balance_usd, balance_usd))
        account_balances = account_balances2

        asset_balances2 = defaultdict(list)
        for (asset, user_id), (balance, balance_usd) in asset_balances.items():
            asset_balances2[asset].append((user_id, 'ALL', asset, balance, balance_usd))
        asset_balances = asset_balances2
        
        total_balances2 = []
        for user_id, balance_usd in total_balances.items():
            total_balances2.append((user_id, 'ALL', 'ALL', balance_usd, balance_usd))
        total_balances = total_balances2

        def iter_top(n):
            for (account_type, asset), rows in account_asset_balances.items():
                rows.sort(key=lambda x: x[3], reverse=True)
                yield from rows[:n]
                # user_id=0的是该维度下的汇总数据
                yield (0, account_type, asset, sum(x[3] for x in rows), sum(x[4] for x in rows))

            for account_type, rows in account_balances.items():
                rows.sort(key=lambda x: x[3], reverse=True)
                yield from rows[:n]
                yield (0, account_type, 'ALL', sum(x[3] for x in rows), sum(x[4] for x in rows))

            for asset, rows in asset_balances.items():
                rows.sort(key=lambda x: x[3], reverse=True)
                yield from rows[:n]
                yield (0, 'ALL', asset, sum(x[3] for x in rows), sum(x[4] for x in rows))

            total_balances.sort(key=lambda x: x[3], reverse=True)
            yield from total_balances[:n]
            yield (0, 'ALL', 'ALL', sum(x[3] for x in total_balances), sum(x[4] for x in total_balances))

        for row in iter_top(1000):
            table.insert(*row)
        table.flush()

    @classmethod
    def sync_user_margin_balances(cls, timestamp: int):
        from app.models import MarginAccount
        from app.business.margin.helper import MarginHelper
        cls.drop_xdays_ago_table(table_func=cls.user_margin_table)
        table = cls.user_margin_table(timestamp)
        if cls.daily_table_synced(table, False):
            return

        if table.exists():
            table.drop()
        table.create_if_not_exists()

        margin_due_assets = MarginHelper.group_margin_loan_order_with_interest()
        last_dt = timestamp_to_datetime(timestamp - 86400)
        prices = AssetPrice.get_close_price_map(last_dt)
        id_name_mapping = {v.id: v.name for v in
            MarginAccount.query.with_entities(MarginAccount.name, MarginAccount.id).all()}
        for key, value in margin_due_assets.items():
            account_id, user_id, asset = key
            total_amount, interest_amount = value["amount"], value["interest_amount"]
            asset_to_usd = prices.get(asset, Decimal())
            total_usd = quantize_amount(total_amount * asset_to_usd, PrecisionEnum.CASH_PLACES)
            interest_usd = quantize_amount(interest_amount * asset_to_usd, PrecisionEnum.CASH_PLACES)
            market = id_name_mapping.get(account_id, '')
            # user_id,account,market,asset,loan_amount,interest_amount,loan_amount_usd,interest_amount_usd
            table.insert(user_id, account_id, market, asset, total_amount, interest_amount, total_usd,
                         interest_usd)
        table.flush()

    @classmethod
    def sync_user_amm_balances(cls, timestamp: int):
        from app.business.amm import get_all_user_amm_assets
        cls.drop_xdays_ago_table(table_func=cls.user_amm_table)
        table = cls.user_amm_table(timestamp)
        if cls.daily_table_synced(table, False):
            return

        if table.exists():
            table.drop()
        table.create_if_not_exists()

        def get_user_amm_assets():
            retry_time = 5
            for i in range(retry_time):
                try:
                    return get_all_user_amm_assets()
                except Exception:
                    pass
            raise RuntimeError("cannot get amm assets")

        amm_data = get_user_amm_assets()
        last_dt = timestamp_to_datetime(timestamp - 86400)
        prices = AssetPrice.get_close_price_map(last_dt)
        for user_id, amm_asset_balances in amm_data.items():
            for asset, balance in amm_asset_balances.items():
                asset_to_usd = prices.get(asset, Decimal()) or Decimal()
                balance_usd = quantize_amount(balance * asset_to_usd, PrecisionEnum.CASH_PLACES)
                # user_id,asset,amount,usd
                table.insert(
                    user_id, asset, balance, balance_usd
                )
        table.flush()

    @classmethod
    def sync_user_pledge_balances(cls, timestamp: int):
        """ 质押账户未还快照 """
        from app.business.pledge.helper import group_pledge_unflat_debt_interest

        cls.drop_xdays_ago_table(table_func=cls.user_pledge_table)
        table = cls.user_pledge_table(timestamp)
        if cls.daily_table_synced(table, False):
            return

        if table.exists():
            table.drop()
        table.create_if_not_exists()

        last_dt = timestamp_to_datetime(timestamp - 86400)
        prices = AssetPrice.get_close_price_map(last_dt)
        unflat_debt_interests = group_pledge_unflat_debt_interest()
        for key, value in unflat_debt_interests.items():
            user_id, account_id, loan_asset = key
            debt_amount, interest_amount = value
            total_amount = debt_amount + interest_amount
            asset_to_usd = prices.get(loan_asset, Decimal())
            total_usd = quantize_amount(total_amount * asset_to_usd, PrecisionEnum.CASH_PLACES)
            interest_usd = quantize_amount(interest_amount * asset_to_usd, PrecisionEnum.CASH_PLACES)
            table.insert(user_id, account_id, loan_asset, total_amount, interest_amount, total_usd, interest_usd)
        table.flush()

    @classmethod
    def sync_user_pre_trading_balances(cls, timestamp: int):
        """ 预测市场的发行token和质押资产 快照 """
        from app.business.pre_trading import group_user_issue_and_pledge_amounts

        cls.drop_xdays_ago_table(table_func=cls.user_pre_trading_table)
        table = cls.user_pre_trading_table(timestamp)
        if cls.daily_table_synced(table, False):
            return

        if table.exists():
            table.drop()
        table.create_if_not_exists()

        last_dt = timestamp_to_datetime(timestamp - 86400)
        prices = AssetPrice.get_close_price_map(last_dt)
        user_issue_data_map = group_user_issue_and_pledge_amounts()
        for key, value in user_issue_data_map.items():
            user_id, issue_asset = key
            issue_amount, pledge_asset, pledge_amount = value
            issue_usd = quantize_amount(prices.get(issue_asset, Decimal()) * issue_amount, PrecisionEnum.CASH_PLACES)
            pledge_usd = quantize_amount(prices.get(pledge_asset, Decimal()) * pledge_amount, PrecisionEnum.CASH_PLACES)
            table.insert(user_id, issue_asset, issue_amount, pledge_asset, pledge_amount, issue_usd, pledge_usd)
        table.flush()

    @classmethod
    def sync_user_account_balance_sum(cls, timestamp: int):

        cls.drop_xdays_ago_table(table_func=cls.user_account_balance_sum_table)
        to_table = cls.user_account_balance_sum_table(timestamp)

        if cls.daily_table_synced(to_table):
            return

        if to_table.exists():
            to_table.drop()
        to_table.create_if_not_exists()
        from_table = cls.user_account_balance_table(timestamp)
        rows = from_table.select(
            *['user_id', 'SUM(balance_usd) total_usd'],
            group_by="user_id",
        )  # 未减去借币对应的 usd
        for user_id, balance_usd in rows:
            balance_usd = quantize_amount(balance_usd, PrecisionEnum.CASH_PLACES)
            if balance_usd <= Decimal('0.1'):
                continue
            to_table.insert(user_id, balance_usd)
        to_table.flush()

    @classmethod
    def drop_xdays_ago_table(cls, table_func, days=30):
        # todo: 因其他问题导致未正确删除表情况处理
        _xdays_ago_timestamp = today_timestamp_utc() - 60 * 60 * 24 * days
        _xdays_ago_table = table_func(_xdays_ago_timestamp)
        if _xdays_ago_table.exists():
            _xdays_ago_table.drop()
            return True
        return False

    @classmethod
    def init_user_slice_balance_tables(cls, *, truncate: bool = False):
        for idx in range(cls.USER_SLICE_BALANCE_TABLE_COUNT):
            cls.user_slice_balance_table(idx).create_if_not_exists(
                truncate=truncate)

    @classmethod
    def init_user_slice_perpetual_balance_tables(cls, *, truncate: bool = False):
        for idx in range(cls.USER_SLICE_PERPETUAL_BALANCE_TABLE_COUNT):
            cls.user_slice_perpetual_balance_table(idx).create_if_not_exists(
                truncate=truncate)

    @classmethod
    def init_user_slice_account_balance_sum_tables(cls, *, truncate: bool = False):
        for idx in range(cls.USER_SLICE_ACCOUNT_BALANCE_SUM_TABLE_COUNT):
            cls.user_slice_account_balance_sum_table(idx).create_if_not_exists(
                truncate=truncate)

    @classmethod
    def init_user_slice_profit_loss_snapshot_tables(cls, *, truncate: bool = False):
        for idx in range(cls.USER_SLICE_PROFIT_LOSS_SNAPSHOT_TABLE_COUNT):
            cls.user_slice_profit_loss_snapshot_table(idx).create_if_not_exists(
                truncate=truncate)
    
    @classmethod
    def init_user_slice_account_deal_value_sum_tables(cls, *, truncate: bool = False):
        for idx in range(cls.USER_SLICE_ACCOUNT_DEAL_VALUE_SUM_TABLE_COUNT):
            cls.user_slice_account_deal_value_sum_table(idx).create_if_not_exists(
                truncate=truncate)
    
    @classmethod
    def init_user_slice_asset_profit_loss_snapshot_tables(cls, *, truncate: bool = False):
        for idx in range(cls.USER_SLICE_ASSET_PROFIT_LOSS_SNAPSHOT_TABLE_COUNT):
            cls.user_slice_asset_profit_loss_snapshot_table(idx).create_if_not_exists(
                truncate=truncate)

    @classmethod
    def sync_user_slice_balances(cls, timestamp: int = None):
        if timestamp is None:
            timestamp = current_timestamp(to_int=True)
        timestamp = timestamp - timestamp % 86400
        date_str = timestamp_to_datetime(timestamp).strftime('%Y-%m-%d')

        balance_table = TradeLogDB.slice_balance_table(timestamp)
        if balance_table is None:
            raise RuntimeError(
                f'spot slice balance table at {timestamp} is not created yet, '
                f'please wait a moment')

        table_name = 'user_slice_balance'
        if cls.slice_table_synced(table_name, date_str):
            return
        cls.clean_slice_table(table_name, date_str, date_str,
                              cls.USER_SLICE_ACCOUNT_BALANCE_SUM_TABLE_COUNT)
        cls.insert_log(table_name, date_str)

        tables: Dict[int, cls.Table] \
            = DefaultDictWithArg(cls.user_slice_balance_table)
        user_id_to_idx = cls.user_slice_balance_hash

        balance_usd_sum = defaultdict(lambda: defaultdict(Decimal))
        margin_usd_sum = defaultdict(lambda: defaultdict(Decimal))

        prices = PriceManager.assets_to_usd()
        quantize = partial(quantize_amount, decimals=16, precision=34)

        from .margin.helper import MarginHelper
        margin_due_assets = MarginHelper.group_margin_loan_order()

        for (user_id,
             asset,
             account,
             balance,
             locked_balance) in TradeLogDB.get_slice_balances(timestamp):
            asset_to_usd = prices.get(asset, Decimal()) or Decimal()
            balance_usd = quantize(balance * asset_to_usd)
            margin_amount = margin_due_assets.pop(
                (account, user_id, asset), 0)
            margin_amount_usd = quantize(margin_amount * asset_to_usd)
            table = tables[user_id_to_idx(user_id)]
            table.insert(
                date_str, user_id, account, asset, balance, locked_balance,
                balance_usd, margin_amount, margin_amount_usd)
            balance_usd_sum[user_id][account] += balance_usd
            margin_usd_sum[user_id][account] += margin_amount_usd

        for (account, user_id, asset), margin_amount in margin_due_assets.items():
            asset_to_usd = prices.get(asset, Decimal()) or Decimal()
            margin_amount_usd = quantize(margin_amount * asset_to_usd)
            table = tables[user_id_to_idx(user_id)]
            table.insert(date_str, user_id, account, asset, 0, 0, 0,
                         margin_amount, margin_amount_usd)
            margin_usd_sum[user_id][account] += margin_amount_usd

        for table in tables.values():
            table.flush()

        cls.update_log_finished(table_name, date_str)

    @classmethod
    def get_user_balance_ratios(cls, asset: str, start: int, end: int,
                                min_balance: Decimal = Decimal(),
                                *, exclude_credit: bool = False
                                ) -> Dict[int, Decimal]:
        zeitpunkt = datetime(2020, 8, 26).timestamp()
        start = start - start % 86400
        balances = defaultdict(Decimal)

        while start < end:
            table = cls.user_balance_table(
                start if start >= zeitpunkt else start - 3600 * 8)
            if not table.exists():
                _logger.warning(f'table {table.name!r} does not exist')
            else:
                credit_balances = {}
                if exclude_credit:
                    for user_id, credit_id in CreditAssetHistory.query \
                            .filter(CreditAssetHistory.asset == asset,
                                    CreditAssetHistory.created_at
                                    < timestamp_to_datetime(start)) \
                            .with_entities(CreditAssetHistory.user_id,
                                           func.max(CreditAssetHistory.id)) \
                            .group_by(CreditAssetHistory.user_id):
                        credit_balances[user_id] \
                            = (CreditAssetHistory.query.get(credit_id)
                               .unflat_amount)
                for user_id, balance in table.select(
                        'user_id', 'balance',
                        where=f"`asset` = '{asset}'"
                              f" AND `balance` > '{min_balance}'"
                ):
                    balances[user_id] += (Decimal(balance)
                                          - credit_balances.get(user_id, 0))
            start += 86400

        balance_sum = sum(balances.values())
        if balance_sum <= 0:
            return {}

        return {user_id: balance / balance_sum
                for user_id, balance in balances.items()}

    @classmethod
    def sync_user_slice_perpetual_balances(cls, timestamp: int = None):
        if timestamp is None:
            timestamp = current_timestamp(to_int=True)
        timestamp = timestamp - timestamp % 86400
        date_str = timestamp_to_datetime(timestamp).strftime('%Y-%m-%d')
        balance_table = PerpetualLogDB.slice_balance_table(timestamp)
        if balance_table is None:
            raise RuntimeError(
                f'perpetual slice balance table at {timestamp} is not created yet, '
                f'please wait a moment')
        table_name = 'user_slice_perpetual_balance'
        if cls.slice_table_synced(table_name, date_str):
            return
        cls.clean_slice_table(table_name, date_str, date_str,
                              cls.USER_SLICE_PERPETUAL_BALANCE_TABLE_COUNT)
        cls.insert_log(table_name, date_str)

        tables: Dict[int, cls.Table] \
            = DefaultDictWithArg(cls.user_slice_perpetual_balance_table)
        user_id_to_idx = cls.user_slice_perpetual_balance_hash

        prices = PriceManager.assets_to_usd()
        quantize = partial(quantize_amount, decimals=16, precision=34)

        for (user_id, asset, balance) in PerpetualLogDB.get_user_balances(timestamp):
            asset_to_usd = prices.get(asset, Decimal()) or Decimal()
            balance_usd = quantize(balance * asset_to_usd)
            table = tables[user_id_to_idx(user_id)]
            table.insert(date_str, user_id, asset, balance, balance_usd)

        for table in tables.values():
            table.flush()

        cls.update_log_finished(table_name, date_str)

    @classmethod
    def sync_user_account_type_balance_sum_table(cls, timestamp: int = None):
        import time
        from flask import current_app
        from app.business.amm import get_all_user_amm_assets
        from app.business.pledge.helper import is_pledge_account, group_pledge_total_unflat
        from .margin.helper import MarginHelper

        cls.drop_xdays_ago_table(table_func=cls.user_account_type_balance_sum_table, days=60)
        table = cls.user_account_type_balance_sum_table(timestamp)
        task_start = time.time()
        # TODO: 前一天快照数据生成之后这里逻辑需变更回来
        if table.exists():
            return
        #
        # if cls.daily_table_synced(table, False):
        #     return
        table.create_if_not_exists()

        if timestamp is None:
            timestamp = current_timestamp(to_int=True)
        timestamp = timestamp - timestamp % 86400
        last_dt = timestamp_to_datetime(timestamp - 86400)
        prices = AssetPrice.get_close_price_map(last_dt)
        quantize = partial(quantize_amount, decimals=16, precision=34)

        def get_user_amm_assets():
            retry_time = 5
            for i in range(retry_time):
                try:
                    return get_all_user_amm_assets()
                except Exception:
                    pass
            raise RuntimeError("cannot get amm assets")

        amm_data = get_user_amm_assets()

        margin_due_assets = MarginHelper.group_margin_loan_order()
        margin_unflat_amount_dict = defaultdict(lambda: defaultdict(Decimal))
        for key, amount in margin_due_assets.items():
            account, user_id, asset = key
            margin_unflat_amount_dict[(account, user_id)][asset] += amount

        pledge_unflat_data = group_pledge_total_unflat()
        pledge_unflat_amount_dict = defaultdict(lambda: defaultdict(Decimal))
        for key, amount in pledge_unflat_data.items():
            user_id, account, asset = key
            pledge_unflat_amount_dict[(account, user_id)][asset] += amount

        account_balance_sum = defaultdict(lambda: defaultdict(Decimal))
        for (user_id,
             asset,
             account,
             balance) in TradeLogDB.get_user_balances(timestamp):
            asset_to_usd = prices.get(asset, Decimal()) or Decimal()
            balance_usd = quantize(balance * asset_to_usd)
            if account == SPOT_ACCOUNT_ID:
                account_balance_sum[user_id][AccountBalanceType.SPOT.name] += balance_usd
            elif account == AssetInvestmentConfig.ACCOUNT_ID:
                account_balance_sum[user_id][AccountBalanceType.INVESTMENT.name] += balance_usd
            elif account == StakingAccount.ACCOUNT_ID:
                account_balance_sum[user_id][AccountBalanceType.STAKING.name] += balance_usd
            elif SPOT_ACCOUNT_ID < account <= MAX_ORDER_ACCOUNT_ID:
                account_balance_sum[user_id][AccountBalanceType.MARGIN.name] += balance_usd
                user_margin_result = margin_unflat_amount_dict.pop((account, user_id),
                                                                   defaultdict(Decimal))
                for _margin_asset, _unflat_amount in user_margin_result.items():
                    _asset_price = prices.get(_margin_asset, Decimal()) or Decimal()
                    _unflat_usd = quantize(_unflat_amount * _asset_price)
                    account_balance_sum[user_id][AccountBalanceType.MARGIN.name] -= _unflat_usd
            elif is_pledge_account(account):
                account_balance_sum[user_id][AccountBalanceType.PLEDGE.name] += balance_usd
                user_pledge_unflat_result = pledge_unflat_amount_dict.pop((account, user_id), defaultdict(Decimal))
                for _p_unflat_asset, _p_unflat_amount in user_pledge_unflat_result.items():
                    _asset_price = prices.get(_p_unflat_asset, Decimal()) or Decimal()
                    _unflat_usd = quantize(_p_unflat_amount * _asset_price)
                    account_balance_sum[user_id][AccountBalanceType.PLEDGE.name] -= _unflat_usd

        for (user_id, asset, balance) in PerpetualLogDB.get_user_balances(timestamp):
            asset_to_usd = prices.get(asset, Decimal()) or Decimal()
            balance_usd = quantize(balance * asset_to_usd)
            account_balance_sum[user_id][AccountBalanceType.PERPETUAL.name] += balance_usd

        for user_id, amm_asset_balances in amm_data.items():
            for asset, balance in amm_asset_balances.items():
                asset_to_usd = prices.get(asset, Decimal()) or Decimal()
                balance_usd = quantize(balance * asset_to_usd)
                account_balance_sum[user_id][AccountBalanceType.AMM.name] += balance_usd

        for user_id, accounts in account_balance_sum.items():
            for account, balance_usd in accounts.items():
                balance_usd = quantize_amount(balance_usd, PrecisionEnum.CASH_PLACES)
                if balance_usd <= Decimal('0.1'):
                    continue
                table.insert(user_id, account, balance_usd)
        table.flush()
        task_end = time.time()
        current_app.logger.info(f"sync_user_account_type_balance_sum_table cost {task_end-task_start}s")

    @classmethod
    def sync_user_slice_account_balance_sum(cls, timestamp: int = None):
        from app.business.amm import get_all_user_amm_assets
        from app.business.pledge.helper import is_pledge_account, group_pledge_total_unflat

        if timestamp is None:
            timestamp = current_timestamp(to_int=True)
        timestamp = timestamp - timestamp % 86400
        date_str = timestamp_to_datetime(timestamp).strftime('%Y-%m-%d')

        table_name = 'user_slice_account_balance_sum'
        if cls.slice_table_synced(table_name, date_str):
            return
        cls.clean_slice_table(table_name, date_str, date_str,
                              cls.USER_SLICE_ACCOUNT_BALANCE_SUM_TABLE_COUNT)
        cls.insert_log(table_name, date_str)

        tables: Dict[int, cls.Table] \
            = DefaultDictWithArg(cls.user_slice_account_balance_sum_table)

        user_id_to_idx = cls.user_slice_account_balance_sum_hash
        last_dt = timestamp_to_datetime(timestamp - 86400)
        prices = AssetPrice.get_close_price_map(last_dt)
        quantize = partial(quantize_amount, decimals=16, precision=34)

        from .margin.helper import MarginHelper
        margin_due_assets = MarginHelper.group_margin_loan_order()
        margin_unflat_amount_dict = defaultdict(lambda: defaultdict(Decimal))
        for key, amount in margin_due_assets.items():
            account, user_id, asset = key
            margin_unflat_amount_dict[(account, user_id)][asset] += amount

        pledge_unflat_data = group_pledge_total_unflat()
        pledge_unflat_amount_dict = defaultdict(lambda: defaultdict(Decimal))
        for key, amount in pledge_unflat_data.items():
            user_id, account, asset = key
            pledge_unflat_amount_dict[(account, user_id)][asset] += amount

        account_balance_sum = defaultdict(lambda: defaultdict(Decimal))
        for (user_id,
             asset,
             account,
             balance) in TradeLogDB.get_user_balances(timestamp):
            asset_to_usd = prices.get(asset, Decimal()) or Decimal()
            balance_usd = quantize(balance * asset_to_usd)
            if account == SPOT_ACCOUNT_ID:
                account_balance_sum[user_id][AccountBalanceType.SPOT.name] += balance_usd
            elif account == AssetInvestmentConfig.ACCOUNT_ID:
                account_balance_sum[user_id][AccountBalanceType.INVESTMENT.name] += balance_usd
            elif SPOT_ACCOUNT_ID < account <= MAX_ORDER_ACCOUNT_ID:
                account_balance_sum[user_id][AccountBalanceType.MARGIN.name] += balance_usd
                user_margin_result = margin_unflat_amount_dict.pop((account, user_id),
                                                                   defaultdict(Decimal))
                for _margin_asset, _unflat_amount in user_margin_result.items():
                    _asset_price = prices.get(_margin_asset, Decimal()) or Decimal()
                    _unflat_usd = quantize(_unflat_amount * _asset_price)
                    account_balance_sum[user_id][AccountBalanceType.MARGIN.name] -= _unflat_usd
            elif is_pledge_account(account):
                account_balance_sum[user_id][AccountBalanceType.PLEDGE.name] += balance_usd
                user_pledge_unflat_result = pledge_unflat_amount_dict.pop((account, user_id), defaultdict(Decimal))
                for _p_unflat_asset, _p_unflat_amount in user_pledge_unflat_result.items():
                    _asset_price = prices.get(_p_unflat_asset, Decimal()) or Decimal()
                    _unflat_usd = quantize(_p_unflat_amount * _asset_price)
                    account_balance_sum[user_id][AccountBalanceType.PLEDGE.name] -= _unflat_usd
            elif account == StakingAccount.ACCOUNT_ID:
                account_balance_sum[user_id][AccountBalanceType.STAKING.name] += balance_usd

        for (user_id, asset, balance) in PerpetualLogDB.get_user_balances(timestamp):
            asset_to_usd = prices.get(asset, Decimal()) or Decimal()
            balance_usd = quantize(balance * asset_to_usd)
            account_balance_sum[user_id][AccountBalanceType.PERPETUAL.name] += balance_usd
        retry_time = 5
        for i in range(retry_time):
            try:
                amm_data = get_all_user_amm_assets()
            except Exception:
                if i < retry_time - 1:
                    time.sleep(1)
                    i += 1
                    continue
                raise
            break

        for user_id, amm_asset_balances in amm_data.items():
            for asset, balance in amm_asset_balances.items():
                asset_to_usd = prices.get(asset, Decimal()) or Decimal()
                balance_usd = quantize(balance * asset_to_usd)
                account_balance_sum[user_id][AccountBalanceType.AMM.name] += balance_usd

        for user_id, accounts in account_balance_sum.items():
            for account, balance_usd in accounts.items():
                balance_usd = quantize_amount(balance_usd, PrecisionEnum.CASH_PLACES)
                if balance_usd <= Decimal('0.1'):
                    continue
                table = tables[user_id_to_idx(user_id)]
                table.insert(date_str, user_id, account, balance_usd)

        for table in tables.values():
            table.flush()
        cls.update_log_finished(table_name, date_str)

    @classmethod
    def get_user_max_balance_table_data(cls) -> dict:
        table = cls.user_max_position_table()
        _id_cursor = None
        table_data = defaultdict(Decimal)
        limit = 50000
        while True:
            if _id_cursor:
                table_result = table.select(
                    '`id`', '`user_id`', '`max_usd`',
                    where=f'id < {_id_cursor}',
                    order_by='`id` desc',
                    limit=limit
                )
            else:
                table_result = table.select(
                    '`id`', '`user_id`', '`max_usd`',
                    order_by='`id` desc',
                    limit=limit
                )
            for _d in table_result:
                table_data[_d[1]] = Decimal(_d[2])
            if len(table_result) != limit:
                break
            else:
                _id_cursor = table_result[-1][0]
        return table_data


def add_users_in_where(where: str, user_ids: List[int]):
    if len(user_ids) == 0:
        raise InvalidArgument(message='users为空')
    if len(user_ids) == 1:
        where = f'{where} and `user_id` = {user_ids[0]}'
    else:
        where = f'{where} and `user_id` in {tuple(user_ids)}'
    return where


class TradeHistoryDB0(ExternalDB):

    config = 'TRADE_HISTORY_0_MYSQL'


class TradeHistoryDB1(ExternalDB):

    config = 'TRADE_HISTORY_1_MYSQL'


class TradeHistoryDB2(ExternalDB):

    config = 'TRADE_HISTORY_2_MYSQL'


class TradeHistoryDB3(ExternalDB):

    config = 'TRADE_HISTORY_3_MYSQL'


class TradeHistoryDB4(ExternalDB):

    config = 'TRADE_HISTORY_4_MYSQL'


class TradeHistoryDB:

    """
    balance_history_{idx}
    order_history_{idx}
    stop_history_{idx}
    user_deal_history_{idx}
    """

    DBS = (
        TradeHistoryDB0,
        TradeHistoryDB1,
        TradeHistoryDB2,
        TradeHistoryDB3,
        TradeHistoryDB4
    )
    DB_COUNT = len(DBS)
    TABLE_COUNT = 100  # in each table

    @classmethod
    def user_to_db_and_table(cls, user_id: int, table_name: str
                             ) -> Tuple[Type[ExternalDB], str]:
        db_idx, table_idx = cls.user_hash(user_id)
        return cls.DBS[db_idx], f'{table_name}_{table_idx}'

    @classmethod
    def users_to_dbs_and_tables(cls, user_ids: Iterable[int], table_name: str
                                ) -> List[Tuple[Type[ExternalDB],
                                                Dict[str, List[int]]]]:
        mapping = defaultdict(lambda: defaultdict(list))
        user_hash = cls.user_hash
        for user_id in user_ids:
            db, table = user_hash(user_id)
            mapping[db][table].append(user_id)

        result = []
        dbs = cls.DBS
        for db_idx, tables in mapping.items():
            result.append((
                dbs[db_idx], {f'{table_name}_{table_idx}': user_ids
                              for table_idx, user_ids in tables.items()}
            ))
        return result

    @classmethod
    def user_hash(cls, user_id: int) -> Tuple[int, int]:
        # 0 -> (0, 0), 1 -> (0, 1), ..., 99 -> (0, 99), 100 -> (1, 0), ...,
        # 499 -> (4, 99), 500 -> (0, 0), ...
        table_count = cls.TABLE_COUNT
        offset = user_id % (cls.DB_COUNT * table_count)
        return divmod(offset, table_count)

    @classmethod
    def iter_db_and_table(cls, table_name: str) -> Generator[Tuple[Type[ExternalDB], str], None, None]:
        for db in cls.DBS:
            for table_idx in range(cls.TABLE_COUNT):
                yield db, f'{table_name}_{table_idx}'

    @classmethod
    def get_users_history(cls, user_ids: List[int], fields: List[str], cond: str, table_type: str):
        tables = defaultdict(list)
        for user_id in user_ids:
            _db, _table = cls.user_to_db_and_table(
                user_id, table_type)
            tables[(_db, _table)].append(user_id)
        result = []
        for k, v in tables.items():
            _db, _table = k
            user_id_str = ','.join(map(str, v))
            if cond:
                where = f' {cond} AND user_id in ({user_id_str})'
            else:
                where = f' user_id in ({user_id_str})'
            records = _db.table(_table).select(
                *fields,
                where=where,
                order_by='order_id DESC',
            )
            result.extend(list(dict(zip(fields, item)) for item in records))
        return result

    @classmethod
    def get_order_history_records(cls, db_, table_, start_id) -> List:
        table = cls.DBS[db_].table(f'order_history_{table_}')
        columns = ['id', 'market', 'side', 'user_id']
        where = f'id > {start_id}'
        records = table.select(
            *columns,
            where=where,
            order_by='id DESC',
        )
        return records or []

    @classmethod
    def get_balance_history_users(cls, user_ids: Iterable[int], start: int, end: int):
        tables = defaultdict(list)
        for user_id in user_ids:
            _db, _table = cls.user_to_db_and_table(
                user_id, 'balance_history')
            tables[(_db, _table)].append(user_id)
        result = set()
        fields = ['DISTINCT(`user_id`)']
        for k, v in tables.items():
            _db, _table = k
            user_id_str = ','.join(map(str, v))
            where = f'user_id in ({user_id_str}) '
            where += f' AND time >= {start} AND time < {end}'
            records = _db.table(_table).select(
                *fields,
                where=where,
            )
            result |= {i[0] for i in records}
        return result


class TradeLogDB(ExternalDB):

    """
    slice_history
    slice_balance_{timestamp} (
        t(1: available, 2: frozen, 3: lock)
    ) # 现货每30分钟快照，合约每10分钟快照。只保留最近7天的快照表
    backup_balance_{timestamp}
    indexlog_{timestamp}
    operlog_{timestamp}
    slice_order_{timestamp}  # 现货每30分钟快照, 合约每10分钟快照
    slice_stop_{timestamp}
    slice_update_{timestamp}
    """

    config = 'TRADE_LOG_MYSQL'
    SLICE_INTERVAL = 30 * 60

    @classmethod
    def get_slice_history_timestamp(cls, timestamp: int = None,
                                    interval: int = 3600,
                                    ) -> Optional[int]:
        """每小时快照"""
        table = cls.table('slice_history')
        if timestamp is not None:
            timestamp -= timestamp % interval
            records = table.select(
                'time',
                where=f'`time` >= {timestamp - 30} '
                      f'AND `time` <= {timestamp + 30}'
            )
        else:
            records = table.select(
                'time',
                order_by='`id` desc',
                limit=1
            )
        return records[0][0] if records else None

    @classmethod
    def slice_balance_table(cls, timestamp: int = None,
                            interval: int = 3600,
                            ) -> Optional[ExternalDB.Table]:
        timestamp = cls.get_slice_history_timestamp(timestamp, interval)
        if timestamp is None:
            return None
        return cls.table(f'slice_balance_{timestamp}')

    @classmethod
    def slice_order_table(cls, timestamp: int = None,
                          interval: int = 3600,
                          ) -> Optional[ExternalDB.Table]:
        timestamp = cls.get_slice_history_timestamp(timestamp, interval)
        if timestamp is None:
            return None
        return cls.table(f'slice_order_{timestamp}')

    @classmethod
    def slice_stop_order_table(cls, timestamp: int = None
                            ) -> Optional[ExternalDB.Table]:
        timestamp = cls.get_slice_history_timestamp(timestamp)
        if timestamp is None:
            return None
        return cls.table(f'slice_stop_{timestamp}')

    @classmethod
    def get_slice_balances(cls, timestamp: int = None
                           ) -> Tuple[Tuple[int, str, int, Decimal, Decimal],
                                      ...]:
        table = cls.slice_balance_table(timestamp)
        if table is None:
            raise RuntimeError(
                f'slice balance table at {timestamp} is not created yet, '
                f'please wait a moment')
        return table.select(
            'user_id',
            'asset',
            'account',
            'SUM(`balance`) `balance`',
            'SUM(case when t=3 then balance else 0 end) `lock_balance`',
            group_by='`user_id`, `asset`, `account`'
        )

    @classmethod
    def get_user_balances(cls, timestamp: int = None,
                          account_id: int = None,
                          assets: List[str] = None,
                          order: str = None
                          ) -> Tuple[Tuple[int, str, int, Decimal],
                                     ...]:
        table = cls.slice_balance_table(timestamp)
        if table is None:
            raise RuntimeError(
                f'slice balance table at {timestamp} is not created yet, '
                f'please wait a moment')
        where = None
        if account_id is not None:
            where = f'account = {account_id}'
        if assets:
            where = where or ''
            if where:
                where += ' AND '
            asset_str = '","'.join(assets)
            where += f'`asset` in ("{asset_str}")'

        return table.select(
            'user_id',
            'asset',
            'account',
            'SUM(`balance`) `balance`',
            group_by='`user_id`, `asset`, `account`',
            where=where,
            order_by=order
        )

    @classmethod
    def get_asset_total_summary(cls, timestamp: int = None
                                ) -> List[Tuple[Decimal, Decimal]]:
        table = cls.slice_balance_table(timestamp)
        if table is None:
            raise RuntimeError(
                f'slice balance table at {timestamp} is not created yet, '
                f'please wait a moment')
        return list(table.select(
            'asset',
            'SUM(`balance`) `balance`',
            group_by='`asset`'
        ))

    @classmethod
    def get_asset_summary(cls, timestamp: int = None
                          ) -> Tuple[Tuple[Decimal, Decimal],
                                     ...]:
        table = cls.slice_balance_table(timestamp)
        if table is None:
            raise RuntimeError(
                f'slice balance table at {timestamp} is not created yet, '
                f'please wait a moment')
        return table.select(
            'SUM(`balance`) `balance`',
            'SUM(case when t=1 then balance else 0 end) `balance1`',
            'SUM(case when t=1 then 1 else 0 end) count1',
            'SUM(case when t=2 then balance else 0 end) `balance2`',
            'SUM(case when t=2 then 1 else 0 end) count2',
            'SUM(case when t=3 then balance else 0 end) `balance3`',
            'SUM(case when t=3 then 1 else 0 end) count3',
            'asset',
            group_by='`asset`'
        )

    @classmethod
    def get_balances_group_by_asset(cls):
        table = cls.slice_balance_table()
        where = f'account > 0 and account < {AssetInvestmentConfig.ACCOUNT_ID}'
        recs = table.select(
            'asset',
            'SUM(`balance`) `balance`',
            where=where,
            group_by='`asset`'
        )
        return dict(recs)


class SummaryMixin(object):

    @classmethod
    def is_data_completed(cls, report_date: Union[date, datetime]):
        # server每小时dump，判断有没有第二天dump记录
        report_date += timedelta(days=1)
        columns = ('time',)
        where = f' trade_date = "{report_date.strftime("%Y-%m-%d")}"'
        try:
            # noinspection PyUnresolvedReferences
            records = cls.table('dump_history').select(
                *columns,
                where=where,
            )
            return bool(records)
        except ProgrammingError:
            return False

    @classmethod
    def is_hour_data_completed(cls, hour_stamp: int):
        columns = ('time',)
        where = f' time >= {hour_stamp}'
        try:
            # noinspection PyUnresolvedReferences
            records = cls.table('dump_history').select(
                *columns,
                where=where,
            )
            return bool(records)
        except ProgrammingError:
            return False

    @classmethod
    def get_dump_time(cls):
        columns = ('time',)
        # noinspection PyUnresolvedReferences
        records = cls.table('dump_history').select(
            *columns,
            order_by="id DESC",
            limit=1
        )
        return records[0][0]


    @classmethod
    def convert_date(cls, report_date: Union[date, datetime]):
        return report_date.strftime('%Y-%m-%d'), report_date.strftime('%Y%m')

    @classmethod
    def group_by_user_fee_by_date(cls, report_date: date, user_ids: Iterable = None):
        if not cls.is_data_completed(report_date):
            return []
        return cls.group_by_user_fee(report_date, user_ids)

    @classmethod
    def group_by_user_fee_by_hours(cls, report_date: date, report_hour_ts, user_ids: Iterable = None):
        if not cls.is_hour_data_completed(report_hour_ts):
            raise DataNotReady
        return cls.group_by_user_fee(report_date, user_ids)

    @classmethod
    def group_by_user_fee(cls, report_date: Union[date, datetime], user_ids: List[int] = None):
        columns = ('user_id', 'fee', 'asset')
        select_fields = ('user_id', 'SUM(`fee`)', 'asset')
        date_str, month_str = cls.convert_date(report_date)
        where = f' trade_date = "{date_str}"'
        if user_ids:
            where = f"{where} AND user_id in ({','.join(map(str, user_ids))})"
        try:
            # noinspection PyUnresolvedReferences
            records = cls.table(f'user_fee_summary_{month_str}').select(
                *select_fields,
                where=where,
                group_by='user_id, asset'
            )
        except ProgrammingError:
            records = []
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def group_by_client_fee(cls, report_date: Union[date, datetime]):
        if not cls.is_data_completed(report_date):
            return []
        columns = ('user_id', 'client_id', 'fee', 'asset')
        select_fields = ('user_id', 'client_id', 'SUM(`fee`)', 'asset')
        date_str, month_str = cls.convert_date(report_date)
        where = f' trade_date = "{date_str}"'
        try:
            # noinspection PyUnresolvedReferences
            records = cls.table(f'client_fee_summary_{month_str}').select(
                *select_fields,
                where=where,
                group_by='user_id, client_id, asset'
            )
        except ProgrammingError:
            records = []
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def daily_trade_fee(cls, report_date: date, user_ids: List[int] = None):
        """

        trade_summary_db
        sql = "select asset,sum(fee) from user_fee_summary_{sql_date} where " \
              "trade_date='{query_date}' group by asset;".format(
                sql_date=sql_date, query_date=date)

        """
        if not cls.is_data_completed(report_date):
            return []
        columns = ('asset', 'total_fee')
        select_fields = ('asset',
                         'SUM(fee) AS total_fee')
        date_str = report_date.strftime('%Y-%m-%d')
        month_str = report_date.strftime('%Y%m')
        where = f' trade_date = "{date_str}" '
        if user_ids is not None:
            if len(user_ids) == 0:
                return []
            where = add_users_in_where(where, user_ids)
        # noinspection PyUnresolvedReferences
        records = cls.table(f'user_fee_summary_{month_str}').select(
            *select_fields,
            where=where,
            group_by='asset'
        )
        return [dict(zip(columns, record)) for record in records]

    @classmethod
    def daily_trade_fee_list(cls, report_date: date):
        if not cls.is_data_completed(report_date):
            return []
        columns = ('user_id', 'asset', 'market', 'fee', 'taker_fee', 'maker_fee')
        date_str = report_date.strftime('%Y-%m-%d')
        month_str = report_date.strftime('%Y%m')
        where = f' trade_date = "{date_str}" '
        records = cls.table(f'user_fee_summary_{month_str}').select(
            *columns,
            where=where,
        )
        return [dict(zip(columns, record)) for record in records]

    @classmethod
    def daily_client_trade_fee_list(cls, report_date: date):
        if not cls.is_data_completed(report_date):
            return []
        columns = ('user_id', 'client_id', 'asset', 'market', 'fee')
        date_str = report_date.strftime('%Y-%m-%d')
        month_str = report_date.strftime('%Y%m')
        where = f' trade_date = "{date_str}" '
        records = cls.table(f'client_fee_summary_{month_str}').select(
            *columns,
            where=where,
        )
        return [dict(zip(columns, record)) for record in records]

    @classmethod
    def get_user_trade_summary_tables(cls, start_date: date, end_date: date) -> List[str]:
        tables_suffix = cls.get_tables_suffix(start_date, end_date)

        tables = [f'user_trade_summary_{table_suffix}' for table_suffix in tables_suffix
                  if cls.table(f'user_trade_summary_{table_suffix}').exists()]
        return tables

    @classmethod
    def get_user_fee_summary_tables(cls, start_date: date, end_date: date) -> List[str]:
        tables_suffix = cls.get_tables_suffix(start_date, end_date)
        tables = [f'user_fee_summary_{table_suffix}' for table_suffix in tables_suffix]
        return tables

    @classmethod
    def get_user_detail_tables(cls, start_date, end_date):
        tables_suffix = cls.get_tables_suffix(start_date, end_date)
        tables = [f'user_detail_{table_suffix}' for table_suffix in tables_suffix
                  if cls.table(f'user_detail_{table_suffix}').exists()]
        return tables

    @classmethod
    def get_tables_suffix(cls, start_date: date, end_date: date):
        tables_suffix = []
        start = date(start_date.year, start_date.month, 1)
        end = date(end_date.year, end_date.month, 1)
        month = relativedelta(months=1)
        while start <= end:
            tables_suffix.append(start.strftime('%Y%m'))
            start += month
        return tables_suffix

    @classmethod
    def get_assets_usd_group_by_user_sum(
            cls,
            start_date: datetime.date,
            end_date: datetime.date,
            asset_name: Optional[str] = None,
            user_ids: Optional[List] = [],
    ) -> Dict[int, Decimal]:

        _user_id2deal_usd = defaultdict(Decimal)
        users_assets_usd = cls.get_assets_usd_group_by_user(start_date, end_date, asset_name, user_ids)
        for user_id, trade_mapping in users_assets_usd.items():
            for xxx_asset, usd in trade_mapping.items():
                _user_id2deal_usd[user_id] += usd
        return _user_id2deal_usd

    @classmethod
    def get_assets_usd_group_by_user(
            cls,
            start_date: datetime.date,
            end_date: datetime.date,
            asset_name: Union[None, str],
            user_ids: Optional[List] = [],
    ) -> Dict[int, Dict[str, Decimal]]:
        raise NotImplementedError

    @classmethod
    def get_user_trade_summary_mapping(
            cls,
            start_date: datetime.date,
            end_date: datetime.date,
            asset_name: Union[None, str],
    ) -> Dict[int, List[Dict]]:
        user_mapping = defaultdict(list)
        rows_dict = cls.get_user_trade_summary_data(start_date, end_date, asset_name)
        for row_dict in rows_dict:
            user_mapping[row_dict['user_id']].append(row_dict)
        return user_mapping

    @classmethod
    def get_user_trade_summary_data(
            cls,
            start_date: datetime.date,
            end_date: datetime.date,
            asset_name: Union[None, str],
            user_ids: Optional[List] = [],
            columns: Optional[Tuple] = None
    ) -> List[Dict]:
        raise NotImplementedError


class TradeSummaryDB(SummaryMixin, ExternalDB):

    """
    dump_history
    coin_trade_summary
    user_fee_summary_{date}
    user_trade_summary_{date}
    # user_trade_summary 按月分表，每小时快照，交易量币种对应关系为
    # (deal_amount, stock_asset), (deal_volume, money_asset)
    # user_trade_summary以用户维度统计，会统计对手盘交易量，
    # 因此该表交易总量是coin_trade_summary的两倍，做统计报表时需要注意
    """

    config = 'TRADE_SUMMARY_MYSQL'

    @classmethod
    def get_coin_trade_summary_by_date(
            cls, report_date: Union[date, datetime], columns: List[str] = None) -> List[Dict]:
        if not cls.is_data_completed(report_date):
            return []

        if columns:
            columns = tuple(columns)
        else:
            columns = ('market', 'stock_asset', 'money_asset', 'deal_amount',
                       'deal_volume', 'deal_count', 'deal_user_count',
                       'deal_user_list', 'taker_buy_amount',
                       'taker_sell_amount',
                       'taker_buy_count', 'taker_sell_count')
        where = f' trade_date = "{report_date.strftime("%Y-%m-%d")}"'
        try:
            records = cls.table('coin_trade_summary').select(
                *columns,
                where=where,
            )
        except ProgrammingError:
            records = []
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_user_daily_trade_summary_records(cls, user_id: int, market: str,
                                             start_time: int, end_time: int):
        columns = (
            'trade_date', 'buy_amount', 'buy_volume', 'sell_amount', 'sell_volume'
        )
        if start_time > end_time:
            raise InvalidArgument
        start_date = timestamp_to_date(start_time)
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date = timestamp_to_date(end_time)
        end_date_str = end_date.strftime('%Y-%m-%d')
        table_names = []
        while start_date <= end_date:
            table_names.append(start_date.strftime('%Y%m'))
            start_date = next_month(start_date.year, start_date.month)

        where = f' trade_date >= "{start_date_str}" AND trade_date < "{end_date_str}" ' \
                f' AND market = "{market}" AND user_id = {user_id}'
        records = []
        for table_name in table_names:
            try:
                s = cls.table(f'user_trade_summary_{table_name}').select(
                    *columns,
                    where=where,
                )
                records.extend(list(dict(zip(columns, r)) for r in s))
            except ProgrammingError as e:
                if e.args[0] == 1146:
                    # table not exists
                    continue
                raise
        return records

    @classmethod
    def get_trade_summary_by_user_ids(cls,
                                      start_time: int, end_time: int, asset: str,
                                      markets: List[str], user_ids: set):
        columns = ('user_id', 'deal_amount', 'buy_amount', 'sell_amount')
        select_fields = (
            'user_id',
            'SUM(`deal_amount`) AS deal_amount',
            'SUM(`buy_amount`) AS buy_amount',
            'SUM(`sell_amount`) AS sell_amount'
        )
        if start_time > end_time or not markets or not user_ids:
            raise InvalidArgument
        start_date = timestamp_to_date(start_time)
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date = timestamp_to_date(end_time)
        end_date_str = end_date.strftime('%Y-%m-%d')
        table_names = []
        while start_date <= end_date:
            table_names.append(start_date.strftime('%Y%m'))
            start_date = next_month(start_date.year, start_date.month)

        user_id_str = ','.join(map(str, user_ids))
        markets_str = '","'.join(markets)

        where = f' trade_date >= "{start_date_str}" ' \
                f'AND trade_date < "{end_date_str}" ' \
                f'AND market in ("{markets_str}") ' \
                f'AND user_id in ({user_id_str}) ' \
                f'AND stock_asset = "{asset}"'
        records = []
        for table_name in table_names:
            try:
                s = cls.table(f'user_trade_summary_{table_name}').select(
                    *select_fields,
                    where=where,
                    group_by='user_id'
                )
                records.extend(list(dict(zip(columns, r)) for r in s))
            except ProgrammingError as e:
                if e.args[0] == 1146:
                    # table not exists
                    continue
                raise
        return records

    @classmethod
    def get_trade_summary_from_detail_table(cls, start_time: int, end_time: int, markets: List[str]):
        """ 获取用户买/卖的总数（同下面的get_trade_summary，支持分钟级） """
        start_date = timestamp_to_date(start_time)
        end_date = timestamp_to_date(end_time)
        tables = cls.get_user_detail_tables(start_date, end_date)
        columns = ('user_id', 'buy_amount', 'sell_amount')
        select_fields = (
            'user_id',
            'SUM(`buy_amount`) AS buy_amount',
            'SUM(`sell_amount`) AS sell_amount'
        )
        where = f' time >= {start_time} AND time < {end_time} '
        if len(markets) == 1:
            where += f' AND market="{markets[0]}" '
        elif len(markets) > 1:
            markets_str = '","'.join(markets)
            where += f' AND `market` in ("{markets_str}") '
        res = []
        for table in tables:
            records = cls.table(table).select(
                *select_fields,
                where=where,
                group_by='user_id',
            )
            res.extend(list(dict(zip(columns, r)) for r in records))
        return res

    @classmethod
    def get_client_trade_summary_by_date(cls, report_date):
        columns = ('user_id', 'client_id', 'asset', 'deal_amount')
        select_fields = (
            'user_id',
            'client_id',
            "money_asset",
            'SUM(deal_volume) AS deal_volume',
        )
        report_date_str = report_date.strftime('%Y-%m-%d')
        table_name = report_date.strftime('%Y%m')
        where = f'trade_date = "{report_date_str}" '
        records = []
        s = cls.table(f'client_trade_summary_{table_name}').select(
            *select_fields,
            where=where,
            group_by='user_id, client_id, money_asset'
        )
        records.extend(list(dict(zip(columns, r)) for r in s))
        return records

    @classmethod
    def get_trade_summary(
            cls,
            start_time: int = 0, end_time: int = 0,
            market_type: Optional[str] = None, asset: str = None):
        columns = ('user_id', 'deal_amount', 'buy_amount', 'sell_amount')
        select_fields = (
            'user_id',
            'SUM(`deal_amount`) AS deal_amount',
            'SUM(`buy_amount`) AS buy_amount',
            'SUM(`sell_amount`) AS sell_amount'
        )
        if start_time > end_time:
            raise InvalidArgument
        start_date = timestamp_to_date(start_time)
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date = timestamp_to_date(end_time)
        end_date_str = end_date.strftime('%Y-%m-%d')
        table_names = []
        while start_date <= end_date:
            table_names.append(start_date.strftime('%Y%m'))
            start_date = next_month(start_date.year, start_date.month)

        where = f' trade_date >= "{start_date_str}" ' \
                f'AND trade_date < "{end_date_str}" ' \
                'AND deal_amount > 0'

        if market_type:
            where += f' AND market = "{market_type}" '
        if asset:
            where += f' AND stock_asset = "{asset}" '

        records = []
        for table_name in table_names:
            try:
                s = cls.table(f'user_trade_summary_{table_name}').select(
                    *select_fields,
                    where=where,
                    group_by='user_id'
                )
                records.extend(list(dict(zip(columns, r)) for r in s))
            except ProgrammingError as e:
                if e.args[0] == 1146:
                    # table not exists
                    continue
                raise
        return records

    @classmethod
    def get_market_maker_data(cls, report_date: date, user_ids: Iterable[int]):
        if not cls.is_data_completed(report_date):
            return []
        columns = ("market", "money_asset", "maker_volume", "user_id")
        select_fields = (
            "market",
            "money_asset",
            "SUM(maker_volume) AS maker_volume",
            "user_id",
        )
        date_str = report_date.strftime("%Y-%m-%d")
        month_str = report_date.strftime("%Y%m")
        where = f' trade_date = "{date_str}" AND deal_amount > 0'
        where = add_users_in_where(where, list(user_ids))
        records = cls.table(f"user_trade_summary_{month_str}").select(
            *select_fields,
            where=where,
            group_by="market, money_asset, user_id",
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def group_by_user_asset_by_date(cls, report_date: date, user_ids: Iterable = None):
        if not cls.is_data_completed(report_date):
            return []
        return cls.group_by_user_date_asset(report_date, user_ids)

    @classmethod
    def group_by_user_asset_by_hours(cls, report_date: date, report_hour_ts, user_ids: Iterable = None):
        if not cls.is_hour_data_completed(report_hour_ts):
            raise DataNotReady
        return cls.group_by_user_date_asset(report_date, user_ids)

    @classmethod
    def group_by_user_date_asset(cls, report_date: date, user_ids: Iterable = None):
        columns = ('user_id', 'trade_date', 'stock_asset', 'money_asset', 'market', 'deal_volume',
                   'deal_amount', 'maker_volume', 'maker_amount',
                   'taker_volume', 'taker_amount')
        select_fields = ('user_id', 'trade_date', 'stock_asset', 'money_asset', 'market',
                         'SUM(deal_volume) AS deal_volume',
                         'SUM(deal_amount) AS deal_amount',
                         'SUM(maker_volume) AS maker_volume',
                         'SUM(maker_amount) AS maker_amount',
                         'SUM(taker_volume) AS taker_volume',
                         'SUM(taker_amount) AS taker_amount',)
        date_str = report_date.strftime('%Y-%m-%d')
        month_str = report_date.strftime('%Y%m')
        where = f' trade_date = "{date_str}" AND deal_amount > 0'
        if user_ids:
            where = f"{where} AND user_id in ({','.join(map(str, user_ids))})"
        # noinspection PyUnresolvedReferences
        records = cls.table(f'user_trade_summary_{month_str}').select(
            *select_fields,
            where=where,
            group_by='user_id, trade_date, money_asset, market'
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def group_by_date_market_asset(cls, report_date: date):
        if not cls.is_data_completed(report_date):
            return []
        columns = ('market', 'money_asset', 'stock_asset', 'deal_volume',
                   'deal_amount', 'maker_volume', 'maker_amount',
                   'taker_volume', 'taker_amount')
        select_fields = ('market', 'money_asset', 'stock_asset',
                         'SUM(deal_volume) AS deal_volume',
                         'SUM(deal_amount) AS deal_amount',
                         'SUM(maker_volume) AS maker_volume',
                         'SUM(maker_amount) AS maker_amount',
                         'SUM(taker_volume) AS taker_volume',
                         'SUM(taker_amount) AS taker_amount',)
        date_str = report_date.strftime('%Y-%m-%d')
        month_str = report_date.strftime('%Y%m')
        where = f' trade_date = "{date_str}" AND deal_amount > 0'
        records = cls.table(f'user_trade_summary_{month_str}').select(
            *select_fields,
            where=where,
            group_by='market, money_asset, stock_asset'
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def group_by_asset(cls, report_date: date):
        if not cls.is_data_completed(report_date):
            return []
        columns = ('user_id', 'stock_asset')
        select_fields = ('user_id', 'stock_asset')
        date_str = report_date.strftime('%Y-%m-%d')
        month_str = report_date.strftime('%Y%m')
        where = f' trade_date = "{date_str}" AND deal_amount > 0'
        records = cls.table(f'user_trade_summary_{month_str}').select(
            *select_fields,
            where=where,
            group_by=f'user_id, stock_asset'
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_assets_usd_group_by_user(
            cls,
            start_date: datetime.date,
            end_date: datetime.date,
            asset_name: Union[None, str],
            user_ids: Optional[List] = [],
    ) -> Dict[int, Dict[str, Decimal]]:
        query_data = cls.get_user_trade_summary_data(start_date, end_date, asset_name, user_ids)
        curr_date = start_date
        daily_price_map = dict()
        while curr_date <= end_date:
            daily_price_map[curr_date] = AssetPrice.get_close_price_map(curr_date)
            curr_date += timedelta(days=1)
        user_trade_summary_map = defaultdict(lambda: defaultdict(Decimal))
        for item in query_data:
            price = daily_price_map[item['trade_date']].get(item['money_asset'], 0)
            usd = item['deal_volume'] * price
            user_trade_summary_map[item['user_id']][item['money_asset']] += usd
            user_trade_summary_map[item['user_id']][item['stock_asset']] += usd

        return user_trade_summary_map

    @classmethod
    def get_trade_amount_group_by_user(
            cls,
            start_date: datetime.date,
            end_date: datetime.date,
            user_ids: list | None = None
    ) -> dict[int, dict[date, Decimal]]:
        query_data = cls.get_user_trade_summary_data(start_date, end_date, user_ids=user_ids, asset_name=None)
        daily_price_map = AssetPrice.get_close_price_range_map(start_date, end_date)
        ret = defaultdict(lambda: defaultdict(Decimal))
        for item in query_data:
            price = daily_price_map[item['trade_date']].get(item['money_asset'], 0)
            deal_usd = item['deal_volume'] * price
            ret[item['user_id']][item['trade_date']] += deal_usd
        return ret

    @classmethod
    def get_user_trade_summary_data(
            cls,
            start_date: datetime.date,
            end_date: datetime.date,
            asset_name: Union[None, str],
            user_ids: Optional[List] = [],
            columns: Optional[Tuple] = None,
    ):
        if not columns:
            columns = ('trade_date', 'user_id', 'money_asset', 'stock_asset', 'deal_volume')
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        where = f' trade_date >= "{start_date_str}" ' \
                f'AND trade_date < "{end_date_str}" ' \
                'AND deal_amount > 0'
        if asset_name:
            where += f' AND (stock_asset = "{asset_name}" OR money_asset = "{asset_name}")'
        if user_ids:
            where = add_users_in_where(where, user_ids)
        query_data = []
        for table_name in cls.get_user_trade_summary_tables(start_date, end_date):
            s = cls.table(f'{table_name}').select(
                *columns,
                where=where,
            )
            query_data.extend(list(dict(zip(columns, r)) for r in s))
        return query_data


class PerpetualSysHistoryDB(ExternalDB):

    config = 'PERPETUAL_SYS_HISTORY'
    # cet_price_history
    # funding_history
    # index_price_history
    # insurance_history
    # positionliq_history
    # premium_history
    # sign_price_history
    # adl_deal_history

    class InsuranceChange(Enum):
        GTE_0 = '>=0'
        LT_0 = '<0'

    @classmethod
    def get_liquidation_history(
            cls,
            start_time: int = 0, end_time: int = 0,
            offset: int = 0, limit: int = 10,
            user_id: Optional[int] = None, market_type: Optional[str] = None,
            side: Optional[int] = None, insurance_change: Optional[InsuranceChange] = None,
            export: bool = False,
    ):
        columns = ('id', 'position_id', 'liq_time', 'market', 'user_id', 'side',
                   'liq_amount', 'open_price', 'bkr_price', 'profit_real',
                   'liq_price')
        if side:
            where = f' side = {side} '
        else:
            where = ' side in (1, 2) '
        if start_time:
            where += f' AND update_time >= {start_time} '
        if end_time:
            where += f' AND update_time < {end_time} '
        if user_id:
            where += f' AND user_id = {user_id} '
        if market_type:
            where += f' AND market = "{market_type}" '
        if insurance_change is not None:
            if insurance_change is cls.InsuranceChange.GTE_0:
                where += ' AND insurance >= 0'
            else:
                where += ' AND insurance < 0'

        records = cls.table('positionliq_history').select(
            *columns,
            where=where,
            order_by='id DESC',
            limit=(offset, limit) if not export else None,
        )
        total = cls.table('positionliq_history').select(
            'COUNT(*)',
            where=where
        )
        return total[0][0], list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_liq_summary(cls, start_time: int = 0, end_time: int = 0, user_id: Optional[int] = None,
                              market_type: Optional[str] = None,side: Optional[int] = None,
                              insurance_change: Optional[InsuranceChange] = None):
        if side:
            where = f' side = {side} '
        else:
            where = ' side in (1, 2) '
        if start_time:
            where += f' AND update_time >= {start_time} '
        if end_time:
            where += f' AND update_time < {end_time} '
        if user_id:
            where += f' AND user_id = {user_id} '
        if market_type:
            where += f' AND market = "{market_type}" '
        if insurance_change is not None:
            if insurance_change is cls.InsuranceChange.GTE_0:
                where += ' AND insurance >= 0'
            else:
                where += ' AND insurance < 0'
        columns = ('id', 'user_id', 'position_id')
        start_id = 0
        limit = 10000
        res = []
        while True:
            where += f' AND id > {start_id}'
            records = cls.table('positionliq_history').select(
                *columns,
                where=where,
                limit=limit,
                order_by='id asc',
            )
            if not records:
                break
            user_ids, position_ids = set(), set()
            for _, user_id, position_id in records:
                user_ids.add(user_id)
                position_ids.add(position_id)
            res.append((user_ids, position_ids))
            start_id = records[-1][0]
        return res

    @classmethod
    def get_insurance_records(cls, start_time, end_time, user_id, market, insurance_change):
        columns = ('asset', 'type', 'change')
        where = f'time >= {start_time} AND time < {end_time} '
        if user_id:
            where += f' AND user_id = {user_id} '
        if market:
            where += f' AND market = "{market}" '
        if insurance_change is not None:
            if insurance_change is cls.InsuranceChange.GTE_0:
                where += ' AND type = 1'
            else:
                where += ' AND type = 2'
        records = cls.table('insurance_history').select(
            *columns,
            where=where
        )
        return records

    @classmethod
    def get_latest_insurance(cls, asset: str):
        columns = ('time', 'asset', 'total')
        where = f' `asset` = "{asset}" '
        records = cls.table('insurance_history').select(
            *columns,
            where=where,
            order_by=' `time` desc ',
            limit=1
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_insurance_detail(cls, start_time: int, end_time: int,
                             offset: int = 0, limit: int = 10,
                             asset: Optional[str] = None,
                             insurance_type: Optional[int] = None,):
        columns = ('time', 'asset', 'type', 'change', 'total')
        where_list = []
        if start_time and end_time:
            where_list.append(f' time >= {start_time} AND time < {end_time} ')
        if insurance_type:
            where_list.append(f' type = {insurance_type} ')
        if asset:
            where_list.append(f' asset = "{asset}" ')
        records = cls.table('insurance_history').select(
            *columns,
            where=' AND '.join(where_list),
            order_by='time DESC',
            limit=(offset, limit)
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_insurance_history(cls, start_time: int, end_time: int,
                              offset: int = 0, limit: int = 10,
                              user_id: Optional[int] = None,
                              asset: Optional[str] = None):
        columns = ('user_id', 'type', 'time', 'market', 'deal_id', 'change',
                   'amount', 'price', 'id', 'bkr_price', 'side')
        sql = f'SELECT a.user_id, a.type, a.time, a.market, a.deal_id, ' \
              f'a.change, a.amount, a.price, b.id, b.bkr_price, b.side ' \
              f'FROM insurance_history a ' \
              f'JOIN positionliq_history b ' \
              f'ON a.position_id = b.position_id ' \
              f'WHERE a.time >= {start_time} ' \
              f'AND a.time < {end_time} '
        if user_id:
            sql += f' AND a.user_id = {user_id} '
        if asset:
            sql += f' AND a.asset = "{asset}" '
        sql += f' ORDER BY a.time DESC LIMIT {offset}, {limit}; '
        table = cls.table('insurance_history')
        table.execute(sql)
        records = table.cursor.fetchall()
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_insurance_obj(cls, start_time: int, end_time: int):
        from ..models import DailyPerpetualInsuranceReport

        # 理论变动
        columns = ('type', 'asset', 'SUM(`change`)')
        where = f' time >= {start_time} AND time < {end_time} '
        records = cls.table('insurance_history').select(
            *columns,
            where=where,
            group_by='`type`, `asset`'
        )
        stash_dict = defaultdict(lambda: defaultdict(Decimal))
        for item in records:
            if int(item[0]) == 2:
                stash_dict[item[1]]['decrease_amount'] += item[2]
            elif int(item[0]) == 1:
                stash_dict[item[1]]['increase_amount'] += item[2]

        # 实际变动
        real_records = cls.table('real_insurance_history').select(
            *columns,
            where=where,
            group_by='`type`, `asset`'
        )
        for item in real_records:
            # 3表示划转
            if int(item[0]) in (2, 3):
                stash_dict[item[1]]['real_decrease_amount'] += item[2]
                if int(item[0]) == 3:
                    stash_dict[item[1]]['transfer'] += item[2]
            elif int(item[0]) == 1:
                stash_dict[item[1]]['real_increase_amount'] += item[2]

        results = []
        for asset in PerpetualCoinTypeCache().read_aside():
            if asset == 'CET':
                continue
            where = f' asset = "{asset}" '
            where += f' AND time < {end_time} '
            temp_records = cls.table('insurance_history').select(
                'total',
                where=where,
                order_by='time DESC',
                limit=1
            )
            real_records = cls.table("real_insurance_history").select(
                'total',
                where=where,
                order_by='time DESC',
                limit=1
            )
            if (not temp_records) and (not real_records):
                continue
            total_balance = temp_records[0][0] if temp_records else Decimal()
            total_real_balance = real_records[0][0] if real_records else Decimal()
            results.append(DailyPerpetualInsuranceReport(
                asset=asset,
                report_date=timestamp_to_datetime(start_time).date(),
                increase_amount=amount_to_str(
                    stash_dict[asset]['increase_amount'], 8),
                decrease_amount=amount_to_str(
                    stash_dict[asset]['decrease_amount'], 8),
                real_increase_amount=amount_to_str(
                    stash_dict[asset]['real_increase_amount'], 8),
                real_decrease_amount=amount_to_str(
                    stash_dict[asset]['real_decrease_amount'], 8),
                total_balance=amount_to_str(total_balance, 8),
                real_balance=amount_to_str(total_real_balance, 8),
                transfer=amount_to_str(stash_dict[asset]['transfer'], 8),
            ))
        return results

    @classmethod
    def get_auto_deleveraging_records(cls, start_time: int, end_time: int,
                                      user_id: Optional[int] = None,
                                      market: Optional[str] = None,
                                      side: Optional[int] = 0):
        columns = ('deal_user_id', 'id', 'user_id', 'deal_id', 'time',
                   'market', 'side', 'leverage', 'open_price', 'amount',
                   'position_amount', 'price')
        where = ' deal_type in (7, 9, 13) '
        if start_time and end_time:
            where += f' AND time >= {start_time} AND time < {end_time} '
        if user_id:
            where += f' AND user_id = {user_id} '
        if market:
            where += f' AND market = "{market}" '
        if side:
            where += f' AND side = {side}'
        record_list = []
        table = cls.table('adl_deal_history')
        records = table.select(
            *columns,
            where=where,
        )
        records = list(dict(zip(columns, record)) for record in records)
        record_list.extend(records)
        return sorted(record_list, key=lambda x: x['time'], reverse=True)

    @classmethod
    def get_markets_insurance_summary(cls, type_: int, start_time: int = 0, end_time: int = 0):
        columns = ('market', 'change', 'asset')
        where = f'type={type_}'
        if start_time:
            where += f' AND time >= {start_time}'
        if end_time:
            where += f' AND time <= {end_time}'

        records = cls.table('insurance_history').select(
            *columns,
            where=where,
        )
        price_rates = PriceManager.assets_to_usd()
        res = defaultdict(Decimal)
        for market, change, asset in records:
            change_usd = price_rates.get(asset, 0) * Decimal(change)
            res[market] += quantize_amount(change_usd, 2)
        return res

    @classmethod
    def get_funding_rate_records(cls, start_time: int, end_time: int, market: str = None):
        columns = ('market', 'time', 'funding_rate')
        where = f'time >= {start_time} AND time <= {end_time}'
        if market:
            where += f' AND market = {market}'
        records = cls.table('funding_history').select(
            *columns,
            where=where
        )
        return list(dict(zip(columns, s)) for s in records)


class PerpetualSummaryDB(SummaryMixin, ExternalDB):

    """
    dump_history
    coin_trade_summary
    user_fee_summary_{date}
    client_fee_summary_{date}
    user_trade_summary_{date}
    client_trade_summary_{date}
    # user_trade_summary 按月分表，每天快照。
    # 对于反向合约市场，交易量币种对应关系为：
    # (deal_amount, money_asset), (deal_volume, stock_asset)
    # 对于正向合约市场，交易量币种对应关系为：
    # (deal_amount, stock_asset), (deal_volume, money_asset)
    # user_trade_summary以用户维度统计，会统计对手盘交易量，
    # client_trade_summary_是以client_id和user_id维度统计
    # 因此该表交易总量是coin_trade_summary的两倍，做统计报表时需要注意
    """

    config = 'PERPETUAL_SUMMARY'

    @classmethod
    def sum_user_trade_summary_by_date(
            cls, report_date: Union[date, datetime],
            user_ids: List[int] = None) -> Decimal:
        if not cls.is_data_completed(report_date):
            return Decimal()
        columns = ('asset', 'amount')
        date_str, month_str = cls.convert_date(report_date)
        where = f' trade_date = "{date_str}" AND deal_amount > 0'
        if user_ids is not None:
            if len(user_ids) == 0:
                return Decimal()
        where = add_users_in_where(where, user_ids)
        records = cls.table(f'user_trade_summary_{month_str}').select(
            'money_asset', f'SUM(CASE WHEN market_type={PerpetualMarketType.INVERSE} '
            'THEN deal_amount ELSE deal_volume END)',
            where=where,
            group_by='money_asset'
        )
        result = list(dict(zip(columns, record)) for record in records)
        prices = AssetPrice.get_close_price_map(report_date)
        prices['USD'] = Decimal('1')
        return sum(prices.get(item['asset'], 0) * item['amount'] for item in result)


    @classmethod
    def get_coin_trade_summary_by_date(
            cls, report_date: Union[date, datetime], columns: List[str] = None) -> List[Dict]:
        if not cls.is_data_completed(report_date):
            return []

        if columns:
            columns = tuple(columns)
        else:
            columns = ('market', 'stock_asset', 'money_asset', 'deal_amount',
                       'deal_volume', 'deal_count', 'deal_user_count',
                       'deal_user_list', 'taker_buy_amount',
                       'taker_sell_amount',
                       'taker_buy_count', 'taker_sell_count',
                       'market_type')
        where = f' trade_date = "{report_date.strftime("%Y-%m-%d")}"'
        try:
            records = cls.table('coin_trade_summary').select(
                *columns,
                where=where,
            )
        except ProgrammingError:
            records = []
        return list(dict(zip(columns, record)) for record in records)


    @classmethod
    def get_trade_summary(
            cls,
            start_time: int = 0, end_time: int = 0,
            market_type: Optional[str] = None):
        columns = ('user_id', 'deal_amount', 'buy_amount', 'sell_amount', 'asset')
        direct_select_fields = (
            'user_id',
            'SUM(deal_amount) AS deal_amount',
            'SUM(buy_amount) AS buy_amount',
            'SUM(sell_amount) AS sell_amount',
            'stock_asset AS asset'
        )
        inverse_select_fields = (
            'user_id',
            'SUM(deal_amount) AS deal_amount',
            'SUM(buy_amount) AS buy_amount',
            'SUM(sell_amount) AS sell_amount',
            'money_asset AS asset'
        )

        if start_time > end_time:
            raise InvalidArgument
        start_date = timestamp_to_date(start_time)
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date = timestamp_to_date(end_time)
        end_date_str = end_date.strftime('%Y-%m-%d')
        table_names = []
        while start_date <= end_date:
            table_names.append(start_date.strftime('%Y%m'))
            start_date = next_month(start_date.year, start_date.month)

        where = f' trade_date >= "{start_date_str}" ' \
                f'AND trade_date < "{end_date_str}" ' \
            'AND deal_amount > 0'
        if market_type:
            where += f' AND market = "{market_type}" '
        records = []
        for table_name in table_names:
            try:
                # 正向合约交易
                s = cls.table(f'user_trade_summary_{table_name}').select(
                    *direct_select_fields,
                    where=where + f' AND market_type = "{PerpetualMarketType.DIRECT.value}"',
                    group_by='user_id, asset'
                )
                direct_records = list(dict(zip(columns, r)) for r in s)
                records.extend(direct_records)

                # 反向合约交易
                s = cls.table(f'user_trade_summary_{table_name}').select(
                    *inverse_select_fields,
                    where=where + f' AND market_type = "{PerpetualMarketType.INVERSE.value}"',
                    group_by='user_id, asset'
                )
                inverse_records = list(dict(zip(columns, r)) for r in s)
                records.extend(inverse_records)

            except ProgrammingError as e:
                if e.args[0] == 1146:
                    # table not exists
                    continue
                raise
        return records

    @classmethod
    def get_client_trade_summary_by_date(cls, report_date):
        columns = ('user_id', 'client_id','deal_amount', 'buy_amount', 'sell_amount', 'asset')
        direct_select_fields = (
            'user_id',
            'client_id',
            'SUM(deal_volume) AS deal_amount',
            'SUM(buy_volume) AS buy_amount',
            'SUM(sell_volume) AS sell_amount',
            'money_asset AS asset'
        )
        inverse_select_fields = (
            'user_id',
            'client_id',
            'SUM(deal_amount) AS deal_amount',
            'SUM(buy_amount) AS buy_amount',
            'SUM(sell_amount) AS sell_amount',
            'money_asset AS asset'
        )

        report_date_str = report_date.strftime('%Y-%m-%d')
        table_name = report_date.strftime('%Y%m')
        where = f'trade_date = "{report_date_str}"'
        records = []
        # 正向合约交易
        s = cls.table(f'client_trade_summary_{table_name}').select(
            *direct_select_fields,
            where=where + f' AND market_type = "{PerpetualMarketType.DIRECT.value}"',
            group_by='user_id, client_id, asset'
        )
        direct_records = list(dict(zip(columns, r)) for r in s)
        records.extend(direct_records)

        # 反向合约交易
        s = cls.table(f'client_trade_summary_{table_name}').select(
            *inverse_select_fields,
            where=where + f' AND market_type = "{PerpetualMarketType.INVERSE.value}"',
            group_by='user_id, client_id, asset'
        )
        inverse_records = list(dict(zip(columns, r)) for r in s)
        records.extend(inverse_records)

        return records

    @classmethod
    def group_by_user_asset_by_date(cls, report_date: date, user_ids: Iterable = None):
        if not cls.is_data_completed(report_date):
            return []
        return cls.group_by_user_date_asset(report_date, user_ids)

    @classmethod
    def group_by_user_asset_by_hours(cls, report_date: date, report_hour_ts, user_ids: Iterable = None):
        if not cls.is_hour_data_completed(report_hour_ts):
            raise DataNotReady
        return cls.group_by_user_date_asset(report_date, user_ids)

    @classmethod
    def group_by_user_date_asset(cls, report_date: date, user_ids: Iterable = None, hour_ts: int = None):
        columns = ('user_id', 'trade_date', 'stock_asset', 'money_asset', 'market',
                   'deal_amount', 'maker_amount', 'taker_amount',
                   'deal_volume', 'maker_volume', 'taker_volume')
        inverse_trade_select_fields = (
            'user_id',
            'trade_date',
            'stock_asset',
            'money_asset',
            'market',
            'SUM(deal_amount) AS deal_amount',
            'SUM(maker_amount) AS maker_amount',
            'SUM(taker_amount) AS taker_amount',
            'SUM(deal_volume) AS deal_volume',
            'SUM(maker_volume) AS maker_volume',
            'SUM(taker_volume) AS taker_volume'
        )
        direct_trade_select_fields = (
            'user_id',
            'trade_date',
            'stock_asset',
            'money_asset',
            'market',
            'SUM(deal_volume) AS deal_amount',
            'SUM(maker_volume) AS maker_amount',
            'SUM(taker_volume) AS taker_amount',
            'SUM(deal_amount) AS deal_volume',
            'SUM(maker_amount) AS maker_volume',
            'SUM(taker_amount) AS taker_volume'
        )
        date_str = report_date.strftime('%Y-%m-%d')
        month_str = report_date.strftime('%Y%m')
        where = f' trade_date = "{date_str}" AND deal_amount > 0'
        if user_ids:
            where = f"{where} AND user_id in ({','.join(map(str, user_ids))})"
        # noinspection PyUnresolvedReferences
        asset_price_map = AssetPrice.get_close_price_map(report_date)
        # 正向合约交易
        direct_records = cls.table(f'user_trade_summary_{month_str}').select(
            *direct_trade_select_fields,
            where=where + f' AND market_type = {PerpetualMarketType.DIRECT.value}',
            group_by='user_id, trade_date, money_asset, market'
        )
        direct_records = list(dict(zip(columns, record)) for record in direct_records)
        for record in direct_records:
            record['deal_amount'] = asset_price_map[record['money_asset']] * \
                record['deal_amount']
            record['maker_amount'] = asset_price_map[record['money_asset']] * \
                record['maker_amount']
            record['taker_amount'] = asset_price_map[record['money_asset']] * \
                record['taker_amount']
        # 反向合约交易
        inverse_records = cls.table(f'user_trade_summary_{month_str}').select(
            *inverse_trade_select_fields,
            where=where + f' AND market_type = {PerpetualMarketType.INVERSE.value}',
            group_by='user_id, trade_date, money_asset, market'
        )
        inverse_records = list(dict(zip(columns, record)) for record in inverse_records)

        return direct_records + inverse_records

    @classmethod
    def group_by_date_market_asset(cls, report_date: date):
        if not cls.is_data_completed(report_date):
            return []
        columns = ('market', 'money_asset', 'stock_asset', 'deal_volume',
                   'deal_amount', 'maker_volume', 'maker_amount',
                   'taker_volume', 'taker_amount')
        inverse_select_fields = ('market', 'money_asset', 'stock_asset',
                                 'SUM(deal_volume) AS deal_volume',
                                 'SUM(deal_amount) AS deal_amount',
                                 'SUM(maker_volume) AS maker_volume',
                                 'SUM(maker_amount) AS maker_amount',
                                 'SUM(taker_volume) AS taker_volume',
                                 'SUM(taker_amount) AS taker_amount',)
        direct_select_fields = ('market', 'money_asset', 'stock_asset',
                                'SUM(deal_amount) AS deal_volume',
                                'SUM(deal_volume) AS deal_amount',
                                'SUM(maker_amount) AS maker_volume',
                                'SUM(maker_volume) AS maker_amount',
                                'SUM(taker_amount) AS taker_volume',
                                'SUM(taker_volume) AS taker_amount',)
        date_str = report_date.strftime('%Y-%m-%d')
        month_str = report_date.strftime('%Y%m')
        where = f' trade_date = "{date_str}" AND deal_amount > 0'

        inverse_records = cls.table(f'user_trade_summary_{month_str}').select(
            *inverse_select_fields,
            where=where + f' AND market_type={PerpetualMarketType.INVERSE.value}',
            group_by='market, money_asset, stock_asset'
        )
        inverse_records = \
            list(dict(zip(columns, record)) for record in inverse_records)

        direct_records = cls.table(f'user_trade_summary_{month_str}').select(
            *direct_select_fields,
            where=where + f' AND market_type={PerpetualMarketType.DIRECT.value}',
            group_by='market, money_asset, stock_asset'
        )
        direct_records = \
            list(dict(zip(columns, record)) for record in direct_records)

        return inverse_records + direct_records

    @classmethod
    def group_by_asset(cls, report_date: date):
        if not cls.is_data_completed(report_date):
            return []
        columns = ('user_id', 'stock_asset')
        select_fields = ('user_id', 'stock_asset')
        date_str = report_date.strftime('%Y-%m-%d')
        month_str = report_date.strftime('%Y%m')
        where = f' trade_date = "{date_str}" AND deal_amount > 0'
        records = cls.table(f'user_trade_summary_{month_str}').select(
            *select_fields,
            where=where,
            group_by=f'user_id, stock_asset'
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_assets_usd_group_by_user(
            cls,
            start_date: datetime.date,
            end_date: datetime.date,
            asset_name: Union[None, str],
            user_ids: Optional[List] = [],
    ) -> Dict[int, Dict[str, Decimal]]:
        query_data = cls.get_user_trade_summary_data(start_date, end_date, asset_name, user_ids)
        user_trade_summary_map = defaultdict(lambda: defaultdict(Decimal))
        curr_date = start_date
        daily_price_map = dict()
        while curr_date <= end_date:
            daily_price_map[curr_date] = AssetPrice.get_close_price_map(curr_date)
            curr_date += timedelta(days=1)
        for item in query_data:
            if item['market_type'] == PerpetualMarketType.DIRECT.value:
                user_trade_summary_map[item['user_id']][item['money_asset']] += \
                    item['deal_volume'] * daily_price_map[item['trade_date']][item['money_asset']]
                user_trade_summary_map[item['user_id']][item['stock_asset']] += \
                    item['deal_volume'] * daily_price_map[item['trade_date']][item['money_asset']]
            else:
                user_trade_summary_map[item['user_id']][item['money_asset']] += item['deal_amount']
                user_trade_summary_map[item['user_id']][item['stock_asset']] += \
                    item['deal_amount']
        return user_trade_summary_map

    @classmethod
    def get_trade_amount_group_by_user(
            cls,
            start_date: datetime.date,
            end_date: datetime.date,
            user_ids: list | None = None
    ) -> dict[int, dict[date, Decimal]]:
        query_data = cls.get_user_trade_summary_data(start_date, end_date, user_ids=user_ids, asset_name=None)
        daily_price_map = AssetPrice.get_close_price_range_map(start_date, end_date)
        ret = defaultdict(lambda: defaultdict(Decimal))
        for item in query_data:
            if item['market_type'] == PerpetualMarketType.DIRECT.value:
                deal_usd = item['deal_volume'] * daily_price_map[item['trade_date']][item['money_asset']]
                ret[item['user_id']][item['trade_date']] += deal_usd
            else:
                ret[item['user_id']][item['trade_date']] += item['deal_amount']
        return ret

    @classmethod
    def get_user_trade_summary_data(
            cls,
            start_date: datetime.date,
            end_date: datetime.date,
            asset_name: Union[None, str],
            user_ids: Optional[List] = [],
            columns: Optional[Tuple] = None,
    ):
        if not columns:
            columns = (
                'trade_date', 'user_id', 'deal_amount',
                'deal_volume', 'market_type', 'money_asset',
                'stock_asset', 'market'
            )
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        where = f' trade_date >= "{start_date_str}" ' \
                f'AND trade_date < "{end_date_str}" ' \
                'AND deal_amount > 0'
        if asset_name:
            where += f' AND stock_asset = "{asset_name}"'
        if user_ids:
            where = add_users_in_where(where, user_ids)
        query_data = []
        for table_name in cls.get_user_trade_summary_tables(start_date, end_date):
            s = cls.table(f'{table_name}').select(
                *columns,
                where=where,
            )
            query_data.extend(list(dict(zip(columns, r)) for r in s))
        return query_data

    @classmethod
    def get_perpetual_trade_summary(cls, start_time: int, end_time: int):
        """获取主动买/卖的交易详细数据"""
        start_date = timestamp_to_date(start_time)
        end_date = timestamp_to_date(end_time)
        tables = cls.get_user_detail_tables(start_date, end_date)
        columns = ('market', 'taker_buy_amount', 'taker_buy_volume',
                   'taker_sell_amount', 'taker_sell_volume')
        where = f' time >= {start_time} AND time < {end_time}'
        res = []
        for table in tables:
            records = cls.table(table).select(
                *columns,
                where=where
            )
            res.extend(list(dict(zip(columns, r)) for r in records))
        return res


class PerpetualHistoryDB0(ExternalDB):

    config = 'PERPETUAL_HISTORY_DB_CONFIG0'


class PerpetualHistoryDB1(ExternalDB):

    config = 'PERPETUAL_HISTORY_DB_CONFIG1'


class PerpetualHistoryDB2(ExternalDB):

    config = 'PERPETUAL_HISTORY_DB_CONFIG2'


class PerpetualHistoryDB3(ExternalDB):

    config = 'PERPETUAL_HISTORY_DB_CONFIG3'


class PerpetualHistoryDB4(ExternalDB):

    config = 'PERPETUAL_HISTORY_DB_CONFIG4'


class PerpetualHistoryDB(TradeHistoryDB):
    """
    deal_history (
        deal_type(
            1: 开仓, 2: 加仓, 3: 减仓,
            4: 平仓(自定义平仓), 5: 减仓(强制减仓),
            6: deprecated 强平, 不区分减仓跟平仓,
            7: deprecated 自动减仓, 不区分减仓跟平仓,
            8: 减仓(系统强平), 9: 减仓(自动减仓), 10: 减仓(止盈),
            11: 减仓(止损), 12: 平仓(系统强平), 13: 平仓(自动减仓),
            14: 平仓(止盈), 15: 平仓(止损)
        )
    )
    """

    DBS = (
        PerpetualHistoryDB0,
        PerpetualHistoryDB1,
        PerpetualHistoryDB2,
        PerpetualHistoryDB3,
        PerpetualHistoryDB4
    )

    @classmethod
    def get_position_stop_loss_take_profit_history(cls, user_id: int, position_id: int,
                                                   offset: int = 0, limit: int = 10):
        _db, _table = cls.user_to_db_and_table(
            user_id, 'position_stoploss_takeprofit_history')
        where = f' user_id = {user_id}  AND  position_id = {position_id}'
        columns = ('time', 'market', 'state', 'type', 'stop_type',
                   'amount', 'stop_price', 'market')
        records = _db.table(_table).select(
            *columns,
            where=where,
            order_by='id DESC',
            limit=(offset, limit)
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_position_margin_history(cls, user_id: int, position_id: int,
                                    offset: int = 0, limit: int = 10):
        _db, _table = cls.user_to_db_and_table(
            user_id, 'position_margin_history')
        where = f' user_id = {user_id}  AND  position_id = {position_id} AND type != 5'  # 仓位调整查询排除结算记录
        columns = ('type', 'position_type', 'margin_change', 'margin_amount', 'leverage', 'time', 'id',
                   'position_id', 'liq_price', 'settle_price', 'market')
        records = _db.table(_table).select(
            *columns,
            where=where,
            order_by='id DESC',
            limit=(offset, limit)
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_settle_history(cls, user_id: int, position_id: int,
                           offset: int = 0, limit: int = 10):
        _db, _table = cls.user_to_db_and_table(
            user_id, 'position_margin_history')
        where = f' user_id = {user_id}  AND  position_id = {position_id} AND type = 5'
        columns = ('type', 'position_type', 'margin_change', 'leverage', 'time',
                   'position_id', 'settle_price', 'market')
        records = _db.table(_table).select(
            *columns,
            where=where,
            order_by='id DESC',
            limit=(offset, limit)
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_position_funding_history(cls, user_id: int, position_id: int = None,
                                     offset: int = 0, limit: int = 10,
                                     market: str = '', side: int = 0,
                                     start_time: int = 0, end_time: int = 0,
                                     include_zero: bool = True):
        _db, _table = cls.user_to_db_and_table(
            user_id, 'position_funding_history')
        where = f' user_id = {user_id} '
        if position_id:
            where += f' AND position_id = {position_id} '
        if start_time:
            where += f' AND time >= {start_time} '
        if end_time:
            where += f' AND time < {end_time} '
        if market:
            where += f' AND market = "{market}" '
        if side:
            where += f' AND side = {side}'
        if not include_zero:
            where += ' AND funding_rate != 0'
        columns = ('funding_rate', 'funding', 'time', 'amount', 'price', 'market', 'liq_price',
                   'user_id', 'asset', 'type', 'position_id', 'side')
        records = _db.table(_table).select(
            *columns,
            where=where,
            order_by='id DESC',
            limit=(offset, limit)
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_position_funding_summary(cls, user_id: int, position_id: int):
        _db, _table = cls.user_to_db_and_table(
            user_id, 'position_funding_history')
        where = f' user_id = {user_id} AND position_id = {position_id}'
        columns = ('SUM(funding)', )
        records = _db.table(_table).select(
            *columns,
            where=where,
        )

        result = dict(total_amount=0)
        if records:
            result['total_amount'] = quantize_amount(records[0][0] or 0, PrecisionEnum.COIN_PLACES)
        return result

    @classmethod
    def get_position_funding_summarys(cls, user_id: int, position_ids: list[int]) -> dict[int, Decimal]:
        result = defaultdict(Decimal)
        if not position_ids:
            return result
        pids_str = ','.join(map(str, position_ids))
        _db, _table = cls.user_to_db_and_table(
            user_id, 'position_funding_history')
        where = f' user_id = {user_id} AND position_id in ({pids_str})'
        columns = ('position_id', 'SUM(funding)',)
        records = _db.table(_table).select(
            *columns,
            where=where,
            group_by='`user_id`, `position_id`'
        )

        for position_id, funding in records:
            result[position_id] = quantize_amount(funding or 0, PrecisionEnum.COIN_PLACES)
        return result
    
    @classmethod
    def get_deal_history(cls, user_id: int, position_id: int,
                         offset: int = 0, limit: int = 10):
        _db, _table = cls.user_to_db_and_table(
            user_id, 'deal_history')
        where = f' user_id = {user_id}  AND  position_id = {position_id}'
        columns = ('market', 'deal_type', 'amount', 'position_amount', 'margin_amount', 'deal_fee',
                   'deal_profit', 'deal_margin', 'leverage', 'price', 'time', 'id',
                   'fee_asset', 'order_id', 'liq_price', 'settle_price')
        records = _db.table(_table).select(
            *columns,
            where=where,
            order_by='id DESC',
            limit=(offset, limit)
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_user_deals(cls, user_ids: Iterable, start: int, end: int) -> List:
        res = []
        fields = ['market', 'position_id', 'deal_profit', 'fee_asset', 'deal_fee']
        dbs_tables = cls.users_to_dbs_and_tables(user_ids, table_name='deal_history')
        for db_tables in dbs_tables:
            _db = db_tables[0]
            for _table, _table_user_ids in db_tables[1].items():
                user_id_str = ','.join(map(str, _table_user_ids))
                where = f'time >= {start} AND time < {end}'
                where += f' AND user_id in ({user_id_str})'
                recs = _db.table(_table).select(
                    *fields,
                    where=where
                )
                if not recs:
                    continue
                res.extend(recs)
        return list(dict(zip(fields, record)) for record in res)

    @classmethod
    def get_history_position_side_dic(cls, start: int, end: int, user_ids: Iterable[int]):
        fields = ['position_id', 'side']
        res = dict()
        dbs_tables = cls.users_to_dbs_and_tables(user_ids, table_name='position_history')
        for db_tables in dbs_tables:
            _db = db_tables[0]
            for _table, _table_user_ids in db_tables[1].items():
                where = f'update_time >= {start} AND update_time < {end}'
                user_id_str = ','.join(map(str, _table_user_ids))
                where += f' AND user_id in ({user_id_str})'
                records = _db.table(_table).select(
                    *fields,
                    where=where,
                )
                res.update(dict(records))
        return res

    @classmethod
    def get_realized_settle_profit(cls, user_ids: Iterable, start: int, end: int) -> List:
        res = []
        fields = ['market', 'position_id', 'margin_change']
        dbs_tables = PerpetualHistoryDB.users_to_dbs_and_tables(user_ids, table_name='position_margin_history')
        for db_tables in dbs_tables:
            _db = db_tables[0]
            for _table, _table_user_ids in db_tables[1].items():
                user_id_str = ','.join(map(str, _table_user_ids))
                where = f'time >= {start} AND time < {end} AND type = 5'
                where += f' AND user_id in ({user_id_str})'
                recs = _db.table(_table).select(
                    *fields,
                    where=where
                )
                if not recs:
                    continue
                res.extend(recs)
        return list(dict(zip(fields, record)) for record in res)

    @classmethod
    def get_position_history(cls, user_ids: List[int], cond: str, offset: int = None,
                             limit: int = None):
        tables = defaultdict(list)
        for user_id in user_ids:
            _db, _table = PerpetualHistoryDB.user_to_db_and_table(
                user_id, 'position_history')
            tables[(_db, _table)].append(user_id)

        result = []
        columns = (
            'id', 'user_id', 'create_time', 'update_time', 'market', 'side', 'open_price',
            'finish_type', 'amount_max', 'open_val_max', 'amount_max_margin', 'profit_real', 'position_id',
            'first_price', 'latest_price',
        )
        for k, v in tables.items():
            _db, _table = k
            user_id_str = ','.join(map(str, v))
            if cond:
                where = f' {cond} AND user_id in ({user_id_str})'
            else:
                where = f' user_id in ({user_id_str})'
            if offset is not None and limit is not None:
                records = _db.table(_table).select(
                    *columns,
                    where=where,
                    order_by='id DESC',
                    limit=(offset, limit)
                )
            else:
                records = _db.table(_table).select(
                    *columns,
                    where=where,
                    order_by='id DESC',
                )
            result.extend(list(dict(zip(columns, item)) for item in records))
        return result

    @classmethod
    def get_order_history(cls, user_ids: List[int],
                          fields: List[str], cond: str, table_type: str):
        tables = defaultdict(list)
        for user_id in user_ids:
            _db, _table = PerpetualHistoryDB.user_to_db_and_table(
                user_id, table_type)
            tables[(_db, _table)].append(user_id)

        result = []
        columns = fields
        for k, v in tables.items():
            _db, _table = k
            user_id_str = ','.join(map(str, v))
            if cond:
                where = f' {cond} AND user_id in ({user_id_str})'
            else:
                where = f' user_id in ({user_id_str})'
            records = _db.table(_table).select(
                *columns,
                where=where,
                order_by='id DESC',
            )
            result.extend(list(dict(zip(columns, item)) for item in records))
        return result

    @classmethod
    def get_users_deals(cls, user_ids: List[int], fields: List[str], cond):
        tables = defaultdict(list)
        for user_id in user_ids:
            _db, _table = PerpetualHistoryDB.user_to_db_and_table(
                user_id, 'deal_history')
            tables[(_db, _table)].append(user_id)

        result = []
        for k, v in tables.items():
            _db, _table = k
            user_id_str = ','.join(map(str, v))
            if cond:
                where = f' {cond} AND user_id in ({user_id_str})'
            else:
                where = f' user_id in ({user_id_str})'

            records = _db.table(_table).select(
                *fields,
                where=where,
                order_by='id DESC',
            )
            result.extend(list(dict(zip(fields, item)) for item in records))
        return result

    @classmethod
    def sum_deal_history(cls, user_id: int, position_id: int):
        _db, _table = cls.user_to_db_and_table(
            user_id, 'deal_history')
        where = f' `user_id` = {user_id}  AND  `position_id` = {position_id}' \
                f' AND `deal_type` in (6, 8, 12)'
        records = _db.table(_table).select(
            'SUM(`deal_stock`)',
            'SUM(`deal_insurance`)',
            where=where,
        )
        return records[0]
    
    @classmethod
    def sum_deal_fee(cls, user_id: int, position_id: int):
        _db, _table = cls.user_to_db_and_table(
            user_id, 'deal_history')
        where = f' `user_id` = {user_id}  AND  `position_id` = {position_id}'
        records = _db.table(_table).select(
            'SUM(`deal_fee`)',
            where=where,
        )
        return quantize_amount(records[0][0] or 0, PrecisionEnum.COIN_PLACES)

    @classmethod
    def calc_close_position_avg_price(cls, user_id: int, position_id: int):
        _db, _table = cls.user_to_db_and_table(
            user_id, 'deal_history')
        where = f' `user_id` = {user_id}  AND  `position_id` = {position_id}'
        where += f' AND deal_type not in (1, 2, 16)'
        records = _db.table(_table).select(
            *['price', 'amount'],
            where=where,
        )
        deal = Decimal()
        deal_amount = Decimal()
        for price, amount in records:
            deal += price * amount
            deal_amount += amount
        avg_price = deal / deal_amount if deal_amount else Decimal()
        return quantize_amount(avg_price or 0, PrecisionEnum.COIN_PLACES)

    @classmethod
    def sum_deal_fees(cls, user_id: int, position_ids: list[int]) -> dict[int, Decimal]:
        result = defaultdict(Decimal)
        if not position_ids:
            return result
        pids_str = ','.join(map(str, position_ids))
        _db, _table = cls.user_to_db_and_table(
            user_id, 'deal_history')
        where = f' `user_id` = {user_id}  AND  `position_id` in ({pids_str})'
        columns = ('position_id', 'SUM(deal_fee)',)
        records = _db.table(_table).select(
            *columns,
            where=where,
            group_by='`user_id`, `position_id`'
        )
        for position_id, funding in records:
            result[position_id] = quantize_amount(funding or 0, PrecisionEnum.COIN_PLACES)
        return result

    @classmethod
    def get_liq_finished_order(cls, market: str,
                               start_time: int = 0, end_time: int = 0,
                               offset: int = 0, limit: int = 10,
                               ):
        _db, _table = cls.user_to_db_and_table(
            0, 'order_history')
        where = f' user_id = 0 AND market = "{market}" AND target = 6 '
        if start_time:
            where += f' AND create_time >= {start_time} '
        if end_time:
            where += f' AND create_time < {end_time} '
        columns = ('create_time', 'side', 'amount', 'left', 'price', 'deal_stock')
        records = _db.table(_table).select(
            *columns,
            where=where,
            order_by='id DESC',
            limit=(offset, limit)
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_liq_finished_order_summary(cls, start_time: int, end_time: int):
        _db, _table = cls.user_to_db_and_table(
            0, 'order_history')
        where = ' user_id = 0 AND target = 6 '
        where += f' AND create_time >= {start_time} '
        where += f' AND create_time < {end_time} '
        columns = ('market', 'side', 'amount', 'deal_stock', 'left')
        records = _db.table(_table).select(
            *columns,
            where=where,
            order_by='id DESC',
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_liq_finished_order_by_id(cls, id_: int):
        _db, _table = cls.user_to_db_and_table(
            0, 'order_history')
        where = f' id > {id_} AND user_id = 0 AND target = 6'
        columns = ('id', 'create_time', 'market', 'side', 'amount', 'deal_stock', 'left')
        records = _db.table(_table).select(
            *columns,
            where=where,
            order_by='id',
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_first_rec_id(cls, date_timestamp):
        _db, _table = cls.user_to_db_and_table(
            0, 'order_history')
        where = f' create_time >= {date_timestamp} AND user_id = 0 AND target = 6'
        records = _db.table(_table).select(
            'id',
            where=where,
            order_by='id asc',
            limit=1
        )
        return records[0][0] if records else 0

    @classmethod
    def get_liquidation_detail(cls, position_id: int, user_id: int):
        _db, _table = cls.user_to_db_and_table(user_id, 'deal_history')
        columns = ('time', 'market', 'user_id', 'deal_user_id', 'order_id',
                   'deal_order_id', 'position_id', 'side', 'role', 'price',
                   'amount', 'margin_amount', 'deal_stock', 'deal_margin')
        where = f' user_id = {user_id} AND position_id = {position_id} ' \
                'AND deal_type in (6, 8, 12, 7, 9, 13) '
        records = _db.table(_table).select(
            *columns,
            where=where,
            order_by='id DESC',
        )
        return list(dict(zip(columns, record)) for record in records)

    @classmethod
    def get_position_history_records(cls, db_, table_, start_id) -> List:
        table = cls.DBS[db_].table(f'position_history_{table_}')
        columns = ['id', 'user_id', 'market', 'leverage', 'side', 'market_type',
                             'profit_real', 'amount_max_margin']
        where = f'id > {start_id}'
        records = table.select(
            *columns,
            where=where,
            order_by='id DESC',
        )
        return records or []

    @classmethod
    def get_position_history_by_update_time(cls, db_index, table_index, start_time: int, end_time: int) -> list:
        table = cls.DBS[db_index].table(f'position_history_{table_index}')
        columns = ['id', 'user_id', 'market', 'side', 'market_type', 'profit_real']
        where = f'update_time >= {start_time} AND update_time < {end_time}'
        records = table.select(
            *columns,
            where=where,
            order_by='id DESC',
        )
        return records or []


class PerpetualLogDB(ExternalDB):

    """
    slice_history
    slice_balance_{timestamp} (
        type (1: available, 2: margin, 3: frozen_fee, 4: frozen_margin, 5: 未实现盈亏)
    )   # 每10分钟快照生成，但只会保留最近7天的表
    slice_insurance_{timestamp} (
        type (1: 理论, 2: 实际)
    )
    operlog_{timestamp}
    slice_order_{timestamp}
    slice_stop_{timestamp}
    slice_update_{timestamp}
    slice_position_{timestamp}
    slice_preference_{timestamp}
    kline_history_{YYYY-MM}
    """

    config = 'PERPETUAL_LOG'
    SLICE_INTERVAL = 10 * 60
    @classmethod
    def get_slice_history_timestamp(cls, timestamp: int = None,
                                    interval: int = 3600
                                    ) -> Optional[int]:
        """每10分钟快照"""
        table = cls.table('slice_history')
        if timestamp is not None:
            timestamp -= timestamp % interval
            records = table.select(
                'time',
                where=f'`time` >= {timestamp - 180} '
                      f'AND `time` <= {timestamp + 180}'
            )
        else:
            records = table.select(
                'time',
                order_by='`id` desc',
                limit=1
            )
        return records[0][0] if records else None

    @classmethod
    def slice_balance_table(cls, timestamp: int = None,
                            interval: int = 3600,
                            ) -> Optional[ExternalDB.Table]:
        timestamp = cls.get_slice_history_timestamp(timestamp, interval)
        if timestamp is None:
            return None
        return cls.table(f'slice_balance_{timestamp}')

    @classmethod
    def slice_insurance_table(cls, timestamp: int = None,
                              interval: int = 3600
                              ) -> Optional[ExternalDB.Table]:
        timestamp = cls.get_slice_history_timestamp(timestamp, interval)
        if timestamp is None:
            return None
        return cls.table(f'slice_insurance_{timestamp}')

    @classmethod
    def slice_preference_table(cls, timestamp: int = None
                               ) -> Optional[ExternalDB.Table]:
        timestamp = cls.get_slice_history_timestamp(timestamp)
        if timestamp is None:
            return None
        return cls.table(f'slice_preference_{timestamp}')

    @classmethod
    def get_user_balances(cls, timestamp: int = None
                          ) -> Tuple[Tuple[int, str, Decimal],
                                     ...]:
        table = cls.slice_balance_table(timestamp)
        if table is None:
            raise RuntimeError(
                f'slice balance table at {timestamp} is not created yet, '
                f'please wait a moment')
        return table.select(
            'user_id',
            'asset',
            'SUM(`balance`) `balance`',
            group_by='`user_id`, `asset`'
        )

    @classmethod
    def get_asset_total_summary(cls, timestamp: int = None
                                ) -> List[Tuple[Decimal, Decimal]]:
        table = cls.slice_balance_table(timestamp)
        if table is None:
            raise RuntimeError(
                f'slice balance table at {timestamp} is not created yet, '
                f'please wait a moment')
        return list(table.select(
            'asset',
            'SUM(`balance`) `balance`',
            group_by='`asset`'
        ))

    @classmethod
    def get_balances(cls, *select_field,
                     where: str = None,
                     group_by: str = None, timestamp: int = None) -> Tuple[Tuple[int, str, Decimal],
                                                                           ...]:
        table = cls.slice_balance_table(timestamp)
        if table is None:
            raise RuntimeError(
                f'slice balance table at {timestamp} is not created yet, '
                f'please wait a moment')
        return table.select(
            *select_field,
            group_by=group_by,
            where=where
        )

    @classmethod
    def slice_position_table(cls, timestamp: int = None,
                             interval: int = 3600,
                             ) -> Optional[ExternalDB.Table]:
        timestamp = cls.get_slice_history_timestamp(timestamp, interval)
        if timestamp is None:
            return None
        return cls.table(f'slice_position_{timestamp}')

    @classmethod
    def slice_order_table(cls, timestamp: int = None,
                          interval: int = 3600
                             ) -> Optional[ExternalDB.Table]:
        timestamp = cls.get_slice_history_timestamp(timestamp, interval)
        if timestamp is None:
            return None
        return cls.table(f'slice_order_{timestamp}')

    @classmethod
    def slice_stop_order_table(cls, timestamp: int = None
                             ) -> Optional[ExternalDB.Table]:
        timestamp = cls.get_slice_history_timestamp(timestamp)
        if timestamp is None:
            return None
        return cls.table(f'slice_stop_{timestamp}')

    @classmethod
    def get_positions(cls, *select_field,
                      where: str = None,
                      group_by: str = None, timestamp: int = None) -> Tuple[Tuple[int, str, Decimal],
                                                                            ...]:
        table = cls.slice_position_table(timestamp)
        if table is None:
            raise RuntimeError(
                f'slice position table at {timestamp} is not created yet, '
                f'please wait a moment')
        return table.select(
            *select_field,
            group_by=group_by,
            where=where
        )

    @classmethod
    def get_insurance_balance(cls, timestamp: int = None, interval: int = 3600) -> Tuple[Tuple[str, Decimal], ...]:
        table = cls.slice_insurance_table(timestamp, interval)
        if table is None:
            raise RuntimeError(
                f'slice insurance balance table at {timestamp} is not created yet, '
                f'please wait a moment')
        return table.select('asset', 'SUM(balance)', where="`type`=2", group_by='asset')

    @classmethod
    def get_settle_on_nums(cls, timestamp: int = None) -> int:
        table = cls.slice_preference_table(timestamp)
        if table is None:
            raise RuntimeError(
                f'slice preference balance table at {timestamp} is not created yet, '
                f'please wait a moment')
        rows = table.select('count(*)', where="`settle_switch`=1 and `market`=''")
        if not rows and not rows[0]:
            raise RuntimeError(
                f'settle_switch count failed, try again!')
        return rows[0][0]

    @classmethod
    def get_position_slice_data(cls, report_time: int, exclude_user_ids: Iterable[int] = None) -> List:
        ts = report_time - report_time % 3600
        slice_table = cls.slice_position_table(ts)
        if not slice_table:
            return []
        last = 0
        fields = ("id", "market", "side", "amount")
        if exclude_user_ids:
            ids_str = ','.join(map(str, exclude_user_ids))
        else:
            ids_str = ''
        res = []
        while True:
            if ids_str:
                where = f"id > {last} AND user_id not in ({ids_str})"
            else:
                where = f'id > {last}'
            rows = slice_table.select(*fields, where=where, order_by="id",
                                      limit=10000)
            if not rows:
                break
            res.extend(rows)
            last = rows[-1][0]
        return res
