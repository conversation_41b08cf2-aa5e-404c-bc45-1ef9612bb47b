# -*- coding: utf-8 -*-
import json
from decimal import Decimal
from datetime import timedelta
from enum import Enum
from typing import Dict, Optional, NamedTuple, List

from flask import current_app
from flask_babel import force_locale
from celery.schedules import crontab
from sqlalchemy import func

from app.models import db, Message
from app.models.wallet import (
    AbnormalDepositApplication,
    AbnormalDepositApplicationChangelog,
)
from app.common import CeleryQueues
from app.common.constants import MessageTitle, MessageContent, MessageWebLink, PrecisionEnum
from app.config import config
from app.business import WalletClient, lock_call, CacheLock, LockKeys, UserPreferences
from app.business.alert import send_alert_notice
from app.business.email import (
    send_abnormal_deposit_application_additional_info_email,
    send_abnormal_deposit_application_rejected_email,
)
from app.business.abnormal_deposit import notice_abnormal_deposit_application_finished, AbnormalDepositBusiness
from app.caches.operation import (
    AbnormalDepositApplicationNoticeCache,
    AbnormalDepositApplicationAdditionNoticeCache,
)
from app.utils import scheduled, celery_task, route_module_to_celery_queue, now, amount_to_str, datetime_to_str, batch_iter


route_module_to_celery_queue(__name__, CeleryQueues.REAL_TIME)


# 钱包充值找回-状态
class WalletDepositRecoveryStatus(Enum):
    CREATED = "未处理"
    VERIFIED = "已验证"
    INVALID = "无效"  # info invalid, verify failed
    AUDITED = "已审核"
    PROCESSING = "处理中"
    EXCHANGING = "兑换中"
    FINISHED = "已完成"
    FAILED = "失败"  # can --> CANCELLED | AUDITED
    CANCELLED = "已取消"
    DELETED = "已删除"  # ?


# 钱包异常充值-状态
class WalletAbnormalDepositStatus(Enum):
    CREATED = "待审核"
    FINISHED = "已完成"
    REJECTED = "已拒绝"
    DELETED = "已删除"


class _SendResult(NamedTuple):
    # 请求钱包接口的结果
    success: bool
    wallet_id: Optional[int]


def send_abnormal_deposit_application_to_wallet(row: AbnormalDepositApplication) -> Optional[_SendResult]:
    """ 发送web的申请记录 到 wallet """
    client = WalletClient()
    if row.wallet_type == AbnormalDepositApplication.WalletType.ABNORMAL_DEPOSIT:
        if row.type in [
            AbnormalDepositApplication.Type.PENDING_DEPOSIT,
            AbnormalDepositApplication.Type.RECIPIENT_IS_HOT_WALLET,
            AbnormalDepositApplication.Type.WRONG_CHAIN_OF_ADDRESS,
        ]:
            # 新增【异常充值】
            client.send_abnormal_deposit(
                request_id=row.id,
                user_id=row.user_id,
                asset=row.asset,
                chain=row.chain,
                address=row.address,
                amount=row.tx_amount,
                tx_id=row.tx_id,
                vout=0,
                memo="",
                remark="",
            )
            return _SendResult(True, None)

        elif row.type in [
            AbnormalDepositApplication.Type.WRONG_MEMO,
        ]:
            # 审核已有的异常充值记录，
            client.audit_abnormal_deposit(
                request_id=row.id,
                chain=row.chain,
                asset=row.asset,
                tx_id=row.tx_id,
                address=row.address,
                user_id=row.user_id,
                auditor_id=row.auditor_id,
            )
            return _SendResult(True, None)

    elif row.wallet_type == AbnormalDepositApplication.WalletType.DEPOSIT_RECOVERY:
        if row.type in [
            AbnormalDepositApplication.Type.WRONG_ASSET,
            AbnormalDepositApplication.Type.WRONG_CHAIN_WITH_RECORDABLE,
            AbnormalDepositApplication.Type.WRONG_CHAIN_WITHOUT_RECORDABLE,
        ]:
            # 新增【充值找回】
            if row.type in [
                AbnormalDepositApplication.Type.WRONG_ASSET,
                AbnormalDepositApplication.Type.WRONG_CHAIN_WITHOUT_RECORDABLE,
            ]:
                # 【充错链，不可入账】和【充错币】 都是 退回
                solution = "REFUND"
                deduction = row.expect_fee_amount if row.is_new and row.is_need_fee else None
                if row.is_new and row.is_need_fee:
                    assert row.expect_fee_asset == row.asset
            else:
                # 【充错链，可入账】 是 兑换
                solution = "EXCHANGE"
                deduction = None
            response = client.send_deposit_recovery(
                request_id=row.id,
                user_id=row.user_id,
                asset=row.asset,
                chain=row.chain,
                address=row.address,
                amount=row.tx_amount,
                deduction=deduction,
                tx_id=row.tx_id,
                solution=solution,
                sent_to=row.refund_address or "",
            )
            if response.get("successful") and response.get("ids"):
                return _SendResult(True, response["ids"][0])
            else:
                current_app.logger.warning(f"send_deposit_recovery row:{row.id}, error_response: {response}")


@celery_task
@lock_call(with_args=True)
def process_audited_abnormal_deposit_application(application_id: int) -> bool:
    """ 已复审的资产自助找回申请 -> 处理中 """
    with CacheLock(LockKeys.abnormal_deposit_application(application_id)):
        db.session.rollback()

        row: AbnormalDepositApplication = AbnormalDepositApplication.query.get(application_id)
        if row.status != AbnormalDepositApplication.Status.CHECKED:
            raise ValueError(f"not checked row:{application_id}")
        if row.wallet_type == AbnormalDepositApplication.WalletType.UNKNOWN:
            raise ValueError(f"unknown wallet_type row:{application_id}")
        if not row.asset:
            raise ValueError(f"empty asset row:{application_id}")
        if not row.tx_amount:
            raise ValueError(f"zero tx_amount row:{application_id}")

        result = send_abnormal_deposit_application_to_wallet(row)
        if result and result.success:
            row.status = AbnormalDepositApplication.Status.PROCESSING
            row.processed_at = now()
            row.wallet_id = result.wallet_id
            db.session.commit()
            return True
        else:
            # next time retry
            return False


@scheduled(crontab(minute="*/5"))
@lock_call()
def process_audited_abnormal_deposit_applications_schedule():
    """ 定时任务，已复审的资产自助找回申请 -> 处理中 """
    rows = (
        AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.status == AbnormalDepositApplication.Status.CHECKED,
        )
        .with_entities(AbnormalDepositApplication.id)
        .all()
    )
    for row in rows:
        process_audited_abnormal_deposit_application.delay(row.id)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_abnormal_deposit_applications_status_schedule():
    """ 定时任务，更新`处理中`的资产自助找回申请记录的状态 """
    rows = AbnormalDepositApplication.query.filter(
        AbnormalDepositApplication.status == AbnormalDepositApplication.Status.PROCESSING,
    ).all()
    for row in rows:
        try:
            update_abnormal_deposit_application_status(row)
        except Exception as e:
            current_app.logger.error(f"update_status abnormal_deposit_application<{row.id}> failed: {e!r}")


def update_abnormal_deposit_application_status(row: AbnormalDepositApplication):
    if row.status != AbnormalDepositApplication.Status.PROCESSING:
        raise ValueError(f"cannot update_abnormal_deposit_application_status<{row.id}:{row.status.name}>")

    client = WalletClient()
    if row.wallet_type == AbnormalDepositApplication.WalletType.ABNORMAL_DEPOSIT:
        abnormal_info = client.get_abnormal_deposit(row.id)
        if abnormal_info:
            wallet_status = abnormal_info["status"]
            if wallet_status == WalletAbnormalDepositStatus.FINISHED.name:
                if row.is_new and row.type in AbnormalDepositBusiness.DEPOSIT_TO_FINANCE_TYPES:
                    AbnormalDepositBusiness.finish_new_apply_by_finance_user_deposit(row.id)
                else:
                    _finish_abnormal_deposit_application(row.id, abnormal_info)

    elif row.wallet_type == AbnormalDepositApplication.WalletType.DEPOSIT_RECOVERY:
        recovery_info = client.get_deposit_recovery(row.id)
        if recovery_info:
            wallet_status = recovery_info["status"]
            if wallet_status == WalletDepositRecoveryStatus.FINISHED.name:
                _finish_abnormal_deposit_application(row.id, recovery_info)

    else:
        raise ValueError(f"row:{row.id} unknown wallet_type")


def _finish_abnormal_deposit_application(application_id: int, wallet_row_info: Dict):
    with CacheLock(LockKeys.abnormal_deposit_application(application_id)):
        db.session.rollback()

        row: AbnormalDepositApplication = AbnormalDepositApplication.query.get(application_id)
        if row.status != AbnormalDepositApplication.Status.PROCESSING:
            raise ValueError(f"_finish_abnormal_deposit_application<{row.id}:{row.status.name}> status error")

        # 【充错链，可入账】还需要资产变更
        is_need_asset_change = row.type == AbnormalDepositApplication.Type.WRONG_CHAIN_WITH_RECORDABLE
        if is_need_asset_change:
            new_status = AbnormalDepositApplication.Status.ASSET_CHANGE_PROCESSING
        else:
            new_status = AbnormalDepositApplication.Status.FINISHED

        old_status = row.status
        row.status = new_status
        row.processed_at = now()
        if row.type in AbnormalDepositApplication.REFUND_TYPES and wallet_row_info.get("sent_tx_id"):
            row.refund_tx_id = wallet_row_info["sent_tx_id"]
        if row.is_new:
            if row.is_need_fee and not row.fee_asset:
                row.fee_asset = row.expect_fee_asset
                row.fee_amount = row.expect_fee_amount

        AbnormalDepositApplicationChangelog.add(
            application_id=row.id,
            user_id=row.user_id,
            admin_user_id=None,
            change_type=AbnormalDepositApplicationChangelog.ChangeType.STATUS.name,
            old_value=old_status.name,
            new_value=new_status.name,
        )
        db.session.commit()

    if is_need_asset_change:
        do_abnormal_deposit_application_asset_changing(application_id)
    elif new_status == AbnormalDepositApplication.Status.FINISHED:
        # 其他找回类型发送成功通知
        notice_abnormal_deposit_application_finished(row)


@celery_task
@lock_call(with_args=True)
def do_abnormal_deposit_application_asset_changing(application_id: int):
    # （可重入）资产变更：扣减CoinEx财务账户资产，给用户账户加上资产
    with CacheLock(LockKeys.abnormal_deposit_application(application_id)):
        db.session.rollback()

        row = AbnormalDepositApplication.query.get(application_id)
        if row.status not in [
            AbnormalDepositApplication.Status.ASSET_CHANGE_PROCESSING,
            AbnormalDepositApplication.Status.ASSET_CHANGE_DEDUCTED,
        ]:
            raise ValueError(f"row:{application_id} 状态不是资产变更态: {row.status.name}")
        if row.type != AbnormalDepositApplication.Type.WRONG_CHAIN_WITH_RECORDABLE:
            raise ValueError(f"row:{application_id} 类型不是WRONG_CHAIN_WITH_RECORDABLE")

        old_status = row.status
        asset = row.asset
        if row.is_new:
            # 新记录 手续费在资产变更时收，收找回币种
            amount = row.tx_amount - row.fee_amount
            assert row.fee_asset == asset
        else:
            # 老记录，审核时单独收了50U，找回数目全部给用户
            amount = row.tx_amount
        assert asset
        assert amount >= Decimal()
        if asset == "BUSD" and row.chain == "BSC":
            # 对于BUSD 给用户资产变更USDT入账
            asset = "USDT"

        history = AbnormalDepositBusiness.get_or_create_transfer_his(row, asset=asset, amount=amount)
        AbnormalDepositBusiness.do_transfer_by_his(row,  history)

    if old_status != row.status:
        AbnormalDepositApplicationChangelog.add(
            application_id=row.id,
            user_id=row.user_id,
            admin_user_id=None,
            change_type=AbnormalDepositApplicationChangelog.ChangeType.STATUS.name,
            old_value=old_status.name,
            new_value=row.status.name,
        )
        db.session.commit()
    if row.status == AbnormalDepositApplication.Status.FINISHED:
        notice_abnormal_deposit_application_finished(row)


def notice_abnormal_deposit_application_additional_info(row: AbnormalDepositApplication):
    """ 补充资料 站内信 和 邮件 """
    pref = UserPreferences(row.user_id)
    message_popup_expired_at = now() + timedelta(days=7)
    msg = Message(
        user_id=row.user_id,
        title=MessageTitle.ABNORMAL_DEPOSIT_APPLICATION_ADDITIONAL_INFO.name,
        content=MessageContent.ABNORMAL_DEPOSIT_APPLICATION_ADDITIONAL_INFO.name,
        params=json.dumps(
            dict(
                time=datetime_to_str(row.created_at, pref.timezone_offset),
                amount=amount_to_str(row.tx_amount, PrecisionEnum.COIN_PLACES),
                asset=row.asset,
            )
        ),
        extra_info=json.dumps(
            dict(
                web_link=MessageWebLink.DEPOSIT_RECOVERY_PAGE.value,
                android_link="",
                ios_link="",
            )
        ),
        display_type=Message.DisplayType.POPUP_WINDOW,
        expired_at=message_popup_expired_at,
        channel=Message.Channel.DEPOSIT_WITHDRAWAL,
    )
    db.session_add_and_commit(msg)
    send_abnormal_deposit_application_additional_info_email.delay(row.id)


def notice_abnormal_deposit_application_rejected(row: AbnormalDepositApplication):
    """ 发送审核不通过通知 """
    pref = UserPreferences(row.user_id)
    lang = pref.language.value
    with force_locale(lang):
        rejection_reason_str = row.rejection_reason_str
    msg = Message(
        user_id=row.user_id,
        title=MessageTitle.ABNORMAL_DEPOSIT_APPLICATION_REJECTED.name,
        content=MessageContent.ABNORMAL_DEPOSIT_APPLICATION_REJECTED.name,
        params=json.dumps(
            dict(
                time=datetime_to_str(row.created_at, pref.timezone_offset),
                amount=amount_to_str(row.tx_amount, PrecisionEnum.COIN_PLACES),
                asset=row.asset,
                rejection_reason=rejection_reason_str,
            )
        ),
        extra_info=json.dumps(
            dict(
                web_link=MessageWebLink.DEPOSIT_RECOVERY_PAGE.value,
                android_link="",
                ios_link="",
            )
        ),
        display_type=Message.DisplayType.TEXT,
        channel=Message.Channel.DEPOSIT_WITHDRAWAL,
    )
    db.session_add_and_commit(msg)
    send_abnormal_deposit_application_rejected_email.delay(row.id)


@scheduled(crontab(minute=30, hour="*/4"))
@lock_call()
def process_abnormal_deposit_application_addition_notice_cache():
    """ 处理待补充资料的充值找回 """
    # 订单状态变为“待补充资料”，加入该缓存
    addition_info_rows = AbnormalDepositApplication.query.filter(
        AbnormalDepositApplication.status == AbnormalDepositApplication.Status.CREATED,
        AbnormalDepositApplication.additional_info_status.in_([
            AbnormalDepositApplication.AdditionalInfoStatus.REQUIRED,
            AbnormalDepositApplication.AdditionalInfoStatus.REQUIRE_MORE,
            AbnormalDepositApplication.AdditionalInfoStatus.REQUIRE_MORE_CR,
        ]),
    ).with_entities(AbnormalDepositApplication.id).all()
    AbnormalDepositApplicationAdditionNoticeCache.add_many([row.id for row in addition_info_rows])

    now_ts = int(now().timestamp())
    # 7天后，如果该订单状态依旧为“待补充资料”，则系统发送邮件/站内信提醒用户补充资料
    re_notice_second_delta = 86400 * 7
    # 14天后，如果该订单状态依旧为“待补充资料”，则系统自动拒绝该订单，拒绝理由为：长时间未提交验证资料。
    reject_second_delta = 86400 * 14

    cache = AbnormalDepositApplicationAdditionNoticeCache()
    apply_id_data_map = cache.hgetall()
    if not apply_id_data_map:
        return

    all_app_ids = [int(i) for i in apply_id_data_map]
    for chunk_ids in batch_iter(all_app_ids, 200):
        rows: List[AbnormalDepositApplication] = AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.id.in_(chunk_ids),
        ).all()
        for row in rows:
            key_ = str(row.id)
            if not (
                row.status == AbnormalDepositApplication.Status.CREATED
                and row.additional_info_status in [
                    AbnormalDepositApplication.AdditionalInfoStatus.REQUIRED,
                    AbnormalDepositApplication.AdditionalInfoStatus.REQUIRE_MORE,
                    AbnormalDepositApplication.AdditionalInfoStatus.REQUIRE_MORE_CR,
                ]
            ):
                apply_id_data_map.pop(key_)
                continue

            data = json.loads(apply_id_data_map[key_])
            delta_second = now_ts - int(data["time"])
            has_re_notice = int(data.get("has_re_notice", 0))
            if reject_second_delta > delta_second >= re_notice_second_delta:
                if not has_re_notice:
                    # re_notice
                    notice_abnormal_deposit_application_additional_info(row)
                    data["has_re_notice"] = 1
                    apply_id_data_map[key_] = json.dumps(data)
            elif delta_second >= reject_second_delta:
                # reject
                row.status = new_status = AbnormalDepositApplication.Status.REJECTED
                row.is_custom_reason = False
                row.rejection_reason = AbnormalDepositApplication.RejectionReason.ADDITIONAL_INFO_NOT_PROVIDED_FOR_A_LONG_TIME.name
                AbnormalDepositApplicationChangelog.add(
                    application_id=row.id,
                    user_id=row.user_id,
                    admin_user_id=None,
                    change_type=AbnormalDepositApplicationChangelog.ChangeType.STATUS.name,
                    old_value="待补充资料",
                    new_value=new_status.name,
                )
                db.session.commit()
                notice_abnormal_deposit_application_rejected(row)
                apply_id_data_map.pop(key_)

    cache.save(apply_id_data_map)


@scheduled(crontab(minute=25, hour="*/1"))
@lock_call()
def abnormal_deposit_application_audit_notice_schedule():
    """ 审核中、资料审核中、已初审待复审 的资产自助找回申请 微信通知 """
    if not config.get('MONITOR_ENABLED'):
        return
    to_audit_count = (
        AbnormalDepositApplication.query.filter(
            AbnormalDepositApplication.status.in_(
                [
                    AbnormalDepositApplication.Status.AUDIT_REQUIRED,
                    AbnormalDepositApplication.Status.CHECK_REQUIRED,
                    AbnormalDepositApplication.Status.DOUBLE_CHECK_REQUIRED,
                ]
            ),
            AbnormalDepositApplication.type != AbnormalDepositApplication.Type.UNKNOWN,
        )
        .with_entities(func.count("*"))
        .scalar()
        or 0
    )

    cache = AbnormalDepositApplicationNoticeCache()
    cache_val = cache.read()
    old_count: int = int(cache_val) if cache_val else 0
    if to_audit_count - old_count >= 100:
        send_alert_notice(
            f"当前存在{to_audit_count}个待审核的资产自助找回申请，请尽快审核",
            config["ADMIN_CONTACTS"]["customer_service"],
        )
        cache_count = to_audit_count
    else:
        cache_count = min(to_audit_count, old_count)
    cache.set(str(cache_count))
