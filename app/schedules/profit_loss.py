# -*- coding: utf-8 -*-
from datetime import timedelta
from decimal import Decimal
import json
from celery.schedules import crontab

from app.business.external_dbs import TradeHistoryDB
from app.utils.date_ import today

from ..business import lock_call
from ..business.account_pl import (
    RealtimeAccountProfitLossProcessor, TOTAL_ACCOUNT, RealtimeAssetProfitLossProcessor, 
    get_deal_realtime_data, sync_user_asset_cost_history, sync_user_asset_slice_profit_loss_snapshot, 
    sync_user_slice_account_deal_value_sum,
    sync_user_slice_profit_loss_snapshot,
)
from ..caches.profit_loss import BalanceH<PERSON>oryDailyIdCache, UserAccountPLDataCache, UserAccountDealCache, UserAssetPLDataCache
from ..common import CeleryQueues, AccountBalanceType
from ..exceptions import InvalidArgument
from ..utils import route_module_to_celery_queue, celery_task, current_timestamp, scheduled

route_module_to_celery_queue(__name__, CeleryQueues.PROFIT_LOSS)


@scheduled(crontab(minute="25,50", hour='0-2'))
@lock_call(ttl=3600*2)
def sync_user_slice_profit_loss_snapshot_schedule():
    ts = current_timestamp(to_int=True)
    today_ts = ts - ts % 86400
    start_ts = today_ts - 7 * 86400
    while start_ts <= today_ts:
        sync_user_slice_profit_loss_snapshot(start_ts)
        start_ts += 86400


@scheduled(crontab(minute="25,50", hour='0-2'))
@lock_call(ttl=3600*2)
def sync_user_slice_account_deal_value_sum_schedule():
    ts = current_timestamp(to_int=True)
    today_ts = ts - ts % 86400
    start_ts = today_ts - 7 * 86400
    while start_ts <= today_ts:
        sync_user_slice_account_deal_value_sum(start_ts)
        start_ts += 86400


@scheduled(crontab(minute="20,40", hour='0-2'))
@lock_call(ttl=3600*2)
def sync_user_asset_slice_profit_loss_snapshot_schedule():
    ts = current_timestamp(to_int=True)
    today_ts = ts - ts % 86400
    start_ts = today_ts - 7 * 86400
    while start_ts <= today_ts:
        sync_user_asset_slice_profit_loss_snapshot(start_ts)
        start_ts += 86400


@scheduled(crontab(minute="20,40", hour='0-2'))
@lock_call()
def sync_user_asset_cost_history_schedule():
    report_date = today() - timedelta(days=1)
    sync_user_asset_cost_history(report_date)


@celery_task
@lock_call(with_args=True)
def update_user_realtime_pl_task(user_id: int, account_type_str: str):
    cache = UserAccountPLDataCache(user_id, account_type_str)
    ts = current_timestamp(to_int=True)
    update_ts = ts - ts % 86400
    processor_cls = RealtimeAccountProfitLossProcessor
    if not processor_cls.check_transfer_data_ready(user_id, account_type_str, update_ts):
        db_idx, table_idx = TradeHistoryDB.user_hash(user_id)
        update_spot_balance_history_id_task.delay(db_idx, table_idx, update_ts)
        return
    if account_type_str in AccountBalanceType.__members__.keys():
        result = processor_cls.get_realtime_user_account_type_data(
            user_id, AccountBalanceType[account_type_str])
    elif account_type_str == TOTAL_ACCOUNT:
        result = processor_cls.get_realtime_user_account_type_data(
            user_id, account_type_str)
    else:
        raise InvalidArgument
    result = {key: str(v) for key, v in result.items()}
    result.update(dict(ts=ts))
    cache.save(result)
    # 缓存当日有效
    today_ts = ts - ts % 86400 + 86400
    ttl = today_ts + 86400 - ts
    cache.expire(ttl)


@celery_task
@lock_call(with_args=True)
def update_user_realtime_deal_value_task(user_id: int, account_type_str: str):
    cache = UserAccountDealCache(user_id, account_type_str)
    
    history_ts, history_deal_usd = None, Decimal()
    ts = current_timestamp(to_int=True)
    today_ts = ts - ts % 86400 + 86400
    if history_result:= cache.read():
        history_result = json.loads(history_result)
        cache_ts = int(history_result['ts'])
        if cache_ts >= today_ts:
            history_ts = cache_ts
            history_deal_usd = Decimal(history_result.get('deal_usd', '0'))
    result = get_deal_realtime_data(user_id, account_type_str, ts=history_ts)
    result['deal_usd'] += history_deal_usd
    result = {key: str(v) for key, v in result.items()}
    result['ts'] = ts
    cache.save(json.dumps(result))
    # 缓存当日有效
    ttl = today_ts + 86400 - ts
    cache.expire(ttl)


@celery_task
@lock_call(with_args=['db_idx', 'table_idx'])
def update_spot_balance_history_id_task(db_idx: int, table_idx: int, ts: int):
    ts -= ts % 86400
    _db, table_name = TradeHistoryDB.DBS[db_idx], f'balance_history_{table_idx}'
    flag = False
    last_id = 0
    fields = ['id', 'time']
    table = _db.table(table_name)
    target_id = 0
    while not flag:
        if last_id:
            where_ = f'id < {last_id}'
        else:
            where_ = None
        records = table.select(
                    *fields,
                    where=where_,
                    limit=50000,
                    force_index='PRI',
                    order_by='id DESC'
                )
        if not records:
            break
        last_id = records[-1][0]
        for r in records:
            id_, time = r
            if time < ts:
                target_id = id_
                flag = True
                break

    assert target_id != 0
    cache = BalanceHistoryDailyIdCache(db_index=db_idx, table_index=table_idx, ts=ts)
    cache.save(str(target_id))
    cache.expire(cache.ttl)


@celery_task
@lock_call(with_args=True)
def update_user_asset_profit_loss_task(user_id: int, asset: str):
    cache = UserAssetPLDataCache(user_id, asset, AccountBalanceType.SPOT.name)
    ts = current_timestamp(to_int=True)
    update_ts = ts - ts % 86400
    if not RealtimeAccountProfitLossProcessor.check_transfer_data_ready(user_id, asset, update_ts):
        db_idx, table_idx = TradeHistoryDB.user_hash(user_id)
        update_spot_balance_history_id_task.delay(db_idx, table_idx, update_ts)
        return
    result = RealtimeAssetProfitLossProcessor.get_realtime_user_account_type_data(user_id, asset, AccountBalanceType.SPOT)
    result = {key: str(v) for key, v in result.items()}
    result.update(dict(ts=ts))
    cache.save(result)
    # 缓存当日有效
    today_ts = ts - ts % 86400 + 86400
    ttl = today_ts + 86400 - ts
    cache.expire(ttl)