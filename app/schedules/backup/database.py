from celery.schedules import crontab

from app.business import CeleryQueues, lock_call, route_module_to_celery_queue
from app.models import (AppraisalHistory, EmailPushUnsubscription,
                        InvestmentTransferHistory, LoginHistory,
                        MarginInsuranceHistory, MarginRealInsuranceHistory,
                        MarginReceivableInterestHistory,
                        PerpetualBalanceTransfer, LendableAssetChangeHistory,
                        KlineAnalysis, KlineAnalysisContent, KlineAnalysisBatch,
                        UserLiquiditySlice, Message, AccountTransferLog, MarketPriceNotice
                        )
from app.models.asset_cost import AssetProfitLossHistory
from app.models.pledge import PledgeInterestHistory, PledgeLoanOrderInterestHistory
from app.models.spot import SystemAssetLiability
from app.models.activity import LaunchMiningUserStakeSnapshot
from app.models.app import IOSSupportInformation
from app.models.system import RealTimeIncomeHistory
from app.utils import scheduled
from app.models.investment import (
    UserDayInterestDetail,
    UserDayInterestHistory,
    UserHourInterestDetail,
    UserHourInterestHistory,
)

from .base import backup_data_history
from flask import current_app
from app.config import config

route_module_to_celery_queue(__name__, CeleryQueues.DAILY)

tables = [
    (MarginInsuranceHistory, 3),
    (MarginReceivableInterestHistory, 3),
    (MarginRealInsuranceHistory, 3),
    (AppraisalHistory, 3),
    (EmailPushUnsubscription, 3),
    # 理财相关表
    (InvestmentTransferHistory, 24),
    (UserHourInterestHistory, 12),
    (UserHourInterestDetail, 12),
    (UserDayInterestHistory, 12),
    (UserDayInterestDetail, 12),
    (LoginHistory, 24),
    (PerpetualBalanceTransfer, 12),
    (LendableAssetChangeHistory, 3),
    (KlineAnalysisContent, 3),
    (KlineAnalysis, 3),
    (KlineAnalysisBatch, 3),
    (PledgeInterestHistory, 12),
    (PledgeLoanOrderInterestHistory, 12),
    (SystemAssetLiability, 24),
    (UserLiquiditySlice, 12),
    (Message, 3),
    (AccountTransferLog, 12),
    (LaunchMiningUserStakeSnapshot, 3),
    (MarketPriceNotice, 6),
    (RealTimeIncomeHistory, 6),
    (IOSSupportInformation, 6),
    (AssetProfitLossHistory, 12),
]


@scheduled(crontab(minute=20, hour=4, day_of_month=1))
@lock_call(with_args=False, wait=True)
def backup_table_history_schedule(index=0):
    if not config.get('BACKUP_ENABLED'):
        return
    if not 0 <= index < len(tables):
        return
    table, month = tables[index]
    try:
        backup_data_history(table, month=month)
    except Exception as e:
        current_app.logger.error(f"backup table {table.__table__.name} history error:", e)

    backup_table_history_schedule.delay(index + 1)
