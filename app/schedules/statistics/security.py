# -*- coding: utf-8 -*-
from collections import defaultdict
import json
from celery.schedules import crontab

from app.business.clients.biz_monitor import BizMonitorClient, biz_monitor
from app.business.lock import lock_call
from app.utils import celery_task, now
from app.models import db, UserWebAuthn
from app.business.security import SECURITY_SETTING_TYPE_EVENT_MAP, SECURITY_GAUGE_EVENT_MAP
from app.business.user import UserPreferences
from app.caches import func
from app.caches.security import SecurityStatisticsCache
from app.common import ProductEvent, EventDataType
from app.common.constants import CeleryQueues
from app.models.user import ThirdPartyAccount, User, UserPreference
from app.models.authority import AdminUserWebAuthn
from app.models.wallet import WithdrawalApprover
from app.utils import (
    route_module_to_celery_queue, scheduled,
)
from app.utils.date_ import current_timestamp

route_module_to_celery_queue(__name__, CeleryQueues.STATISTIC)


@scheduled(crontab(minute='31', hour='2'))
@lock_call()
def update_security_tool_statistics_to_cache_schedule():
    """将事件结果存入缓存中"""
    client = BizMonitorClient()
    security_gauge_count_map = get_security_gauge_count_map(client)
    res = defaultdict(list)

    for type_, business_event_mapping in SECURITY_SETTING_TYPE_EVENT_MAP.items():
        for business, event_dic in business_event_mapping.items():
            count_event = event_dic.get('count')
            count_dic = get_event_data(client, count_event, EventDataType.COUNTER)
            user_count_event = event_dic.get('user_count')
            user_count_dic = get_event_data(client, user_count_event, EventDataType.UNIQ_COUNTER)

            for range_ in SecurityStatisticsCache.TimeRange:
                count = count_dic.get(range_.value, '/')
                user_count = user_count_dic.get(range_.value, '/')
                total_user_count = security_gauge_count_map.get(business, '/')
                item = dict(
                    business=business.name,
                    type=type_,
                    count=count,
                    user_count=user_count,
                    total_user_count=total_user_count,
                )
                res[range_].append(item)
    for range_, data in res.items():
        item = dict(data=data,report_ts=current_timestamp(to_int=True))
        SecurityStatisticsCache(range_).save(json.dumps(item))


def get_event_data(client, event, metric_type):
    if not event:
        return defaultdict(int)
    ret = client.get_rw_metric_data(
        metric_id=event.value,
        metric_type=metric_type.value,
        label_ids=[0]
    )
    if not ret:
        return defaultdict(int)
    return dict(ret.get(0, []))


def get_security_gauge_count_map(client):
    res = dict()
    end_time = current_timestamp(to_int=True)
    start_time = end_time - 86400
    for business, event in SECURITY_GAUGE_EVENT_MAP.items():
        ret = client.get_metric_tag_data(
            start_time=start_time,
            end_time=end_time,
            metric_id=event.value,
            metric_type=EventDataType.GAUGE,
            period='day',
        )
        if not ret:
            res[business] = 0
        else:
            ret.sort(key=lambda x: x[0], reverse=True)
            res[business] = ret[0][1]
    return res


@scheduled(crontab(minute='12', hour='2'))
@lock_call()
def update_security_tool_gauge_events():
    """将事件瞬时值上传"""
    webauthn_count = UserWebAuthn.query.filter(
        UserWebAuthn.status == UserWebAuthn.Status.VALID,
    ).with_entities(func.count(UserWebAuthn.id)).scalar() or 0
    biz_monitor.increase_guage(
        ProductEvent.WEB_AUTHN_ENABLE_COUNT,
        value=webauthn_count,
    )

    totp_count = User.query.filter(
        User.totp_auth_key != '',
    ).with_entities(func.count(User.id)).scalar() or 0
    biz_monitor.increase_guage(
        ProductEvent.TOTP_ENABLE_COUNT,
        value=totp_count,
    )

    mobile_count = User.query.filter(
        User.mobile_num.isnot(None),
    ).with_entities(func.count(User.id)).scalar() or 0
    biz_monitor.increase_guage(
        ProductEvent.MOBILE_ENABLE_COUNT,
        value=mobile_count,
    )

    withdraw_password_count = UserPreference.query.filter(
        UserPreference.key == UserPreferences.opening_withdraw_password.name,
        UserPreference.value == '1',
    ).with_entities(func.count(UserPreference.id)).scalar() or 0
    biz_monitor.increase_guage(
        ProductEvent.WITHDRAWAL_PASSWORD_ENABLE_COUNT,
        value=withdraw_password_count,
    )

    phishing_code_count = UserPreference.query.filter(
        UserPreference.key == UserPreferences.anti_phishing_code.name,
        UserPreference.value != '',
    ).with_entities(func.count(UserPreference.id)).scalar() or 0
    biz_monitor.increase_guage(
        ProductEvent.PHISHING_CODE_ENABLE_COUNT,
        value=phishing_code_count,
    )

    withdrawal_approver_count = WithdrawalApprover.query.filter(
        WithdrawalApprover.status == WithdrawalApprover.Status.VALID,
        WithdrawalApprover.is_self.isnot(True)
    ).with_entities(func.count(WithdrawalApprover.user_id.distinct())).scalar() or 0
    biz_monitor.increase_guage(
        ProductEvent.WITHDRAWAL_APPROVER_ENABLE_COUNT,
        value=withdrawal_approver_count,
    )

    third_party_account_count = ThirdPartyAccount.query.filter(
        ThirdPartyAccount.status == ThirdPartyAccount.Status.VALID,
    ).with_entities(func.count(ThirdPartyAccount.user_id.distinct())).scalar() or 0
    biz_monitor.increase_guage(
        ProductEvent.THIRD_PARTY_ACCOUNT_ENABLE_COUNT,
        value=third_party_account_count,
    )

    login_ip_locking_count = UserPreference.query.filter(
        UserPreference.key == UserPreferences.opening_web_login_ip_locking.name,
        UserPreference.value == '1',
    ).with_entities(func.count(UserPreference.id)).scalar() or 0
    biz_monitor.increase_guage(
        ProductEvent.LOGIN_IP_LOCK_ENABLE_COUNT,
        value=login_ip_locking_count,
    )

    security_login_count = UserPreference.query.filter(
        UserPreference.key == UserPreferences.opening_web_security_login_duration.name,
        UserPreference.value == '1',
    ).with_entities(func.count(UserPreference.id)).scalar() or 0
    biz_monitor.increase_guage(
        ProductEvent.SECURITY_LOGIN_ENABLE_COUNT,
        value=security_login_count,
    )


@celery_task
def update_webauthn_auth_record(auth_id: int, new_sign_count: int):
    """
    :param auth_id: UserWebAuthn 记录的 ID
    :param new_sign_count: 新的签名计数
    """
    auth = UserWebAuthn.query.get(auth_id)
    if not auth:
        return
    
    auth.sign_count = new_sign_count
    auth.last_used_at = now()
    db.session.commit()


@celery_task
def update_admin_webauthn_auth_record(admin_auth_id: int, new_sign_count: int):
    """
    :param admin_auth_id: AdminUserWebAuthn 记录的 ID
    :param new_sign_count: 新的签名计数
    """
    auth = AdminUserWebAuthn.query.get(admin_auth_id)
    if not auth:
        return
    
    auth.sign_count = new_sign_count
    auth.last_used_at = now()
    db.session.commit()
