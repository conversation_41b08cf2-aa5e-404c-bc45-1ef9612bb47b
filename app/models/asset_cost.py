# -*- coding: utf-8 -*-
from __future__ import annotations
from enum import Enum

from app.common.constants import AccountBalanceType

from .base import ModelBase, db

class AssetCost(ModelBase):
    """
    用户持仓成本记录
    """

    __table_args__ = (
        db.UniqueConstraint('user_id', 'account_type', 'asset', name="user_id_account_type_asset_uniq"),
    )

    user_id = db.Column(db.Integer, nullable=False)  # no foreign key here
    account_type = db.Column(db.StringEnum(AccountBalanceType), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    price = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0) # 单位USD

    # 持仓量，基于此持仓量更新均价
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  


class AssetCostUpdateHistory(ModelBase):
    """
    用户手动修改持仓成本记录(目前仅支持修改现货)
    """

    __table_args__ = (
        db.Index("idx_user_id_asset", "user_id", "asset"),
    )
    
    user_id = db.Column(db.Integer, nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    price = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0) # 单位USD


class AssetProfitLossHistory(ModelBase):
    
    """
    币种持仓盈亏曲线
    """
    __table_args__ = (
        db.Index("idx_user_id_report_date_asset", "user_id", "report_date", "asset"),
    )
    
    class Type(Enum):
        SYSTEM = "system" # 系统创建
        USER = "user" # 由用户创建或修改

    id = db.Column(db.BigInteger, primary_key=True)
    asset = db.Column(db.String(32), nullable=False)
    user_id = db.Column(db.Integer, nullable=False)
    report_date = db.Column(db.Date, nullable=False, index=True)
    profit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False) # usd
    price = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False) # usd
    amount = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False) # 执行快照时的资产数量
    type = db.Column(db.StringEnum(Type), nullable=False) # type用于前端展示，如果type=USER，需要标识出来