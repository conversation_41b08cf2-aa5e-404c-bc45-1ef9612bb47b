# -*- coding: utf-8 -*-
import datetime
from enum import Enum
import time
from decimal import Decimal

from flask import g
from webargs import fields as wa_fields

from app.api.common.request import get_request_platform
from app.business.balance.series import get_user_spot_balance_series
from app.business.clients.server import Server<PERSON>lient
from app.business.prices import PriceManager
from app.common.constants import PrecisionEnum
from app.models.asset_cost import AssetCost, AssetProfitLossHistory
from app.utils.amount import quantize_amount

from ..common import (Namespace, Resource, respond_with_code, require_login)
from ..common.fields import AssetField, TimestampField, EnumField
from ...business import (
    UserPreferences, cached,
)
from ...business.account_pl import (
    format_deal_snapshot_data, get_account_deal_value_cache_data, get_asset_pl_cache_data, get_deal_snapshot_data, \
        get_snapshot_data_new, get_snapshot_sum_profit_data, TOTAL_ACCOUNT, get_snapshot_data,
    format_profit_snapshot_data, check_today_data_ready, RealtimeAccountProfitLossProcessor, \
        get_account_pl_cache_data, get_asset_pl_snapshot_data
)
from ...business.profit_loss import MarketProfitLossAnalyzer, UserDealAnalyzer
from ...caches import MarginAccountNameCache
from ...caches.profit_loss import UserAccountPLDataCache, UserAssetPLDataCache
from ...common import AccountBalanceType
from ...exceptions import InvalidArgument, ConfirmationRequired, DataNotReady
from ...models import db
from ...utils import today, current_timestamp

url_prefix = '/profit-loss'

ns = Namespace('Profit-Loss')


@ns.route('/market')
@respond_with_code
class MarketDataResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
       dict(
           market=wa_fields.String(validate=lambda x: x.isupper(), required=True),
           margin=wa_fields.Bool(default=False, missing=False)
       )
    )
    def get(cls, **kwargs):
        user_id = g.user.id
        db.session.close()
        market, margin = kwargs['market'], kwargs['margin']
        if margin:
            if market not in MarginAccountNameCache.list_online_markets().values():
                raise InvalidArgument
        p = MarketProfitLossAnalyzer(user_id, market, margin)
        return p.analyze()


@ns.route('/deal')
@respond_with_code
class DealDataResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            market=wa_fields.String(validate=lambda x: x.isupper(), required=True),
            start_time=TimestampField(required=True),
            end_time=TimestampField(required=True),
        )
    )
    def get(cls, **kwargs):
        user_id = g.user.id
        db.session.close()
        market = kwargs['market']
        start, end = int(kwargs['start_time'].timestamp()), int(kwargs['end_time'].timestamp())
        p = UserDealAnalyzer(user_id, market, start, end)
        return p.analyze()


@ns.route('/account')
@respond_with_code
class AccountProfitLossResource(Resource):


    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            account_type=EnumField(AccountBalanceType, enum_by_value=False),
            start_time=TimestampField(required=True, to_date=True),
            end_time=TimestampField(required=True, to_date=True),
        )
    )
    def get(cls, **kwargs):
        user_id = g.user.id
        pref = UserPreferences(user_id)
        if not pref.opening_account_profit_loss:
            raise ConfirmationRequired
        today_date = datetime.datetime.utcnow().date()
        start_date = kwargs["start_time"]
        end_date = kwargs["end_time"]
        account_type = TOTAL_ACCOUNT if not kwargs.get("account_type") else kwargs["account_type"]
        account_type_str = account_type.name if isinstance(account_type, AccountBalanceType) \
            else account_type
        if account_type_str == AccountBalanceType.PLEDGE.name:
            raise InvalidArgument
        if start_date > end_date:
            raise InvalidArgument
        if end_date > today_date:
            end_date = today_date
        if end_date == today_date:
            include_realtime_data = True
        else:
            include_realtime_data = False
        if include_realtime_data:
            if not check_today_data_ready("account"):
                end_date = today_date - datetime.timedelta(days=1)
                include_realtime_data = False
                if start_date > end_date:
                    raise DataNotReady
        platform = get_request_platform()
        # TODO: app支持质押理财后，改为按版本判断
        if platform.is_web():
            snapshot_func = get_snapshot_data_new
        else:
            snapshot_func = get_snapshot_data
        snap_result = format_profit_snapshot_data(
            start_date,
            snapshot_func(user_id, start_date, end_date, account_type))
        if include_realtime_data:
            realtime_data = {
                "balance_usd": snap_result[-1]["balance_usd"] if len(snap_result) > 0 else Decimal(),
                "profit_usd": Decimal(),
                "profit_rate": Decimal(),
                "total_profit_usd": snap_result[-1]["total_profit_usd"] if len(snap_result) > 0
                else Decimal(),
                "report_date": today()
            }
            cache_data = get_account_pl_cache_data(user_id, account_type_str)
            realtime_data.update(cache_data)
            if "profit_usd" in cache_data:
                realtime_data["total_profit_usd"] += Decimal(cache_data["profit_usd"])
            snap_result.append(realtime_data)
        return snap_result


@ns.route('/account/realtime')
@respond_with_code
class AccountRealtimeProfitLossResource(Resource):

    @classmethod
    @cached(5*60)
    def get_user_realtime_usd(cls, user_id: int, account_type_str: str):
        return RealtimeAccountProfitLossProcessor.get_real_time_account_type_usd(
            user_id,
            account_type_str)

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            account_type=EnumField(AccountBalanceType, enum_by_value=False),
        )
    )
    def get(cls, **kwargs):
        user_id = g.user.id
        pref = UserPreferences(user_id)
        if not pref.opening_account_profit_loss:
            raise InvalidArgument
        account_type = TOTAL_ACCOUNT if not kwargs.get("account_type") else kwargs["account_type"]
        account_type_str = account_type.name if isinstance(account_type, AccountBalanceType) \
            else account_type
        if account_type_str == AccountBalanceType.PLEDGE.name:
            raise InvalidArgument
        
        has_balance_history = bool(ServerClient().get_user_balance_history(user_id, page=1, limit=1))

        if not check_today_data_ready("account"):
            raise DataNotReady(
                data=dict(
                    balance_usd=cls.get_user_realtime_usd(user_id, account_type_str),
                    has_balance_history=has_balance_history
                )
            )
        db.session.close()
        max_wait = 2
        t0 = current_timestamp()
        cache_data = get_account_pl_cache_data(user_id, account_type_str)
        while not {"profit_rate", "profit_usd"} & set(cache_data):
            t1 = current_timestamp()
            if t1 - t0 >= max_wait:
                break
            time.sleep(0.2)
            cache_data = UserAccountPLDataCache(user_id, account_type_str).hgetall()
        if not cache_data:
            raise DataNotReady(
                data=dict(
                    balance_usd=cls.get_user_realtime_usd(user_id, account_type_str),
                    has_balance_history=has_balance_history
                )
            )

        realtime_data = {
            "balance_usd": Decimal(),
            # "profit_usd": Decimal(),
            # "profit_rate": Decimal(),
        }
        # noinspection PyTypeChecker
        realtime_data.update(cache_data)
        realtime_data['has_balance_history'] = has_balance_history
        return realtime_data


@ns.route('/account/summary')
@respond_with_code
class AccountSummaryResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            start_time=TimestampField(required=True, to_date=True),
            end_time=TimestampField(to_date=True, required=True)
        )
    )
    def get(cls, **kwargs):
        user_id = g.user.id
        pref = UserPreferences(user_id)
        if not pref.opening_account_profit_loss:
            raise ConfirmationRequired
        today_date = datetime.datetime.utcnow().date()
        end_date = kwargs["end_time"]
        start_date = kwargs["start_time"]
        if start_date > end_date:
            raise InvalidArgument
        if end_date > today_date:
            end_date = today_date
        if end_date == today_date:
            include_realtime_data = True
        else:
            include_realtime_data = False
        if not check_today_data_ready("account"):
            end_date = today_date - datetime.timedelta(days=1)
            include_realtime_data = False
            if end_date < start_date:
                raise DataNotReady

        show_account_types = [i for i in AccountBalanceType if i not in (AccountBalanceType.PLEDGE,
                                                                         AccountBalanceType.STAKING)]
        snap_result = {
            v.name: Decimal()
            for v in show_account_types
        }
        snap_result.update(get_snapshot_sum_profit_data(user_id, start_date, end_date))

        if (staking_type:= AccountBalanceType.STAKING.name) in snap_result:
            snap_result[AccountBalanceType.INVESTMENT.name] += snap_result[staking_type]
            del snap_result[staking_type]
        if include_realtime_data:
            for account_type in show_account_types:
                cache_data = get_account_pl_cache_data(user_id, account_type.name)
                profit_usd = cache_data.get("profit_usd")
                snap_result[account_type.name] += Decimal(profit_usd) if profit_usd else Decimal()
        return snap_result


@ns.route('/account/asset/percent')
@respond_with_code
class AccountAssetPercentResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            account_type=EnumField(AccountBalanceType, enum_by_value=False),
        )
    )
    def get(cls, **kwargs):
        user_id = g.user.id
        pref = UserPreferences(user_id)
        if not pref.opening_account_profit_loss:
            raise InvalidArgument
        account_type = TOTAL_ACCOUNT if not kwargs.get("account_type") else kwargs["account_type"]
        if account_type == AccountBalanceType.PLEDGE:
            raise InvalidArgument

        return RealtimeAccountProfitLossProcessor.get_user_asset_percent_data(user_id, account_type)


@ns.route('/account/deal')
@respond_with_code
class AccountDealValueResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            account_type=EnumField(AccountBalanceType, enum_by_value=False),
            start_time=TimestampField(required=True, to_date=True),
            end_time=TimestampField(required=True, to_date=True),
        )
    )
    def get(cls, **kwargs):
        """
        盈亏分析-交易额曲线
        """
        start_date = kwargs["start_time"]
        end_date = kwargs["end_time"]
        account_type = kwargs.get("account_type")

        if account_type and account_type not in (
            AccountBalanceType.SPOT,
            AccountBalanceType.MARGIN,
            AccountBalanceType.PERPETUAL,
        ):
            raise InvalidArgument
        user_id = g.user.id
        pref = UserPreferences(user_id)
        if not pref.opening_account_profit_loss:
            raise ConfirmationRequired

        result = get_deal_snapshot_data(user_id, start_date, end_date, account_type)
        format_result = format_deal_snapshot_data(start_date, end_date, result)
        
        today_ = today()
        if end_date == today_:
            include_realtime_data = True
        else:
            include_realtime_data = False
        if not check_today_data_ready("account"):
            end_date = today_ - datetime.timedelta(days=1)
            include_realtime_data = False
            if end_date < start_date:
                raise DataNotReady
        
        if include_realtime_data:
            if account_type:
                account_type_str = account_type.name
            else:
                account_type_str = TOTAL_ACCOUNT
            cache_data = get_account_deal_value_cache_data(user_id, account_type_str)
            if cache_data:
                cache_data["report_date"] = today_
                format_result.append(cache_data)
        return dict(
            data=format_result,
        )



@ns.route('/asset')
@respond_with_code
class AssetProfitLossResource(Resource):

    class SeriesType(Enum):
        ASSET_PROFIT = 'asset_profit'
        DAILY_PROFIT = 'daily_profit'
        BALANCE = 'balance'

    @classmethod
    def get_asset_profit_loss_history_list(cls, user_id: int, 
                                           asset: str, 
                                           start_dt: datetime.date, end_dt: datetime.date, realtime_profit: Decimal):
        records = AssetProfitLossHistory.query.filter(AssetProfitLossHistory.user_id == user_id, 
                                            AssetProfitLossHistory.asset == asset,
                                            AssetProfitLossHistory.report_date >= start_dt,
                                            AssetProfitLossHistory.report_date <= end_dt,
                                            ).with_entities(
                                                AssetProfitLossHistory.report_date,
                                                AssetProfitLossHistory.profit,
                                                AssetProfitLossHistory.type,
                                            ).all()
        profit_map = {r.report_date: (r.profit, r.type) for r in records}
        curr_dt = start_dt
        res = []
        while curr_dt <= end_dt:
            profit, type_ = profit_map.get(curr_dt, (Decimal(), AssetProfitLossHistory.Type.SYSTEM))
            res.append((
                curr_dt,
                profit, # 缺数据补0
                type_.name
            ))
            curr_dt += datetime.timedelta(days=1)
        today_ = today()
        if end_dt == today_:
            type_ = AssetProfitLossHistory.Type.SYSTEM
            if info:= profit_map.get(end_dt):
                type_ = info[1]
            res.pop()
            res.append(
                (
                    today_,
                    realtime_profit,
                    type_.name
                )
            )
        return res


    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            asset=AssetField(required=True),
            start_time=TimestampField(required=True, to_date=True),
            end_time=TimestampField(required=True, to_date=True),
            series_type=EnumField(SeriesType, required=True),
        )
    )
    def get(cls, **kwargs):
        """
        盈亏分析-币种维度盈亏(现货)
        """
        start_dt = kwargs["start_time"]
        end_dt = kwargs["end_time"]
        if end_dt < start_dt or (end_dt - start_dt).days > 365:
            raise InvalidArgument
        user_id = g.user.id
        asset = kwargs["asset"]
        series_type = kwargs["series_type"]

        cost_price = AssetCost.query.filter(
            AssetCost.user_id == user_id,
            AssetCost.asset == asset,
            AssetCost.account_type == AccountBalanceType.SPOT
        ).with_entities(AssetCost.price).scalar() or Decimal()

        price = PriceManager.asset_to_usd(asset)
        balance = ServerClient().get_user_balances(user_id, asset)
        amount = balance.get(asset, {}).get("available", 0) + balance.get(asset, {}).get("frozen", 0)
        asset_profit_loss = quantize_amount((price - cost_price) * amount, PrecisionEnum.CASH_PLACES)
        if series_type == cls.SeriesType.ASSET_PROFIT:
            series = cls.get_asset_profit_loss_history_list(user_id, asset, start_dt, end_dt, asset_profit_loss)
        elif series_type == cls.SeriesType.DAILY_PROFIT:
            series = get_asset_pl_snapshot_data(user_id, start_dt, end_dt, asset)
            series = [(v["report_date"], v["profit_usd"]) for v in series]
        elif series_type == cls.SeriesType.BALANCE:
            series = get_user_spot_balance_series(user_id, asset, start_dt, end_dt)
        else:
            raise InvalidArgument
        asset_profit_rate = Decimal()
        if cost_price > Decimal():
            asset_profit_rate = quantize_amount((price - cost_price) / cost_price, PrecisionEnum.RATE_PLACES)
        
        db.session.close()
        max_wait = 2
        t0 = current_timestamp()
        if not check_today_data_ready("asset"):
            daily_data = {}
        else:
            daily_data = get_asset_pl_cache_data(user_id, asset)
            while not {"profit_rate", "profit_usd"} & set(daily_data):
                t1 = current_timestamp()
                if t1 - t0 >= max_wait:
                    break
                time.sleep(0.2)
                daily_data = UserAssetPLDataCache(user_id, asset, AccountBalanceType.SPOT.name).hgetall()
            
            if daily_data and series_type == cls.SeriesType.DAILY_PROFIT:
                today_ = today()
                if end_dt == today_:
                    if daily_profit:= daily_data.get("profit_usd"):
                        series.append(
                            (today_, daily_profit)
                        )
        return dict(
            cost_price=cost_price,
            price=price,
            asset_profit_loss=asset_profit_loss,
            asset_profit_rate=asset_profit_rate,
            daily_profit_loss=daily_data.get('profit_usd', Decimal()),
            daily_profit_rate=daily_data.get('profit_rate', Decimal()),
            series=series,
        )

