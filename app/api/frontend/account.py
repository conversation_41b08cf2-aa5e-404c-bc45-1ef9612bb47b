# -*- coding: utf-8 -*-
import datetime
import json
import math
from collections import defaultdict
from datetime import timedelta
from dateutil.tz import UTC
from dateutil.relativedelta import relativedelta
from decimal import Decimal
from functools import partial
from itertools import chain as chain_iter
from typing import Iterable

from flask import g
from flask_babel import gettext
from sqlalchemy import func, or_, and_
from webargs import fields

from app.common.constants import PrecisionEnum
from app.models.asset_cost import AssetCost, AssetCostUpdateHistory, AssetProfitLossHistory
from app.models.staking import StakingAccount
from ..common import (
    Namespace, Resource, respond_with_code, limit_user_frequency,
    require_login, get_request_platform,
)
from ..common.decorators import trade_permission_validate
from ..common.fields import (
    AssetField, EnumField, PositiveDecimalField, TimestampField, PageField,
    LimitField,
)
from ..common.request import is_old_app_request
from ...assets import (
    list_all_assets, has_asset,
    normalise_asset_code, get_asset_config, AssetUtils,
)
from ...business import (
    <PERSON><PERSON>etting<PERSON>, PriceManager, <PERSON>ache<PERSON>ock, Lock<PERSON>eys,
    ServerClient, UserPreferences, cached,
    SPOT_ACCOUNT_ID, MAX_ORDER_ACCOUNT_ID, BusinessSettings, ReferralBusiness, send_notice_email,
)
from ...business.account_pl import RealtimeAccountProfitLossProcessor, TOTAL_ACCOUNT
from ...business.balance import (
    get_suggest_user_balance_series_points,
    get_user_spot_balance_series, get_user_margin_balance_series,
    get_user_investment_balance_series, get_user_perpetual_balance_series,
    get_user_balance_sum_series, get_user_spot_balance_sum_series,
    get_user_amm_balance_series, get_user_amm_balance_sum_series,
    MerkleBusiness,
)
from ...business.export.tax_data import export_tax_data
from ...business.fee import FeeFetcher, UserFeeParser
from ...business.fee_constant import ALL_MARKETS
from ...business.export.account_statement import export_account_statement_data
from ...business.margin.helper import MarginHelper
from ...business.p2p.order_factor import get_account_p2p_lock_map
from ...business.pledge.helper import is_pledge_account, get_pledge_loan_asset_account_id_dict
from ...business.push import get_user_web_lang
from ...business.share_window import SharePopWindowRepository
from ...business.sub_account import SubAccountManager, get_sub_account_user_ids, has_sub_account, get_sub_account_balance_map
from ...caches import AmmMarketCache
from ...caches.spot import MarketCache
from ...caches.user import UserBalanceSumCache, UserAssetConversionCache
from ...common import (
    BalanceBusiness,
    ASSET_CONVERSION_FEE_RATE, Language, AccountBalanceType,
    get_balance_business_display, TradeType, TradeBusinessType,
)
from ...config import config
from ...exceptions import (
    InvalidArgument,
    AssetNotFound,
    BeyondMaxExportTimesException, VersionNeedUpgrade, FrequencyExceeded,
)
from ...models import (
    User, AssetConversion, AssetInvestmentConfig, OperationLog, CoinInformation, AccountTransferLog,
    db, TaxExportHistory, UserBalanceMerkleInfo, ReferralAssetHistory,
    UserShareWindowRecord, UserBalanceMerkleTree, AccountStatementExportHistory
)
from ...models.wallet import AssetConversionHistory
from ...utils import (
    quantize_amount, now, amount_to_str,
    export_xlsx, datetime_to_str, AWSBucketPublic,
    timestamp_to_datetime, today, AWSBucketPrivate,
)
from ...utils.parser import JsonEncoder

ns = Namespace('Account')


def _asset_conversion_to_dict(conversion: AssetConversion) -> dict:
    return dict(
        conversion_history_id=conversion.id,
        update_time=conversion.updated_at,
        asset=conversion.asset,
        available=conversion.available,
        conversion_value=conversion.converted,
        fee=conversion.fee,
        rate=conversion.trg_price
    )


@ns.route('/balance/business-types')
@respond_with_code
class BusinessTypesResource(Resource):

    SPOT = [
        BalanceBusiness.DEPOSIT,
        BalanceBusiness.WITHDRAWAL,
        BalanceBusiness.TRADING,
        BalanceBusiness.EXCHANGE_ORDER_TRANSFER,
        BalanceBusiness.MAKER_CASH_BACK,
        BalanceBusiness.MARGIN_TRANSFER,
        BalanceBusiness.PERPETUAL_TRANSFER,
        BalanceBusiness.INVESTMENT_TRANSFER,
        BalanceBusiness.INVESTMENT_IN,
        BalanceBusiness.INVESTMENT_OUT,
        BalanceBusiness.FIXED_INVEST_SUBSCRIBE,
        BalanceBusiness.FIXED_INVEST_MATURITY_REDEEM,
        BalanceBusiness.FIXED_INVEST_EARLY_REDEEM,
        BalanceBusiness.FIXED_INVEST_INTEREST,
        BalanceBusiness.STAKING_INCOME,
        BalanceBusiness.STAKING_ADD,
        BalanceBusiness.STAKING_REMOVE,
        BalanceBusiness.SUB_ACCOUNT_TRANSFER,
        BalanceBusiness.ADD_LIQUIDITY,
        BalanceBusiness.REMOVE_LIQUIDITY,
        BalanceBusiness.NORMAL_REFERRAL,
        BalanceBusiness.AMBASSADOR_REFERRAL,
        BalanceBusiness.INDIRECT_REFERRAL,
        BalanceBusiness.BROKER_REFERRAL,
        BalanceBusiness.ASSET_CONVERSION,
        BalanceBusiness.RED_PACKET_GRABBING,
        BalanceBusiness.RED_PACKET,
        BalanceBusiness.RED_PACKET_REFUND,
        BalanceBusiness.GIFT,
        BalanceBusiness.AUTO_INVEST_TRANSFER,
        BalanceBusiness.SPOT_GRID_TRANSFER,
        BalanceBusiness.PLEDGE_LOAN_ASSET_ADD,
        BalanceBusiness.PLEDGE_ASSET_LOCK,
        BalanceBusiness.PLEDGE_ASSET_RELEASE,
        BalanceBusiness.PLEDGE_REPAY,
        BalanceBusiness.COPY_TRADING_TRANSFER,
        BalanceBusiness.COPY_PROFIT_SETTLEMENT,
        BalanceBusiness.LAUNCH_POOL_MINING,
        BalanceBusiness.PERPETUAL_SUBSIDY_COUPON,
        BalanceBusiness.PRE_TRADING_ISSUE,
        BalanceBusiness.PRE_TRADING_REDEMPTION,
        BalanceBusiness.PRE_TRADING_POS_SETTLE,
        BalanceBusiness.PRE_TRADING_ISSUE_SETTLE,
        BalanceBusiness.EQUITY_CASHBACK,
        BalanceBusiness.EQUITY_AIRDROP,
        BalanceBusiness.SYSTEM,
        BalanceBusiness.P2P_ADD,
        BalanceBusiness.P2P_SUB,
        BalanceBusiness.P2P_MARGIN_PAYMENT,
        BalanceBusiness.P2P_MARGIN_REFUND,
        BalanceBusiness.COMMENT_TIP_IN,
        BalanceBusiness.COMMENT_TIP_OUT,
        BalanceBusiness.ONCHAIN,
        BalanceBusiness.ONCHAIN_ASSET_TO_SPOT,
        BalanceBusiness.PAYMENT_PAY,
        BalanceBusiness.PAYMENT_RECEIVE,
    ]

    MARGIN = [
        BalanceBusiness.TRADING,
        BalanceBusiness.MARGIN_TRANSFER,
        BalanceBusiness.MARGIN_LOAN,
        BalanceBusiness.MARGIN_REPAYMENT,
        BalanceBusiness.MARGIN_LIQUIDATION
    ]

    INVESTMENT = [
        BalanceBusiness.INVESTMENT_TRANSFER,
        BalanceBusiness.INVESTMENT_IN,
        BalanceBusiness.INVESTMENT_OUT,
        BalanceBusiness.INVESTMENT_INTEREST,
        BalanceBusiness.INVESTMENT_INC_INTEREST,
        BalanceBusiness.EQUITY_INVEST_INCREASE,
    ]

    FIXED_INVESTMENT = [
        BalanceBusiness.FIXED_INVEST_SUBSCRIBE,
        BalanceBusiness.FIXED_INVEST_MATURITY_REDEEM,
        BalanceBusiness.FIXED_INVEST_EARLY_REDEEM,
    ]

    PERPETUAL = [
        BalanceBusiness.PERPETUAL_TRANSFER_IN,
        BalanceBusiness.PERPETUAL_TRANSFER_OUT,
        BalanceBusiness.PERPETUAL_CLEARING_FUNDING,
        BalanceBusiness.PERPETUAL_CLEARING_CLOSE,
        BalanceBusiness.GIFT,
    ]

    PLEDGE = [
        BalanceBusiness.PLEDGE_ASSET_LOCK,
        BalanceBusiness.PLEDGE_ASSET_RELEASE,
        BalanceBusiness.PLEDGE_REPAY,
        BalanceBusiness.PLEDGE_LIQ,
        BalanceBusiness.PLEDGE_LIQ_FEE,
        BalanceBusiness.TRADING,
    ]

    STAKING = [
        BalanceBusiness.STAKING_ADD,
        BalanceBusiness.STAKING_REMOVE
    ]

    @classmethod
    def get(cls):
        return {
            'spot': [cls.format_business(business) for business in cls.SPOT],
            'margin': [cls.format_business(business) for business in cls.MARGIN],
            'investment': [cls.format_business(business) for business in cls.INVESTMENT],
            'fixed_investment': [cls.format_business(business) for business in cls.FIXED_INVESTMENT],
            'perpetual': [cls.format_business(business) for business in cls.PERPETUAL],
            'pledge': [cls.format_business(business) for business in cls.PLEDGE],
            'staking': [cls.format_business(business) for business in cls.STAKING],
        }

    @classmethod
    def format_business(cls, business):
        value = business.value
        display = gettext(get_balance_business_display(value))
        return {'key': value, 'name': display}


@ns.route('/asset/conversion')
@respond_with_code
class AssetConversionResource(Resource):

    @classmethod
    def get(cls):
        return cls._get_min_order_amounts()

    @classmethod
    @cached(300)
    def _get_min_order_amounts(cls):
        result = {
            asset: get_asset_config(asset).min_order_amount
            for asset in list_all_assets()
        }
        result['USDT'] = Decimal('1')
        return result

    @classmethod
    @require_login(allow_sub_account=False)
    @trade_permission_validate(is_spot=True, account_id=0)
    @limit_user_frequency(count=1, interval=10)
    @ns.use_kwargs(dict(
        assets=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        user_id = g.user.id

        cache = UserAssetConversionCache(user_id)
        if cache.value:
            # raise FrequencyExceeded(message=gettext('24小时内只能兑换一次，请稍后再尝试'))
            # 没翻译，暂时先注释掉
            raise FrequencyExceeded

        assets = set(kwargs['assets'].split(','))

        convertible = cls.get_convertible_assets(
            user_id, assets)
        trg_asset = convertible['trg_asset']
        trg_price = convertible['trg_price']
        conversions = convertible['conversions']
        online_assets = {i['asset'] for i in conversions}
        offline_assets = assets - online_assets
        if offline_assets:
            raise InvalidArgument(message=gettext('%(offline_assets)s已下架交易市场，不支持兑换',
                                                  offline_assets=(','.join(offline_assets))))

        server_client = ServerClient()
        converted_sum = Decimal()

        for info in conversions:
            asset = info['asset']
            available = info['available']
            converted_trg = info['converted_trg']
            fee_trg = info['fee_trg']

            conversion = db.session_add_and_commit(
                AssetConversion(
                    user_id=user_id,
                    asset=asset,
                    available=available,
                    trg_asset=trg_asset,
                    trg_price=trg_price,
                    converted=converted_trg,
                    fee=fee_trg
                ))
            try:
                server_client.add_user_balance(
                    user_id, asset, -available,
                    BalanceBusiness.ASSET_CONVERSION, conversion.id)
            except server_client.BadResponse as e:
                if e.code == server_client.ResponseCode.INSUFFICIENT_BALANCE:
                    conversion.status = AssetConversion.Status.FAILED
                    conversion.updated_at = now()
                    db.session.commit()
                    continue
                raise

            conversion.status = AssetConversion.Status.DEDUCTED
            conversion.updated_at = now()
            db.session.commit()

            server_client.add_user_balance(
                user_id, trg_asset, converted_trg,
                BalanceBusiness.ASSET_CONVERSION, conversion.id)
            conversion.status = AssetConversion.Status.FINISHED
            conversion.updated_at = now()
            history_record = AssetConversionHistory(
                record_id=conversion.id,
                user_id=user_id,
                source_asset=asset,
                source_amount=available,
                target_asset=trg_asset,
                converted_amount=converted_trg
            )
            db.session.add(history_record)
            db.session.commit()
            converted_sum += converted_trg

        cache.set('1', ex=86400)

        return dict(
            conversion_value=converted_sum
        )

    @classmethod
    def get_convertible_assets(cls, user_id: int, assets: Iterable[str] = None
                               ) -> dict:
        if assets:
            def check_asset(_a):
                if not has_asset(_a):
                    raise AssetNotFound(_a)
                return normalise_asset_code(_a)
            assets = list(map(check_asset, assets))
        else:
            assets = list_all_assets()

        trg_asset = 'CET'
        min_order_amounts = cls._get_min_order_amounts()
        prices = PriceManager.assets_to_usd(chain_iter(assets, [trg_asset]))
        server_client = ServerClient()
        balances = server_client.get_user_balances(user_id)
        conversions = []
        trg_price = prices[trg_asset]
        quantize = partial(quantize_amount, decimals=8)
        disabled_assets = BusinessSettings.disabled_conversion_assets
        markets_info = MarketCache.online_markets_detail()
        online_assets = {i['base_asset'] for i in markets_info.values()} | \
                        {i['quote_asset'] for i in markets_info.values()}
        for asset in assets:
            if asset == trg_asset:
                continue
            if asset in disabled_assets:
                continue
            if asset not in online_assets:
                continue
            try:
                available = balances[asset]['available']
            except KeyError:
                continue
            if not 0 < available <= Decimal(min_order_amounts.get(asset, 0)):
                continue
            price = prices.get(asset, Decimal())
            available_usd = available * price
            if available_usd > Decimal('100'):
                continue
            amount_trg = quantize(available_usd / trg_price)
            fee_trg = quantize(amount_trg * ASSET_CONVERSION_FEE_RATE)
            converted_trg = amount_trg - fee_trg
            if converted_trg <= 0:
                converted_trg = Decimal('0.00000001')

            conversions.append(dict(
                asset=asset,
                price=quantize(price),
                available=quantize(available),
                available_usd=quantize(available_usd),
                converted_trg=converted_trg,
                fee_trg=fee_trg
            ))

        return dict(
            trg_asset=trg_asset,
            trg_price=trg_price,
            conversions=conversions
        )


@ns.route('/asset/conversion_list')
@respond_with_code
class AssetConvertibleResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        convertible = AssetConversionResource.get_convertible_assets(g.user.id)
        trg_price = convertible['trg_price']
        conversions = convertible['conversions']

        data = []
        for info in conversions:
            data.append(dict(
                asset=info['asset'],
                available=info['available'],
                conversion_value=info['converted_trg'],
                fee_value=info['fee_trg'],
                market_value=info['available_usd']
            ))

        return dict(
            data=data,
            rate=trg_price,
            fee_rate=ASSET_CONVERSION_FEE_RATE
        )


@ns.route('/asset/conversion_history')
@respond_with_code
class AssetConversionHistoryResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        records = AssetConversion.query \
            .filter(AssetConversion.user_id == g.user.id,
                    AssetConversion.status == AssetConversion.Status.FINISHED)\
            .order_by(AssetConversion.id.desc()) \
            .paginate(kwargs['page'], kwargs['limit'], error_out=False)

        return dict(
            has_next=records.has_next,
            curr_page=records.page,
            count=len(records.items),
            data=list(map(_asset_conversion_to_dict, records.items)),
            total=records.total,
            total_page=records.pages
        )


############################## 以下接口不再使用，App兼容保留 ##############################
@ns.route('/asset/setting')
@respond_with_code
class AssetSettingsResource(Resource):

    @classmethod
    def get(cls):
        raise VersionNeedUpgrade


@ns.route('/coin/deposit')
@respond_with_code
class DepositsResource(Resource):

    @classmethod
    def get(cls):
        raise VersionNeedUpgrade


# noinspection PyUnresolvedReferences
@ns.route('/coin/deposit/<int:id_>')
@respond_with_code
class DepositResource(Resource):

    @classmethod
    def get(cls, id_):
        raise VersionNeedUpgrade


@ns.route('/coin/deposit/processing')
@respond_with_code
class ProcessingDepositsResource(Resource):

    @classmethod
    def get(cls):
        raise VersionNeedUpgrade


@ns.route('/coin/withdraw')
@respond_with_code
class WithdrawalsResource(Resource):

    @classmethod
    def get(cls):
        raise VersionNeedUpgrade

    @classmethod
    def post(cls):
        raise VersionNeedUpgrade


# noinspection PyUnresolvedReferences
@ns.route('/coin/withdraw/<int:id_>')
@respond_with_code
class WithdrawalResource(Resource):

    @classmethod
    def get(cls, id_):
        raise VersionNeedUpgrade

    @classmethod
    def patch(cls, id_):
        raise VersionNeedUpgrade

    @classmethod
    def delete(cls, id_):
        raise VersionNeedUpgrade


@ns.route('/coin/withdraw/amount')
@respond_with_code
class WithdrawalAmountResource(Resource):

    @classmethod
    def get(cls):
        raise VersionNeedUpgrade


@ns.route('/withdraw/limit')
@respond_with_code
class WithdrawalLimitResource(Resource):

    @classmethod
    def get(cls):
        raise VersionNeedUpgrade


@ns.route('/deposit/withdrawal/broadcast')
@respond_with_code
class DepositWithdrawalBroadcastResource(Resource):

    @classmethod
    def get(cls):
        raise VersionNeedUpgrade
############################## 以上接口不再使用，App兼容保留 ##############################


@ns.route('/fee')
@respond_with_code
class FeeResource(Resource):
    @classmethod
    def get(cls):
        return dict(
            fee_asset=SiteSettings.fee_deduction_asset,
            fee_discount=SiteSettings.fee_deduction_rate,
            contract_fee_discount='1',
        )


@ns.route('/special/fee')
@respond_with_code
class SpecialFeeResource(Resource):

    @classmethod
    @require_login
    def get(cls):
        user_id = g.user.id
        fee_dict = UserFeeParser(user_id).get_user_special_fee()
        market_info = defaultdict(lambda: defaultdict(dict))
        for (business_type, trade_type, market), info in fee_dict.items():
            market_info[business_type][market][trade_type] = info

        ret = defaultdict(list)
        for business_type, markets in market_info.items():
            for market, trade_info in markets.items():
                taker = trade_info.get(TradeType.TAKER, {})
                maker = trade_info.get(TradeType.MAKER, {})
                if not taker and not maker:
                    continue
                ret[business_type.name].append(dict(
                    market=market,
                    taker=amount_to_str(taker['fee'], 8) if taker else None,
                    maker=amount_to_str(maker['fee'], 8) if maker else None,
                    valid_ttl=UserFeeParser.min_with_zero_as_max([taker.get('expired_time'), maker.get('expired_time')])
                ))

        for _type in TradeBusinessType:
            if _type.name not in ret:
                ret[_type.name] = []

        # AMM市场的手续费和现货一样，过滤掉没有AMM的现货
        amm_set = AmmMarketCache.list_amm_markets()
        ret['AMM'] = [i for i in ret[TradeBusinessType.SPOT.name]
                      if i['market'] in amm_set or i['market'] == ALL_MARKETS]

        return ret

@ns.route('/user/fee')
@respond_with_code
class UserFeeResource(Resource):

    @classmethod
    @require_login
    def get(cls):
        setting = UserPreferences(g.user.id)
        return dict(
            fee_asset=SiteSettings.fee_deduction_asset,
            fee_discount=FeeFetcher(g.user.id).fetch_fee_deduction_rate(),
            contract_fee_discount='1',
            fee_switch=setting.cet_discount_enabled
        )

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        fee_switch=fields.Boolean(required=True)
    ))
    def put(cls, **kwargs):
        user = g.user
        fee_switch = kwargs["fee_switch"]
        UserPreferences(user.id).cet_discount_enabled = fee_switch
        OperationLog.add(user.id, OperationLog.Operation.TURN_FEE_SWITCH,
                         'on' if fee_switch else 'off', get_request_platform())


@ns.route('/balance/history')
@respond_with_code
class BalanceHistoryResource(Resource):

    EXPORT_HEADERS = (
        {'field': 'time', Language.ZH_HANS_CN: '时间', Language.EN_US: 'Time'},
        {'field': 'account', Language.ZH_HANS_CN: '账户',
         Language.EN_US: 'Account'},
        {'field': 'business_display', Language.ZH_HANS_CN: '操作',
         Language.EN_US: 'Operation'},
        {'field': 'asset', Language.ZH_HANS_CN: '币种', Language.EN_US: 'Coin'},
        {'field': 'change_display', Language.ZH_HANS_CN: '资产变化',
         Language.EN_US: 'Asset change'},
        {'field': 'balance_display', Language.ZH_HANS_CN: '账户余额',
         Language.EN_US: 'Balance'}
    )

    PLEDGE_EXPORT_HEADERS = (
        {'field': 'time', Language.ZH_HANS_CN: '时间', Language.EN_US: 'Time'},
        # 质押账户流水导出表头名称不同
        {'field': 'loan_asset', Language.ZH_HANS_CN: '借币币种', Language.EN_US: 'Borrowed Coin'},
        {'field': 'business_display', Language.ZH_HANS_CN: '操作', Language.EN_US: 'Operation'},
        {'field': 'asset', Language.ZH_HANS_CN: '币种', Language.EN_US: 'Coin'},
        {'field': 'change_display', Language.ZH_HANS_CN: '资产变化', Language.EN_US: 'Asset change'},
        {'field': 'balance_display', Language.ZH_HANS_CN: '账户余额', Language.EN_US: 'Balance'},
    )

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        asset=AssetField(missing=''),
        account_id=fields.Integer(missing=0),
        account_type=EnumField(['FIXED_INVESTMENT']),
        business=fields.String(missing=''),
        start_time=TimestampField(),
        end_time=TimestampField(),
        page=PageField,
        limit=LimitField,
        export=fields.Integer(missing=0)
    ))
    def get(cls, **kwargs):
        user: User = g.user
        start = kwargs.get('start_time')
        end = kwargs.get('end_time')
        page = kwargs['page']
        limit = kwargs['limit']
        export = kwargs['export']
        business = kwargs['business']
        asset = kwargs['asset']
        history = dict()
        empty_dict = dict(data=[], curr_page=1, has_next=False, count=0, total=0)

        server_client = ServerClient()
        # 普通返佣和大使(代理)返佣通过返佣+币种区分
        if business == BalanceBusiness.AMBASSADOR_REFERRAL.value:
            business = BalanceBusiness.REFERRAL.value
            if asset and asset != ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR]:
                # 由于server历史数据兼容business='refer'，
                # 兼容business='ambassador_referral'但asset='CET'查出普通返佣的数据
                history = empty_dict
            else:
                asset = ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR]
        elif business == BalanceBusiness.NORMAL_REFERRAL.value:
            business = BalanceBusiness.REFERRAL.value
            if asset and asset != ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.REFERRAL]:
                history = empty_dict
            else:
                asset = ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.REFERRAL]
        account_id = kwargs['account_id']
        account_type = kwargs.get('account_type')
        if account_id == AssetInvestmentConfig.ACCOUNT_ID:
            # 定期理财和活期理财流水在同一个account_id下，区分下
            if account_type == 'FIXED_INVESTMENT':
                # 只展示定期理财相关的流水
                q_bus_list = BusinessTypesResource.FIXED_INVESTMENT
            else:
                # 只展示活期理财相关的流水
                q_bus_list = BusinessTypesResource.INVESTMENT
            if not business:
                business = q_bus_list
        if not history:
            history = server_client.get_user_balance_history(
                user.id, asset,
                account_id=account_id,
                business=business,
                start_time=int(start.timestamp() if start else 0),
                end_time=int(end.timestamp() if end else 0),
                page=page if not export else 1,
                limit=limit if not export else config['EXPORT_ITEM_MAX_COUNT']
            ).as_dict(cls.history_to_dict)

        if export:
            pref = UserPreferences(user.id)
            data = history['data']
            dt_to_str = partial(datetime_to_str, offset_minutes=pref.timezone_offset)
            _is_pledge_acc = is_pledge_account(account_id)
            if _is_pledge_acc:
                headers = cls.PLEDGE_EXPORT_HEADERS
                account_loan_asset_dict = {
                    v: k for k, v in get_pledge_loan_asset_account_id_dict().items()
                }
            else:
                headers = cls.EXPORT_HEADERS
                account_loan_asset_dict = {}
            for h in data:
                h['time'] = dt_to_str(timestamp_to_datetime(h['time']))
                if _is_pledge_acc:
                    h['loan_asset'] = account_loan_asset_dict.get(h['account'], "")  # 导出显示借币币种
                else:
                    h['account'] = cls.account_name(h['account'])
            return export_xlsx('balance_history', data, headers)

        return history

    @classmethod
    def history_to_dict(cls, history: dict) -> dict:
        business = history["business"]
        if business == BalanceBusiness.REFERRAL.value:
            if history['asset'] == ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR]:
                business = BalanceBusiness.AMBASSADOR_REFERRAL.value
            elif history['asset'] == ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.REFERRAL]:
                business = BalanceBusiness.NORMAL_REFERRAL.value
        business_display = gettext(get_balance_business_display(business))
        quantize = partial(quantize_amount, decimals=8)
        return dict(
            time=int(history['time']),
            account=history.get('account', 0),
            business=history['business'],
            business_id=history['detail'].get('id', 0),
            business_display=business_display,
            asset=history['asset'],
            change=f'{quantize(history["change"]):.8f}',
            change_display=f'{quantize(history["change"]):+.8f}',
            balance=f'{quantize(history["balance"]):.8f}',
            balance_display=f'{quantize(history["balance"]):.8f}',
            remark=history['business']
        )

    @classmethod
    def account_name(cls, account_id: int) -> str:
        if account_id == 0:
            return gettext('现货账户')
        if account_id in (AssetInvestmentConfig.ACCOUNT_ID, StakingAccount.ACCOUNT_ID):
            return gettext('理财账户')
        if is_pledge_account(account_id):
            return gettext('借贷账户')
        market_name = MarginHelper.get_market_type_by_account_id(account_id)
        return gettext('%(market)s-杠杆账户', market=market_name)


@ns.route('/balance/series')
@respond_with_code
class BalanceSeriesResource(Resource):

    @classmethod
    @require_login(allow_sub_account=True)
    @ns.use_kwargs(dict(
        account_type=EnumField(AccountBalanceType, required=True),
        asset=AssetField(),
        account_id=fields.Integer(
            validate=lambda x: SPOT_ACCOUNT_ID < x < MAX_ORDER_ACCOUNT_ID
        ),
        # 时间范围，一个月/三个月/六个月
        days=fields.Integer(
            validate=lambda x: x in (30, 90, 180),
            missing=90
        ),
        market=fields.String
    ))
    def get(cls, **kwargs):
        user_id = g.user.id
        account_type = kwargs['account_type']
        asset = kwargs.get('asset')
        account_id = kwargs.get('account_id')
        market = kwargs.get('market')
        if account_type == AccountBalanceType.MARGIN:
            if not account_id:
                raise InvalidArgument
        elif account_type == AccountBalanceType.AMM:
            if not market:
                raise InvalidArgument
        else:
            if not asset:
                raise InvalidArgument
        days = kwargs.get("days", 90)
        points = get_suggest_user_balance_series_points(g.user, account_type=account_type,
                                                        max_points=days)
        point_start_date = today() - timedelta(days=points)
        if account_type == AccountBalanceType.SPOT:
            return get_user_spot_balance_series(user_id, asset, point_start_date)
        if account_type == AccountBalanceType.INVESTMENT:
            return get_user_investment_balance_series(user_id, asset, point_start_date)
        if account_type == AccountBalanceType.PERPETUAL:
            return get_user_perpetual_balance_series(user_id, asset, point_start_date)
        if account_type == AccountBalanceType.MARGIN:
            return get_user_margin_balance_series(user_id, account_id, point_start_date)
        if account_type == AccountBalanceType.AMM:
            return get_user_amm_balance_series(user_id, market, points)
        raise InvalidArgument


@ns.route('/balance/sum/series')
@respond_with_code
class BalanceSumSeriesResource(Resource):

    @classmethod
    @require_login(allow_sub_account=True)
    @ns.use_kwargs(dict(
        account_type=EnumField(AccountBalanceType),
        # 时间范围，一个月/三个月/六个月
        days=fields.Integer(
            validate=lambda x: x in (30, 90, 180),
            missing=90
        ),
    ))
    def get(cls, **kwargs):
        user_id = g.user.id
        account_type = kwargs.get('account_type')
        days = kwargs.get("days", 90)
        points = get_suggest_user_balance_series_points(g.user, account_type=account_type, max_points=days)
        point_start_date = today() - timedelta(days=points)
        if not account_type:
            return get_user_balance_sum_series(user_id, point_start_date)
        if account_type == AccountBalanceType.SPOT:
            return get_user_spot_balance_sum_series(user_id, point_start_date)
        if account_type == AccountBalanceType.AMM:
            return get_user_amm_balance_sum_series(user_id, point_start_date)
        raise InvalidArgument


@ns.route('/transfer/history')
@respond_with_code
class AccountTransferHistoryResource(Resource):

    EXPORT_HEADERS = (
        {'field': 'time', Language.ZH_HANS_CN: '时间', Language.EN_US: 'Time'},
        {'field': 'asset', Language.ZH_HANS_CN: '资产', Language.EN_US: 'Coin'},
        {'field': 'amount', Language.ZH_HANS_CN: '数目', Language.EN_US: 'Amount'},
        {'field': 'source_account_type', Language.ZH_HANS_CN: '转出', Language.EN_US: 'Transfer Out'},
        {'field': 'target_account_type', Language.ZH_HANS_CN: '转入', Language.EN_US: 'Transfer In'},
    )

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            asset=AssetField(),
            account_type=EnumField(AccountTransferLog.AccountType),
            start_time=TimestampField(),
            end_time=TimestampField(),
            page=PageField,
            limit=LimitField,
            export=fields.Integer(missing=0),
        )
    )
    def get(cls, **kwargs):
        """ 账户间划转历史 """
        user_id = g.user.id

        query = AccountTransferLog.query.filter(
            AccountTransferLog.user_id == user_id,
        ).order_by(AccountTransferLog.created_at.desc())
        if account_type := kwargs.get("account_type"):
            # 筛选账户 展示现货账户和这个账户之间的所有转入转出记录
            query = query.filter(
                or_(
                    and_(
                        AccountTransferLog.source_account_type == AccountTransferLog.AccountType.SPOT,
                        AccountTransferLog.target_account_type == account_type,
                    ),
                    and_(
                        AccountTransferLog.source_account_type == account_type,
                        AccountTransferLog.target_account_type == AccountTransferLog.AccountType.SPOT,
                    ),
                ),
            )
        if asset := kwargs.get('asset'):
            query = query.filter(AccountTransferLog.asset == asset)
        if start_time := kwargs.get('start_time'):
            query = query.filter(AccountTransferLog.created_at >= start_time)
        if end_time := kwargs.get('end_time'):
            _next_end_dt = end_time + timedelta(days=1)  # 包含end_date当天内的数据
            query = query.filter(AccountTransferLog.created_at < _next_end_dt)

        is_export = kwargs['export']
        if is_export:
            pagination = query.paginate(1, config['EXPORT_ITEM_MAX_COUNT'], error_out=False)
            pref = UserPreferences(user_id)
            dt_to_str = partial(datetime_to_str, offset_minutes=pref.timezone_offset)
            export_data = []
            for record in pagination.items:
                export_data.append(
                    {
                        "time": dt_to_str(record.created_at),
                        "asset": record.asset,
                        "amount": amount_to_str(record.amount),
                        "source_account_type": record.source_account_type.value,
                        "target_account_type": record.target_account_type.value,
                    }
                )
            headers = cls.EXPORT_HEADERS
            return export_xlsx('account_transfer_history', export_data, headers)

        pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        items = []
        for record in pagination.items:
            items.append(
                {
                    "time": int(record.created_at.timestamp()),
                    "asset": record.asset,
                    "amount": record.amount,
                    "source_account_type": record.source_account_type.name,
                    "target_account_type": record.target_account_type.name,
                }
            )
        return dict(
            data=items,
            curr_page=pagination.page,
            has_next=pagination.has_next,
            count=len(items),
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route('/realtime-balance')
@respond_with_code
class RealtimeBalanceResource(Resource):

    @classmethod
    def get_user_realtime_usd(cls, user_id: int, is_sub: bool) -> str:
        cache = UserBalanceSumCache(user_id)
        if v := cache.read():
            return v
        usd = RealtimeAccountProfitLossProcessor.get_real_time_account_type_usd(
            user_id,
            TOTAL_ACCOUNT,
            False,
            True
        )
        if not is_sub and (sub_ids:= get_sub_account_user_ids(user_id, SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES)):
            sub_balance_map = get_sub_account_balance_map(user_id, sub_user_ids=sub_ids)
            sub_usd = sum(sub_balance_map.values())
            usd += sub_usd
        v = amount_to_str(usd, 2)
        cache.set(v, ex=cache.ttl)
        return v

    @classmethod
    @require_login
    def get(cls):
        """导航栏的资产字段。主帐号时，要包括子帐号的资产市值 """
        return {
            TOTAL_ACCOUNT: cls.get_user_realtime_usd(g.user.id, g.user.is_sub_account)
        }


@ns.route('/realtime-account-balance')
@respond_with_code
class RealtimeAccountBalanceResource(Resource):

    @classmethod
    @cached(30)
    def get_(cls, user_id: int, is_sub: bool) -> dict:
        accounts_usd_map = RealtimeAccountProfitLossProcessor.get_real_time_accounts_usd(
            user_id,
            include_invisible_assets=False,
        )
        if not is_sub and has_sub_account(user_id):
            sub_balance_map = get_sub_account_balance_map(user_id)
            sub_usd = sum(sub_balance_map.values())
        else:
            sub_usd = Decimal()
        accounts_usd_map['SUB'] = sub_usd
        return accounts_usd_map

    @classmethod
    @require_login
    def get(cls):
        """ 各个帐户的资产市值（APP使用） """
        return cls.get_(g.user.id, g.user.is_sub_account)


@ns.route('/asset/logo')
@respond_with_code
class AssetLogoResource(Resource):
    """已废弃，兼容保留"""

    @classmethod
    @cached(600)
    def get(cls):
        rows = CoinInformation.query.filter(
            CoinInformation.status == CoinInformation.Status.VALID
        ).with_entities(
            CoinInformation.code, CoinInformation.icon
        ).all()
        return [dict(
            asset=code,
            logo=AWSBucketPublic.get_file_url(icon)
         ) for code, icon in rows]


class TaxExportHistoryMixin:

    @classmethod
    def _get_left_export_times(cls, user_id) -> int:
        now_ = now()
        month_first_day = datetime.datetime(now_.year, now_.month, 1)
        export_times = TaxExportHistory.query.filter(
            TaxExportHistory.created_at >= month_first_day,
            TaxExportHistory.created_at <= now_,
            TaxExportHistory.user_id == user_id
        ).with_entities(
            func.count(1)
        ).scalar() or 0
        left_export_times = TaxExportHistory.MONTHLY_EXPORT_LIMIT - export_times
        return left_export_times if left_export_times >= 0 else 0


@ns.route('/tax/export')
@respond_with_code
class TaxExportResource(Resource, TaxExportHistoryMixin):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        start_time=fields.Integer(required=True),
        end_time=fields.Integer(required=True),
        account=EnumField(TaxExportHistory.Account, enum_by_value=True,
                          missing=TaxExportHistory.Account.ALL),
        asset=fields.String(missing='ALL'),
        hide_transfer=fields.Boolean(missing=True)
    ))
    def post(cls, **kwargs):
        """导出报税数据"""
        user_id = g.user.id
        with CacheLock(LockKeys.tax_export(user_id), wait=False):
            db.session.rollback()
            if not cls._get_left_export_times(user_id):
                raise BeyondMaxExportTimesException
            record_id = cls._add_record(user_id, **kwargs)
            export_tax_data.delay(record_id)
        return {}

    @classmethod
    def _add_record(cls, user_id, **kwargs):
        cls._check(**kwargs)
        rec = TaxExportHistory(
            user_id=user_id,
            status=TaxExportHistory.Status.PENDING,
            start_time=kwargs['start_time'],
            end_time=kwargs['end_time'],
            account=kwargs['account'],
            asset=kwargs['asset'],
            hide_transfer=kwargs['hide_transfer'],
        )
        db.session.add(rec)
        db.session.commit()
        return rec.id

    @classmethod
    def _check(cls, **kwargs):
        start_time, end_time = kwargs['start_time'], kwargs['end_time']
        if start_time > end_time:
            raise InvalidArgument
        asset = kwargs['asset']
        if asset != 'ALL':
            if not has_asset(asset):
                raise AssetNotFound(asset)


@ns.route('/tax/export/history')
@respond_with_code
class TaxExportHistoryResource(Resource, TaxExportHistoryMixin):

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField(missing=20)
    ))
    def get(cls, **kwargs):
        """报税数据导出历史"""
        user_id = g.user.id
        page, limit = kwargs.get('page'), kwargs.get('limit')
        left_export_times = cls._get_left_export_times(user_id)
        export_history = cls._get_export_history(user_id, page, limit)
        return {'left_export_times': left_export_times, 'export_history': export_history}

    @classmethod
    def _get_export_history(cls, user_id, page, limit):
        query = TaxExportHistory.query.filter(
            TaxExportHistory.user_id == user_id
        ).order_by(TaxExportHistory.id.desc())
        total = query.count()
        data = []
        pagination = query.paginate(page, limit, error_out=False)
        now_ = now()
        for record in pagination.items:
            status = record.status
            if status == TaxExportHistory.Status.FINISHED:
                finished_at = record.finished_at
                if now_ > finished_at + datetime.timedelta(days=TaxExportHistory.EXPIRE_DAYS):
                    status = TaxExportHistory.Status.EXPIRED
            item = {
                'id': record.id,
                'created_at': record.created_at.timestamp(),
                'start_time': record.start_time,
                'end_time': record.end_time,
                'status': status.name
            }
            data.append(item)
        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            data=data,
            total=total,
            total_page=math.ceil(total / limit)
        )


@ns.route('/tax/export/download/<int:id_>')
@respond_with_code
class TaxExportDownloadResource(Resource):

    @classmethod
    @require_login
    def get(cls, id_):
        """下载已导出的报税数据"""
        user = g.user
        rec = TaxExportHistory.query.get(int(id_))
        if not rec or rec.status != TaxExportHistory.Status.FINISHED or rec.user_id != user.id:
            raise InvalidArgument
        return {'url': rec.file_url}


class SharePopBase(Resource):
    model = UserShareWindowRecord
    old_android_build = 3410
    old_ios_build = 80
    incompatible_pop_types = [
        model.PopType.investment_amount.name,
        model.PopType.stake_amount.name,
        model.PopType.first_vip.name,
        model.PopType.first_launch_pool.name
    ]


@ns.route('/share-pop-window')
@respond_with_code
class SharePopResource(SharePopBase):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """获取用户分享弹窗详情"""
        user_id = g.user.id
        fmt_detail = SharePopWindowRepository.get_pop_window_detail(user_id)
        waiting_pop_win = SharePopWindowRepository.get_waiting_pop_win(user_id)
        return {
            'pop_win_detail': fmt_detail,
            'waiting_pop_win': cls.old_app_filter(waiting_pop_win),
            'referral_code': ReferralBusiness.get_code_by_user(user_id),
        }

    @classmethod
    def old_app_filter(cls, wait_pops):
        is_old_app = is_old_app_request(cls.old_android_build, cls.old_ios_build)
        # 旧版本 app 不兼容的弹窗类型
        new_pops = []
        for pop in wait_pops:
            if is_old_app and pop['pop_type'] in cls.incompatible_pop_types:
                continue
            new_pops.append(pop)
        return new_pops

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        pop_type=EnumField(UserShareWindowRecord.PopType),
        pop_types=fields.List(fields.Dict(keys=fields.String, values=fields.String)),
    ))
    def patch(cls, **kwargs):
        """更新用户已弹窗的记录"""
        user = g.user
        pop_type = kwargs.get('pop_type')
        pop_types = kwargs.get('pop_types', [])
        if not pop_type and not pop_types:
            raise InvalidArgument("need pop_type or pop_types")
        if pop_type and not pop_types:
            pop_types = [{
                "name": pop_type.name,
                "value": "",
            }]
        pop_names = {i.name for i in cls.model.PopType}
        popped_list = [i["name"] for i in pop_types]
        if set(popped_list) - pop_names:
            return
        pop_types = [{"name": cls.model.PopType[i["name"]], "value": i["value"]} for i in pop_types]
        SharePopWindowRepository.update_pop_win_status(user.id, pop_types)
        return


@ns.route('/balance/merkle/reserve')
@respond_with_code
class BalanceMerkleReserve(Resource):

    @classmethod
    def get(cls):
        s_list: list[UserBalanceMerkleInfo] = UserBalanceMerkleInfo.get_all()
        ret = []
        for s in s_list:
            wallet = s.get_asset_balances()
            site = json.loads(s.user_balances)
            prices = {k: Decimal(v) for k, v in json.loads(s.prices).items()}
            assets = list(wallet.keys())
            AssetUtils.sort_asset_codes(assets)
            result = []
            for a in assets:
                price = prices[a]
                rate = wallet[a] / Decimal(site[a])
                result.append(dict(
                    asset=a,
                    wallet_amount=wallet[a],
                    wallet_usd=quantize_amount(wallet[a] * price, 2),
                    user_amount=site[a],
                    reserve_rate=quantize_amount(rate * 100, 2),
                    price=price
                ))
            item = dict(
                time=s.snapshot_time,
                balances=result,
                wallet_proof_file=s.wallet_proof_file or ''
            )
            if v := s.sum_wallet_total_balances():
                item['wallet_total_usd'] = v
            item['root_hash'] = UserBalanceMerkleTree.get_root_hash(s.id)
            ret.append(item)
        return ret


@ns.route('/balance/merkle/proof')
@respond_with_code
class BalanceMerkleProof(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        s: UserBalanceMerkleInfo = UserBalanceMerkleInfo.get_latest()
        if not s:
            return {}
        r = MerkleBusiness.get_proof_path(s.id, g.user.id)
        if not r:
            return {}
        total = json.loads(s.user_balances)
        total = {k: Decimal(v) for k, v in total.items()}
        balances = r['self']['balances']
        assets = list(balances.keys())
        AssetUtils.sort_asset_codes(assets)
        result = []
        for k, v in balances.items():
            result.append(dict(
                asset=k,
                amount=v,
                rate=quantize_amount(v / total[k] * 100, 4)
            ))
        return dict(
            balances=result,
            proof_path=json.dumps(r, cls=JsonEncoder, separators=(',', ':'))
        )


@ns.route('/balance/lock')
@respond_with_code
class BalanceLockProof(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, **kwargs):
        user_id = g.user.id
        p2p_asset_lock_map = get_account_p2p_lock_map(user_id)
        return p2p_asset_lock_map


class AccountStatementExportHistoryMixin:

    EXPORT_FILENAME_TEMPLATE = "CoinEx Account Statement - {export_date}.pdf"

    @classmethod
    def _get_export_count(cls, user_id) -> (int, int):
        _now = now()
        month_first_day = datetime.datetime(_now.year, _now.month, 1)
        export_times = AccountStatementExportHistory.query.filter(
            AccountStatementExportHistory.created_at >= month_first_day,
            AccountStatementExportHistory.created_at <= _now,
            AccountStatementExportHistory.user_id == user_id,
            AccountStatementExportHistory.status != AccountStatementExportHistory.Status.FAILED,
        ).with_entities(
            func.count(1)
        ).scalar() or 0
        left_export_times = AccountStatementExportHistory.MONTHLY_EXPORT_LIMIT - export_times
        return export_times, left_export_times if left_export_times >= 0 else 0


@ns.route('/statement/export')
@respond_with_code
class AccountStatementExportResource(Resource, AccountStatementExportHistoryMixin):

    @classmethod
    @require_login()
    @ns.use_kwargs(dict(
        export_month=fields.String(required=True),
        include_deposit_withdrawal_detail=fields.Boolean(missing=False),
        include_order_detail=fields.Boolean(missing=False),
    ))
    def post(cls, **kwargs):
        """导出账户结单数据"""
        user_id = g.user.id
        export_month = kwargs['export_month']
        try:
            dt = datetime.datetime.strptime(export_month, "%Y-%m")
            dt = dt.replace(tzinfo=UTC)
        except Exception:
            raise InvalidArgument

        _now = now()
        if dt > _now:
            raise InvalidArgument

        if (_now.year - dt.year) * 12 + (_now.month - dt.month) > 12:  # 只允许导出12个自然月内的数据
            raise InvalidArgument

        start_time = datetime.datetime(dt.year, dt.month, 1)
        if dt.month == _now.month:
            end_time = _now
            read_snap = False
        else:
            end_time = datetime.datetime(dt.year, dt.month, 1) + relativedelta(months=1)
            read_snap = True

        with CacheLock(LockKeys.tax_export(user_id), wait=False):
            db.session.rollback()
            _, left_export_times = cls._get_export_count(user_id)
            if not left_export_times:
                raise BeyondMaxExportTimesException
            record_id = cls._add_record(
                user_id, start_time, end_time, export_month, kwargs['include_deposit_withdrawal_detail'],
                kwargs['include_order_detail'], read_snap,
            )
            export_account_statement_data.delay(record_id)
        return {}

    @classmethod
    def _add_record(
        cls, user_id, start_time, end_time, export_month, include_deposit_withdrawal_detail, include_order_detail,
        read_snap,
    ):
        record = AccountStatementExportHistory(
            user_id=user_id,
            status=AccountStatementExportHistory.Status.PENDING,
            start_time=start_time.timestamp(),
            end_time=end_time.timestamp(),
            export_month=export_month,
            lang=get_user_web_lang(user_id),
            include_deposit_withdrawal_detail=include_deposit_withdrawal_detail,
            include_order_detail=include_order_detail,
            read_snap=read_snap,
        )
        db.session.add(record)
        db.session.commit()
        return record.id


@ns.route('/statement/export/send_email')
@respond_with_code
class AccountStatementExportSendResource(Resource, AccountStatementExportHistoryMixin):

    @classmethod
    @require_login()
    @limit_user_frequency(count=5, interval=3600)
    @ns.use_kwargs(dict(
        export_history_id=fields.Integer(required=True)
    ))
    def post(cls, **kwargs):
        """发送账户结单数据到主账户邮箱"""
        user = g.user
        main_user = user.main_user
        export_history_id = kwargs['export_history_id']
        record = AccountStatementExportHistory.query.get(export_history_id)
        if not record:
            raise InvalidArgument

        if record.status != AccountStatementExportHistory.Status.FINISHED:
            raise InvalidArgument

        email = main_user.email
        pref = UserPreferences(main_user.id)
        filename = cls.EXPORT_FILENAME_TEMPLATE.format(export_date=record.export_month)
        export_data = datetime_to_str(record.created_at, fmt="%Y-%m-%d")
        send_notice_email.delay(
            email=email,
            email_type='account_statement_export_resend',
            template_args=dict(
                export_data=export_data,
                name=user.name_displayed,
                file_url=AWSBucketPrivate.get_file_url(record.file_key, ttl=60 * 60 * 24 * 7, filename=filename)
            ),
            lang=pref.language.value,
        )
        return {}


@ns.route('/statement/export/history')
@respond_with_code
class AccountStatementExportHistoryResource(Resource, AccountStatementExportHistoryMixin):

    @classmethod
    @require_login()
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField(missing=20)
    ))
    def get(cls, **kwargs):
        """获取账户结单导出任务列表"""
        user_id = g.user.id
        page, limit = kwargs.get('page'), kwargs.get('limit')
        export_times, left_export_times = cls._get_export_count(user_id)
        export_history = cls._get_export_history(user_id, page, limit)

        return dict(
            total_export_times=AccountStatementExportHistory.MONTHLY_EXPORT_LIMIT,
            export_times=export_times,
            left_export_times=left_export_times,
            export_history=export_history,
            main_user_email=g.user.main_user.email,
        )

    @classmethod
    def _get_export_history(cls, user_id, page, limit):
        query = AccountStatementExportHistory.query.filter(
            AccountStatementExportHistory.user_id == user_id
        ).order_by(AccountStatementExportHistory.id.desc())
        total = query.count()
        data = []
        pagination = query.paginate(page, limit, error_out=False)
        now_ = now()
        for record in pagination.items:
            status = record.status
            if status == AccountStatementExportHistory.Status.FINISHED:
                finished_at = record.finished_at
                if now_ > finished_at + datetime.timedelta(days=AccountStatementExportHistory.EXPIRE_DAYS):
                    status = AccountStatementExportHistory.Status.EXPIRED
            item = {
                'id': record.id,
                'created_at': record.created_at.timestamp(),
                'status': status.name,
                'file_name': f"{record.export_month}_SOA",
                'file_url': AWSBucketPrivate.get_file_url(
                    record.file_key, filename=cls.EXPORT_FILENAME_TEMPLATE.format(export_date=record.export_month)
                ) if record.file_key and status == AccountStatementExportHistory.Status.FINISHED else "",
            }
            data.append(item)
        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            data=data,
            total=total,
            total_page=math.ceil(total / limit)
        )


@ns.route('/balance/cost')
@respond_with_code
class AccountBalanceCostResource(Resource):

    EXCLUDED_ASSETS = ("USDT", "USDC")

    MAX_PRICE = Decimal("9999999")

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        page=PageField,
        limit=LimitField(missing=200),
    ))
    def get(cls, **kwargs):
        """
        现货币种持仓成本
        """
        user_id = g.user.id
        query = AssetCost.query.filter(
            AssetCost.user_id == user_id,
            AssetCost.account_type == AccountBalanceType.SPOT,
            AssetCost.asset.not_in(cls.EXCLUDED_ASSETS)
        ).with_entities(
            AssetCost.asset,
            AssetCost.price
        ).order_by(AssetCost.amount.desc())
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        return dict(
            items=[dict(asset=asset, price=price) for asset, price in pagination.items]
        )
    
    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        asset=AssetField(required=True),
        price=PositiveDecimalField(required=True)
    ))
    def post(cls, **kwargs):
        if kwargs['asset'] in cls.EXCLUDED_ASSETS:
            raise InvalidArgument
        if kwargs['price'] >= cls.MAX_PRICE:
            raise InvalidArgument(message=gettext("新成本价偏差过大，请重新输入"))

        asset = kwargs['asset']
        user_id = g.user.id
        record = AssetCost.get_or_create(
            user_id=user_id,
            asset=asset,
            account_type=AccountBalanceType.SPOT
        )
        record.price = quantize_amount(kwargs['price'], PrecisionEnum.PRICE_PLACES)
        db.session.add(record)
        db.session.add(AssetCostUpdateHistory(
            user_id=user_id,
            asset=asset,
            price=record.price,
        ))
        balance = ServerClient().get_user_balances(user_id, asset)
        amount = balance.get(asset, {}).get("available", 0) + balance.get(asset, {}).get("frozen", 0)
        record: AssetProfitLossHistory = AssetProfitLossHistory.get_or_create(user_id=user_id, 
                                                                              asset=asset, 
                                                                              report_date=today())
        asset_price = PriceManager.asset_to_usd(asset)
        record.price = quantize_amount(kwargs['price'], PrecisionEnum.PRICE_PLACES)
        record.profit = quantize_amount((asset_price - record.price) * amount, PrecisionEnum.CASH_PLACES)
        record.type = AssetProfitLossHistory.Type.USER
        record.amount = amount
        db.session.add(record)
        db.session.commit()

