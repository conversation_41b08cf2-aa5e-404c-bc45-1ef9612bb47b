# -*- coding: utf-8 -*-
from enum import Enum
from typing import Any
from webargs import fields
from collections import defaultdict
from decimal import Decimal
from datetime import timed<PERSON>ta

from flask import g, send_file

from app.caches.mission import MissionCache
from app.common.constants import ADMIN_EXPORT_LIMIT, Language, BusinessParty
from app.models import db, SubAccount, User
from app.models.investment import AssetInvestmentConfig
from app.models.mission_center import UserMission, Mission
from app.models.user import UserBizTag
from app.models.equity_center import (
    EquityType,
    EquityBaseInfo,
    UserEquity,
    UserCashbackEquity,
    EquitySendApply,
    UserCashbackEquityHistory,
    UserCashbackEquityTransferHistory,
    UserAirdropEquity,
    UserAirdropEquityHistory,
    UserCashbackSettlementHistory,
    UserCashbackTradeFeeHistory,
    UserInvestIncreaseEquity, EquityDailyConsumptionDetail, EquitySetting, EquityDailyConsumption,
)
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation
from app.exceptions import InvalidArgument
from app.business import get_admin_user_name_map
from app.business.equity_center.helper import EquitySettings, EquityCenterService, parse_manual_users, \
    EquityDailyConsumptionService
from app.business.mission_center.mission import MissionBiz
from app.business.push_statistic import UserTagGroupBiz, EquitySendApplyUserParser
from app.assets import list_all_assets
from app.api.common import Namespace, Resource, respond_with_code, lock_request
from app.api.common.fields import (
    LimitField,
    PageField,
    EnumField,
    TimestampField,
    PositiveDecimalField,
)
from app.api.common.decorators import require_admin_webauth_token
from app.utils import amount_to_str, export_xlsx, datetime_to_utc8_str, batch_iter, now, ExcelExporter
from sqlalchemy import func


ns = Namespace("Equity-Center-Admin")


class BaseUserEquityResource(Resource):
    """权益列表基类，提取共同功能"""

    equity_type = None  # 子类需要设置
    model = None  # 子类需要设置

    class AdminBusinessType(Enum):
        MISSION = "任务发放"
        AIRDROP_ACTIVITY = "空投活动发放"
        DEPOSIT_BONUS_ACTIVITY = "充值福利活动发放"
        PLATFORM_SEND = "平台发放"

    @classmethod
    def _build_biz_type_ids_mapper(cls, rows: list[UserEquity]) -> dict[UserEquity.BusinessType, list[int]]:
        biz_type_ids_mapper = defaultdict(list)
        for row in rows:
            biz_type_ids_mapper[row.business_type].append(row.business_id)
        return biz_type_ids_mapper

    @classmethod
    def _query_mission_biz_ids(cls, mission_id: int):
        """查询任务业务ID"""

        return {i.id for i in UserMission.query.filter(UserMission.mission_id == mission_id).with_entities(UserMission.id).all()}

    @classmethod
    def _query_airdrop_biz_ids(cls, activity_id: int):
        """查询空投活动业务ID"""
        from app.models.operation import AirdropActivityRewardHistory

        return {
            i.id
            for i in AirdropActivityRewardHistory.query.filter(AirdropActivityRewardHistory.airdrop_activity_id == activity_id)
            .with_entities(AirdropActivityRewardHistory.id)
            .all()
        }

    @classmethod
    def _query_deposit_bonus_biz_ids(cls, deposit_bonus_id: int):
        """查询充值福利活动业务ID"""
        from app.models.operation import DepositBonusActivityUserGiftRow, DepositBonusActivityUserInfo

        user_info_ids = {
            i.id
            for i in DepositBonusActivityUserInfo.query.filter(DepositBonusActivityUserInfo.deposit_bonus_id == deposit_bonus_id)
            .with_entities(DepositBonusActivityUserInfo.id)
            .all()
        }
        biz_ids = {
            i.id
            for i in DepositBonusActivityUserGiftRow.query.filter(DepositBonusActivityUserGiftRow.user_info_id.in_(user_info_ids))
            .with_entities(DepositBonusActivityUserGiftRow.id)
            .all()
        }
        return biz_ids

    @classmethod
    def _query_airdrop_activity_dict(cls):
        """查询空投活动字典"""
        from app.models.operation import AirdropActivity, AirdropActivityReward

        airdrop_activity_ids = {
            i.airdrop_activity_id
            for i in AirdropActivityReward.query.filter(
                AirdropActivityReward.type == AirdropActivityReward.Type.EQUITY,
            )
            .with_entities(
                AirdropActivityReward.airdrop_activity_id,
            )
            .all()
        }
        return {
            i.id: f"{i.id} {i.name}"
            for i in AirdropActivity.query.filter(
                AirdropActivity.id.in_(airdrop_activity_ids),
            )
            .with_entities(AirdropActivity.id, AirdropActivity.name)
            .all()
        }

    @classmethod
    def _query_deposit_bonus_dict(cls):
        """查询充值福利活动字典"""
        from app.models.operation import DepositBonusActivity, DepositBonusActivityUserInfo, DepositBonusActivityUserGiftRow

        user_info_ids = {
            i.user_info_id
            for i in DepositBonusActivityUserGiftRow.query.filter(
                DepositBonusActivityUserGiftRow.gift_type == DepositBonusActivityUserGiftRow.GiftType.EQUITY,
            )
            .with_entities(
                DepositBonusActivityUserGiftRow.user_info_id,
            )
            .all()
        }
        activity_ids = {
            i.deposit_bonus_id
            for i in DepositBonusActivityUserInfo.query.filter(
                DepositBonusActivityUserInfo.id.in_(user_info_ids),
            )
            .with_entities(
                DepositBonusActivityUserInfo.deposit_bonus_id,
            )
            .all()
        }
        return {
            i.id: f"{i.id} {i.name}"
            for i in DepositBonusActivity.query.filter(
                DepositBonusActivity.id.in_(activity_ids),
            ).all()
        }

    @classmethod
    def _query_mission_dict(cls):
        """查询任务字典"""
        from app.caches.mission import MissionCache

        return {k: f"{k} {MissionBiz.build_title(v)}" for k, v in MissionCache.get_all_cache_data().items()}

    @classmethod
    def _query_platform_send_dict(cls, equity_type: EquityType):
        """查询平台发放字典"""
        from app.models.equity_center import EquitySendApply

        query = EquitySendApply.query
        if equity_type:
            query = query.filter(EquitySendApply.equity_type == equity_type)

        return {i.id: f"{i.id} {i.title}" for i in query.with_entities(EquitySendApply.id, EquitySendApply.title).all()}

    @classmethod
    def build_selector_option(cls):
        """构建选择器选项"""
        option = []
        for i in cls.AdminBusinessType:
            base_dict = {
                "label": i.value,
                "value": i.name,
            }
            match i:
                case cls.AdminBusinessType.MISSION:
                    base_dict["children"] = [
                        {
                            "label": v,
                            "value": f"{i.name}-{k}",
                        }
                        for k, v in cls._query_mission_dict().items()
                    ]
                case cls.AdminBusinessType.AIRDROP_ACTIVITY:
                    base_dict["children"] = [
                        {
                            "label": v,
                            "value": f"{i.name}-{k}",
                        }
                        for k, v in cls._query_airdrop_activity_dict().items()
                    ]
                case cls.AdminBusinessType.DEPOSIT_BONUS_ACTIVITY:
                    base_dict["children"] = [
                        {
                            "label": v,
                            "value": f"{i.name}-{k}",
                        }
                        for k, v in cls._query_deposit_bonus_dict().items()
                    ]
                case cls.AdminBusinessType.PLATFORM_SEND:
                    base_dict["children"] = [
                        {
                            "label": v,
                            "value": f"{i.name}-{k}",
                        }
                        for k, v in cls._query_platform_send_dict(cls.equity_type).items()
                    ]
            option.append(base_dict)
        return option

    @classmethod
    def _apply_source_filter(cls, q, source_value: str):
        source_type, source_id = source_value.split("-")
        match source_type:
            case cls.AdminBusinessType.MISSION.name:
                biz_ids = cls._query_mission_biz_ids(source_id)
                q = q.filter(
                    UserEquity.business_id.in_(biz_ids),
                    UserEquity.business_type == UserEquity.BusinessType.MISSION,
                )
            case cls.AdminBusinessType.AIRDROP_ACTIVITY.name:
                biz_ids = cls._query_airdrop_biz_ids(source_id)
                q = q.filter(
                    UserEquity.business_id.in_(biz_ids),
                    UserEquity.business_type == UserEquity.BusinessType.AIRDROP_ACTIVITY,
                )
            case cls.AdminBusinessType.DEPOSIT_BONUS_ACTIVITY.name:
                biz_ids = cls._query_deposit_bonus_biz_ids(source_id)
                q = q.filter(
                    UserEquity.business_id.in_(biz_ids),
                    UserEquity.business_type == UserEquity.BusinessType.DEPOSIT_BONUS_ACTIVITY,
                )
            case cls.AdminBusinessType.PLATFORM_SEND.name:
                q = q.filter(
                    UserEquity.business_id == source_id,
                    UserEquity.business_type == UserEquity.BusinessType.PLATFORM_SEND,
                )
        return q

    @classmethod
    def _apply_common_filters(cls, q, **kwargs):
        """应用通用过滤条件"""
        if user_id := kwargs.get("user_id"):
            q = q.filter(UserEquity.user_id == user_id)
        if status := kwargs.get("status"):
            q = q.filter(UserEquity.status == status)
        else:
            q = q.filter(UserEquity.status != UserEquity.Status.FAILED)
        if equity_id := kwargs.get("equity_id"):
            q = q.filter(UserEquity.equity_id == equity_id)
        if business_type := kwargs.get("business_type"):
            q = q.filter(UserEquity.business_type == business_type)
        if start_time := kwargs.get("start_time"):
            q = q.filter(UserEquity.created_at >= start_time)
        if end_time := kwargs.get("end_time"):
            q = q.filter(UserEquity.created_at <= end_time)
        if source_value := kwargs.get("source_value"):
            q = cls._apply_source_filter(q, source_value)
        return q

    @classmethod
    def _get_common_columns(cls):
        """获取通用列"""
        return [
            UserEquity.equity_id,
            UserEquity.type,
            UserEquity.business_id,
            UserEquity.business_type,
            UserEquity.status.label("base_status"),
            UserEquity.finished_at,
        ]

    @classmethod
    def _get_mission_info(cls, rows):
        """获取任务信息"""
        mis_biz_ids = {i.business_id for i in rows if i.business_type == UserEquity.BusinessType.MISSION}
        mis_biz_info_map = {}
        if mis_biz_ids:
            mis_biz_info_map = MissionBiz.query_user_mission_info(list(mis_biz_ids))
        return mis_biz_info_map

    @classmethod
    def _get_equity_base_info(cls, equity_ids: list[int]):
        """获取权益基础信息"""
        equity_base_info_map = {}
        if equity_ids:
            eq_base_info_rows = (
                EquityBaseInfo.query.filter(
                    EquityBaseInfo.id.in_(equity_ids),
                )
                .with_entities(
                    EquityBaseInfo.id,
                    EquityBaseInfo.extra_data,
                )
                .all()
            )
            equity_base_info_map = {i.id: i for i in eq_base_info_rows}
        return equity_base_info_map


@ns.route("/settings")
@respond_with_code
class EquitySettingsResource(Resource):
    @classmethod
    def get(cls):
        """权益中心-获取全局配置"""
        return {
            "items": EquitySettings.fields_and_values_json,
        }

    @classmethod
    @ns.use_kwargs(dict(settings=fields.Dict(required=True)))
    def post(cls, **kwargs):
        """权益中心-修改全局配置"""
        settings = kwargs["settings"]
        for key, value in settings.items():
            try:
                setattr(EquitySettings, key, value)
            except (AttributeError, ValueError) as e:
                raise InvalidArgument(message=f"{e!r}")


@ns.route("/settings/<field>")
@respond_with_code
class EquitySettingsManagementResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(value=fields.Raw(required=True)))
    def put(cls, field, **kwargs):
        """权益中心-编辑全局配置"""
        value = kwargs["value"]

        try:
            setattr(EquitySettings, field, value)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f"{e!r}")
        new_value = getattr(EquitySettings, field)

        return dict(
            value=new_value,
        )

    @classmethod
    def delete(cls, field):
        """权益中心-重置全局配置"""
        try:
            delattr(EquitySettings, field)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f"{e!r}")
        new_value = getattr(EquitySettings, field)

        return dict(
            value=new_value,
        )


@ns.route("/cashback/list")
@respond_with_code
class CashbackEquityListResource(BaseUserEquityResource):
    equity_type = EquityType.CASHBACK
    model = EquityBaseInfo
    COST_ASSETS = ["USDT", "USDC"]

    @classmethod
    def get_equity_extra_data(cls):
        cost_amount_dict, effective_days_list = {}, []
        for i in (
            cls.model.query.filter(cls.model.type == cls.equity_type)
            .with_entities(
                cls.model.id,
                cls.model.extra_data,
                cls.model.cost_asset,
                cls.model.cost_amount,
            )
            .all()
        ):
            cost_amount_dict[amount_to_str(i.cost_amount)] = f"{amount_to_str(i.cost_amount)} {i.cost_asset}"
            effective_days_list.append(i.extra_data["effective_days"])
        return cost_amount_dict, effective_days_list

    @classmethod
    @ns.use_kwargs(
        dict(
            equity_id=fields.Integer(),
            status=EnumField(model.Status),
            cost_amount=fields.Decimal(),
            effective_days=fields.Integer(),
            cashback_scope=EnumField(UserCashbackEquity.CashbackScope),
            cashback_asset=fields.String(),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """权益中心-返现权益列表"""
        model = cls.model
        q = model.query.filter(
            model.type == cls.equity_type,
        ).order_by(model.id.desc())
        if equity_id := kwargs.get("equity_id"):
            q = q.filter(model.id == equity_id)
        if status := kwargs.get("status"):
            q = q.filter(model.status == status)
        if cost_amount := kwargs.get("cost_amount"):
            q = q.filter(model.cost_amount == cost_amount)
        if effective_days := kwargs.get("effective_days"):
            q = q.filter(func.json_extract(model.extra_data, "$.effective_days") == effective_days)
        if cashback_scope := kwargs.get("cashback_scope"):
            q = q.filter(func.json_extract(model.extra_data, "$.cashback_scope") == cashback_scope.name)
        if cashback_asset := kwargs.get("cashback_asset"):
            q = q.filter(func.json_extract(model.extra_data, "$.cashback_asset") == cashback_asset)

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        rows: list[model] = pagination.items

        creators = [i.creator for i in rows]
        creator_name_map = get_admin_user_name_map(creators)

        items = []
        extra_fields = ["cashback_scope", "cashback_asset", "effective_days", "cashback_ratio"]
        for r in rows:
            item = r.to_dict(enum_to_name=True)
            extra_data = r.extra_data
            cashback_ratio = extra_data["cashback_ratio"]
            extra_data["cashback_ratio"] = Decimal(cashback_ratio) * Decimal(100)
            item.update(extra_data)
            for fie in extra_fields:
                if fie not in item:
                    item[fie] = ""
            item["extra_data"] = extra_data
            item["creator_name"] = creator_name_map.get(r.creator)
            items.append(item)

        cost_amount_dict, effective_days_list = cls.get_equity_extra_data()
        return dict(
            items=items,
            total=total,
            extra=dict(
                equity_type_dict=EquityType,
                status_dict=EquityBaseInfo.Status,
                cashback_scope_dict=UserCashbackEquity.CashbackScope,
                all_assets=EquitySetting.BALANCE_ASSETS,
                cost_assets=cls.COST_ASSETS,
                all_equity_dict=EquityCenterService.get_all_equity_dict(),
                effective_days=set(effective_days_list),
                cost_amount_dict=cost_amount_dict,
            ),
        )

    @classmethod
    @lock_request()
    @ns.use_kwargs(
        dict(
            cashback_scope=EnumField(UserCashbackEquity.CashbackScope, required=True),
            cashback_asset=fields.String(required=True),
            cashback_ratio=fields.Float(required=True, validate=lambda x: 1 <= x <= 100),
            cost_asset=fields.String(required=True),
            cost_amount=PositiveDecimalField(required=True),
            effective_days=fields.Integer(required=True, validate=lambda x: 1 <= x <= 60),
            remark=fields.String,
        )
    )
    def post(cls, **kwargs):
        """权益中心-返现权益-创建"""
        cashback_asset = kwargs["cashback_asset"]
        if cashback_asset not in EquitySetting.BALANCE_ASSETS:
            raise InvalidArgument(f"返现币种{cashback_asset}不存在")
        cost_asset = kwargs["cost_asset"]
        if cost_asset not in cls.COST_ASSETS:
            raise InvalidArgument(f"返现价值币种当前只支持{','.join(cls.COST_ASSETS)}")

        cost_amount = kwargs["cost_amount"]
        cashback_ratio = Decimal(kwargs["cashback_ratio"])
        extra_data = dict(
            cashback_scope=kwargs["cashback_scope"].name,
            cashback_asset=cashback_asset,
            cashback_ratio=cashback_ratio / Decimal(100),
            effective_days=kwargs["effective_days"],
        )

        exist_base_eqs: list[EquityBaseInfo] = EquityBaseInfo.query.filter(
            EquityBaseInfo.type == cls.equity_type,
            EquityBaseInfo.cost_asset == cost_asset,
            EquityBaseInfo.cost_amount == cost_amount,
        ).all()
        for exist_base_eq in exist_base_eqs:
            if exist_base_eq.extra_data == extra_data:
                raise InvalidArgument(message=f"已存在重复权益数据 权益ID：{exist_base_eq.id}")

        base_eq = EquityBaseInfo(
            type=cls.equity_type,
            creator=g.user.id,
            remark=kwargs.get("remark", ""),
            cost_asset=cost_asset,
            cost_amount=cost_amount,
            extra_data=extra_data,
        )
        db.session.add(base_eq)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CashbackEquity,
            detail=base_eq.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            status=EnumField(model.Status, required=True),
        )
    )
    def patch(cls, **kwargs):
        """权益中心-返现权益-禁用|启用"""
        model = cls.model
        id_ = kwargs["id"]
        row: model = model.query.filter(model.id == id_).first()
        if not row:
            raise InvalidArgument(message=f"返现权益{id_}不存在")
        old_data = row.to_dict(enum_to_name=True)

        row.status = kwargs["status"]
        db.session.add(row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.CashbackEquity,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route("/cashback/user-equity")
@respond_with_code
class UserCashbackEquityListResource(BaseUserEquityResource):
    equity_type = EquityType.CASHBACK

    export_headers = (
        {"field": "user_equity_id", Language.ZH_HANS_CN: "发放ID"},
        {"field": "business_type", Language.ZH_HANS_CN: "权益类型"},
        {"field": "plan_id", Language.ZH_HANS_CN: "推送ID"},
        {"field": "mission_id", Language.ZH_HANS_CN: "任务ID"},
        {"field": "equity_id", Language.ZH_HANS_CN: "权益ID"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "created_at", Language.ZH_HANS_CN: "发放时间(UTC+8)"},
        {"field": "cost_amount", Language.ZH_HANS_CN: "返现价值"},
        {"field": "used_cost_amount", Language.ZH_HANS_CN: "累计返现额度"},
        {"field": "cashback_amount", Language.ZH_HANS_CN: "累计返现额度"},
        {"field": "status", Language.ZH_HANS_CN: "使用权益状态"},
        {"field": "updated_at", Language.ZH_HANS_CN: "更新时间(UTC+8)"},
    )

    @classmethod
    def _get_equity_statistics(cls, rows: list[UserCashbackEquity], user_info_mapper: dict[str, Any]):
        user_set = set()
        count = 0
        cost_amount_mapper = defaultdict(Decimal)
        real_cashback_mapper = defaultdict(Decimal)
        real_cost_amount_mapper = defaultdict(Decimal)
        status_mapper = defaultdict(int)
        for row in rows:
            user_info_dict = user_info_mapper[row.id]
            user_set.add(row.user_id)
            count += 1
            status_mapper[row.status.name] += 1
            cost_amount_mapper[row.cost_asset] += row.cost_amount
            real_cashback_mapper[row.cashback_asset] += user_info_dict["real_cashback_amount"]
            real_cost_amount_mapper[row.cost_asset] += user_info_dict["real_cost_amount"]

        return {
            "user_count": len(user_set),
            "equity_count": len(rows),
            "cost_amount_mapper": cost_amount_mapper,
            "real_cashback_mapper": real_cashback_mapper,
            "real_cost_amount_list": real_cost_amount_mapper,
            "status_mapper": status_mapper,
        }

    @classmethod
    def _query_user_equity_data(cls, user_equity_rows: list[UserCashbackEquity]):
        user_equity_ids = {i.id for i in user_equity_rows}
        pending_tran_rows = []
        for batch_ids in batch_iter(user_equity_ids, 5000):
            pending_tran_rows.extend(
                UserCashbackEquityTransferHistory.query.filter(
                    UserCashbackEquityTransferHistory.user_equity_id.in_(batch_ids),
                    UserCashbackEquityTransferHistory.status != UserCashbackEquityTransferHistory.Status.FINISHED,
                )
                .with_entities(
                    UserCashbackEquityTransferHistory.id,
                    UserCashbackEquityTransferHistory.user_equity_id,
                    UserCashbackEquityTransferHistory.his_ids,
                )
                .all()
            )
        eq_pending_tran_rows_map = defaultdict(list)
        pending_detail_ids = set()
        for r in pending_tran_rows:
            eq_pending_tran_rows_map[r.user_equity_id].append(r)
            pending_detail_ids.update(r.his_ids)
        detail_rows = []
        for ch_d_ids in batch_iter(pending_detail_ids, 5000):
            ch_detail_rows = UserCashbackEquityHistory.query.filter(
                UserCashbackEquityHistory.id.in_(ch_d_ids),
            ).all()
            detail_rows.extend(ch_detail_rows)
        detail_row_map = {i.id: i for i in detail_rows}

        eq_pending_cashback_amount_map = defaultdict(Decimal)
        eq_pending_cost_amount_map = defaultdict(Decimal)
        for r in pending_tran_rows:
            for did in r.his_ids:
                pd = detail_row_map.get(did)
                if pd:
                    eq_pending_cashback_amount_map[r.id] += pd.delta_cashback_amount
                    eq_pending_cost_amount_map[r.id] += pd.delta_cost_amount
        user_equity_info_map = {}
        for row in user_equity_rows:
            eq_pending_tran_rows = eq_pending_tran_rows_map[row.user_equity_id]
            pending_cashback_amount = sum([eq_pending_cashback_amount_map[i.id] for i in eq_pending_tran_rows])
            pending_cost_amount = sum([eq_pending_cost_amount_map[i.id] for i in eq_pending_tran_rows])
            user_equity_info_map[row.id] = {
                "pending_cashback_amount": pending_cashback_amount,
                "real_cashback_amount": row.cashback_amount - pending_cashback_amount,
                "pending_cost_amount": pending_cost_amount,
                "real_cost_amount": row.used_cost_amount - pending_cost_amount,
            }
        return user_equity_info_map

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer,
            status=EnumField(UserCashbackEquity.Status),
            equity_id=fields.Integer,
            business_type=EnumField(UserEquity.BusinessType),
            source_value=fields.String,
            start_time=TimestampField,
            end_time=TimestampField,
            page=PageField,
            limit=LimitField,
            export=fields.Boolean,
        )
    )
    def get(cls, **kwargs):
        """权益中心-权益发放-返现权益发放列表"""
        q = UserCashbackEquity.query.join(
            UserEquity,
            UserCashbackEquity.user_equity_id == UserEquity.id,
        ).order_by(UserCashbackEquity.id.desc())
        q = cls._apply_common_filters(q, **kwargs)

        cols = [
            UserCashbackEquity.created_at,
            UserCashbackEquity.updated_at,
            UserCashbackEquity.id,
            UserCashbackEquity.user_id,
            UserCashbackEquity.user_equity_id,
            UserCashbackEquity.start_time,
            UserCashbackEquity.end_time,
            UserCashbackEquity.status,
            UserCashbackEquity.cost_asset,
            UserCashbackEquity.cost_amount,
            UserCashbackEquity.cashback_scope,
            UserCashbackEquity.cashback_asset,
            UserCashbackEquity.cashback_amount,
            UserCashbackEquity.used_cost_amount,
            UserCashbackEquity.last_cashback_at,
            *cls._get_common_columns(),
        ]
        q = q.with_entities(*cols)

        is_export = kwargs.get("export")
        if is_export:
            rows = q.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(rows)
        else:
            pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
            total = pagination.total
            rows = pagination.items

        biz_source_id_mapper = EquityDailyConsumptionService.query_biz_source_id_mapper(cls._build_biz_type_ids_mapper(rows))
        query_all_rows = q.all()
        user_equity_info_map = cls._query_user_equity_data(query_all_rows)

        mis_biz_info_map = cls._get_mission_info(rows)
        enum_cols = ["type", "status", "base_status", "business_type", "cashback_scope"]
        items = []
        for i in rows:
            d = dict([k, getattr(i, k)] for k in i._fields)  # noqa
            for c in enum_cols:
                d[c] = d[c].name
            d["plan_id"] = mis_biz_info_map.get(i.business_id, {}).get("plan_id", 0)
            d["mission_id"] = mis_biz_info_map.get(i.business_id, {}).get("mission_id", 0)
            d["scene_type"] = mis_biz_info_map.get(i.business_id, {}).get("scene_type", 0)
            d["source_id"] = biz_source_id_mapper.get(i.business_id)
            user_equity_info = user_equity_info_map[i.id]
            d.update(user_equity_info)
            items.append(d)

        if is_export:
            for d in items:
                d["created_at"] = datetime_to_utc8_str(d["created_at"])
                d["updated_at"] = datetime_to_utc8_str(d["updated_at"])
                d["cost_amount"] = f"{amount_to_str(d['cost_amount'])} {d['cost_asset']}"
                d["used_cost_amount"] = f"{amount_to_str(d['used_cost_amount'] - d['pending_cost_amount'])} {d['cost_asset']}"
                d["cashback_amount"] = f"{amount_to_str(d['cashback_amount'] - d['pending_cashback_amount'])} {d['cashback_asset']}"
                d["business_type"] = BaseUserEquityResource.AdminBusinessType[d["business_type"]].value
                d["status"] = UserCashbackEquity.Status[d["status"]].value

            return export_xlsx(
                filename="user-cashback-equity-list",
                data_list=items,
                export_headers=cls.export_headers,
            )

        status_dict = {i.name: i.value for i in UserCashbackEquity.Status if i != UserCashbackEquity.Status.FAILED}
        return dict(
            items=items,
            total=total,
            extra=dict(
                business_type_dict=BaseUserEquityResource.AdminBusinessType,
                status_dict=status_dict,
                all_equity_dict=EquityCenterService.get_all_equity_dict(cls.equity_type),
                cashback_scope_dict=UserCashbackEquity.CashbackScope,
                # equity_statistics=cls._get_equity_statistics(query_all_rows, user_equity_info_map),
                equity_statistics=dict(
                    status_mapper={s.name: 0 for s in UserCashbackEquity.Status},
                ),
                selector_option=cls.build_selector_option(),
            ),
        )


@ns.route("/cashback/user-equity-history")
@respond_with_code
class UserCashbackEquityHistoryResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            user_equity_id=fields.Integer,
            apply_id=fields.Integer,  # 发放ID
            status=EnumField(UserCashbackEquityTransferHistory.Status),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """权益中心-权益发放-返现权益发放明细"""
        q = UserCashbackEquityTransferHistory.query.order_by(UserCashbackEquityTransferHistory.id.desc())

        # 如果提供了发放ID，先查询对应的user_equity_id
        if apply_id := kwargs.get("apply_id"):
            # 通过发放ID查询对应的用户权益记录
            user_equity_ids = (
                UserEquity.query.filter(
                    UserEquity.business_id == apply_id, UserEquity.business_type == UserEquity.BusinessType.PLATFORM_SEND
                )
                .with_entities(UserEquity.id)
                .all()
            )
            user_equity_id_list = [ue.id for ue in user_equity_ids]
            q = q.filter(UserCashbackEquityTransferHistory.user_equity_id.in_(user_equity_id_list))

        if user_equity_id := kwargs.get("user_equity_id"):
            q = q.filter(UserCashbackEquityTransferHistory.user_equity_id == user_equity_id)
        if status := kwargs.get("status"):
            q = q.filter(UserCashbackEquityTransferHistory.status == status)

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        rows = pagination.items

        user_equity_ids = {i.user_equity_id for i in rows}
        user_eqs = UserEquity.query.filter(
            UserEquity.id.in_(user_equity_ids),
        ).all()
        user_eq_map = {i.id: i for i in user_eqs}

        detail_ids = set()
        for r in rows:
            detail_ids.update(r.his_ids)
        pending_tran_rows = (
            UserCashbackEquityTransferHistory.query.filter(
                UserCashbackEquityTransferHistory.user_equity_id.in_(user_equity_ids),
                UserCashbackEquityTransferHistory.status != UserCashbackEquityTransferHistory.Status.FINISHED,
            )
            .with_entities(
                UserCashbackEquityTransferHistory.id,
                UserCashbackEquityTransferHistory.user_equity_id,
                UserCashbackEquityTransferHistory.his_ids,
            )
            .all()
        )
        eq_pending_tran_rows_map = defaultdict(list)
        pending_detail_ids = set()
        for r in pending_tran_rows:
            eq_pending_tran_rows_map[r.user_equity_id].append(r)
            pending_detail_ids.update(r.his_ids)

        q_details = detail_ids | pending_detail_ids
        detail_rows = UserCashbackEquityHistory.query.filter(
            UserCashbackEquityHistory.id.in_(q_details),
        ).all()
        detail_row_map = {i.id: i for i in detail_rows}

        eq_pending_cashback_amount_map = defaultdict(Decimal)
        eq_pending_cost_amount_map = defaultdict(Decimal)
        for r in pending_tran_rows:
            for did in r.his_ids:
                pd = detail_row_map.get(did)
                if pd:
                    eq_pending_cashback_amount_map[r.id] += pd.delta_cashback_amount
                    eq_pending_cost_amount_map[r.id] += pd.delta_cost_amount

        items = []
        for i in rows:
            d = i.to_dict(enum_to_name=True)
            user_eq = user_eq_map.get(i.user_equity_id)
            d["equity_id"] = user_eq.equity_id if user_eq else 0

            d["cashback_asset"] = ""
            d["cost_asset"] = ""
            d["settle_at"] = i.created_at
            d["delta_cashback_amount"] = 0
            d["delta_cost_amount"] = 0
            d["total_cashback_amount"] = 0
            d["total_used_cost_amount"] = 0
            d["details"] = []
            for detail_id in i.his_ids:
                detail_r: UserCashbackEquityHistory = detail_row_map.get(detail_id)
                if detail_r:
                    d["details"].append(detail_r.to_dict(enum_to_name=True))
                    d["cashback_asset"] = detail_r.cashback_asset
                    d["cost_asset"] = detail_r.cost_asset
                    d["settle_at"] = detail_r.settle_at
                    d["delta_cashback_amount"] += detail_r.delta_cashback_amount
                    d["delta_cost_amount"] += detail_r.delta_cost_amount
                    d["total_used_cost_amount"] = max(detail_r.total_used_cost_amount, d["total_used_cost_amount"])
                    d["total_cashback_amount"] = max(detail_r.total_cashback_amount, d["total_cashback_amount"])
            eq_pending_tran_rows = eq_pending_tran_rows_map[i.user_equity_id]
            ago_pending_rows = [p for p in eq_pending_tran_rows if p.id <= i.id]
            pending_cashback_amount = sum([eq_pending_cashback_amount_map[i.id] for i in ago_pending_rows])
            d["pending_cashback_amount"] = pending_cashback_amount
            d["real_cashback_amount"] = d["total_cashback_amount"] - pending_cashback_amount
            pending_cost_amount = sum([eq_pending_cost_amount_map[i.id] for i in ago_pending_rows])
            d["pending_cost_amount"] = pending_cost_amount
            d["real_cost_amount"] = d["total_used_cost_amount"] - pending_cost_amount
            items.append(d)

        return dict(
            items=items,
            total=total,
            extra=dict(
                status_dict=UserCashbackEquityTransferHistory.Status,
            ),
        )


@ns.route("/invest-increase/list")
@respond_with_code
class InvestIncreaseEquityListResource(Resource):
    equity_type = EquityType.INVEST_INCREASE
    model = EquityBaseInfo

    @classmethod
    @ns.use_kwargs(
        dict(
            equity_id=fields.Integer(),
            status=EnumField(model.Status),
            asset=fields.String,
            increase_rate=fields.Decimal,
            principal_amount_limit=fields.Decimal,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """权益中心-理财加息权益列表"""
        model = cls.model
        q = model.query.filter(
            model.type == cls.equity_type,
        ).order_by(model.id.desc())

        if equity_id := kwargs.get("equity_id"):
            q = q.filter(model.id == equity_id)
        if status := kwargs.get("status"):
            q = q.filter(model.status == status)
        asset = kwargs.get("asset")
        increase_rate = kwargs.get("increase_rate")
        principal_amount_limit = kwargs.get("principal_amount_limit")
        page = kwargs.get("page")
        limit = kwargs.get("limit")

        if any([asset, increase_rate, principal_amount_limit]):
            valid_rows = []
            for row in q.all():
                extra = row.extra_data
                is_asset = True if not asset or asset in extra.get("assets", []) else False
                is_increase_rate = (
                    True if not increase_rate or Decimal(str(extra.get("increase_rate"))) * 100 == Decimal(increase_rate) else False
                )
                is_principal_amount_limit = (
                    True if not principal_amount_limit or Decimal(extra.get("principal_amount_limit")) == principal_amount_limit else False
                )
                if all([is_asset, is_increase_rate, is_principal_amount_limit]):
                    valid_rows.append(row)
            total = len(valid_rows)
            rows = valid_rows[(page - 1) * limit : page * limit - 1]
        else:
            pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
            total = pagination.total
            rows: list[model] = pagination.items

        creators = [i.creator for i in rows]
        creator_name_map = get_admin_user_name_map(creators)

        items = []
        for r in rows:
            item = r.to_dict(enum_to_name=True)
            item.update(r.extra_data)
            item["creator_name"] = creator_name_map.get(r.creator)
            items.append(item)

        return dict(
            items=items,
            total=total,
            extra=dict(
                equity_type_dict=EquityType,
                status_dict=EquityBaseInfo.Status,
                all_assets=list(AssetInvestmentConfig.get_valid_assets()),  # 支持的币种
                all_equity_dict=EquityCenterService.get_all_equity_dict(cls.equity_type),
            ),
        )

    @classmethod
    @lock_request()
    @ns.use_kwargs(
        dict(
            increase_rate=fields.Decimal(required=True, validate=lambda x: 0.1 <= x <= 500),
            principal_amount_limit=PositiveDecimalField(required=True),
            assets=fields.List(fields.String, required=True),
            activation_days=fields.Integer(required=True, validate=lambda x: 1 <= x <= 90),
            usable_days=fields.Integer(required=True, validate=lambda x: 1 <= x <= 90),
            total_interest=PositiveDecimalField(required=True),
            remark=fields.String,
        )
    )
    def post(cls, **kwargs):
        """权益中心-理财加息权益-创建"""
        assets = kwargs["assets"]
        valid_assets = ["CET", "USDT", "BTC", "ETH"]
        for asset in assets:
            if asset not in valid_assets:
                raise InvalidArgument(f"适用币种{asset}不支持，支持的币种：{valid_assets}")

        increase_rate = Decimal(kwargs["increase_rate"]) / Decimal("100")
        principal_amount_limit = kwargs["principal_amount_limit"]
        activation_days = kwargs["activation_days"]
        usable_days = kwargs["usable_days"]
        principal_asset = UserInvestIncreaseEquity.DEFAULT_PRINCIPAL_ASSET
        extra_data = dict(
            increase_rate=amount_to_str(increase_rate, 2),
            principal_asset=principal_asset,
            principal_amount_limit=principal_amount_limit,
            assets=assets,
            activation_days=activation_days,
            usable_days=usable_days,
        )

        # 检查是否存在重复权益
        exist_base_eqs: list[EquityBaseInfo] = EquityBaseInfo.query.filter(
            EquityBaseInfo.type == cls.equity_type,
            EquityBaseInfo.cost_asset == principal_asset,
            EquityBaseInfo.cost_amount == principal_amount_limit,
        ).all()

        for exist_base_eq in exist_base_eqs:
            if exist_base_eq.extra_data == extra_data:
                raise InvalidArgument(message=f"已存在重复权益数据 权益ID：{exist_base_eq.id}")
        base_eq = EquityBaseInfo(
            type=cls.equity_type,
            creator=g.user.id,
            remark=kwargs.get("remark", ""),
            cost_asset=principal_asset,
            cost_amount=kwargs["total_interest"],
            extra_data=extra_data,
        )
        db.session.add(base_eq)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.InvestIncreaseEquity,
            detail=base_eq.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            status=EnumField(model.Status, required=True),
        )
    )
    def patch(cls, **kwargs):
        """权益中心-理财加息权益-禁用|启用"""
        model = cls.model
        id_ = kwargs["id"]
        row: model = model.query.filter(model.id == id_).first()
        if not row:
            raise InvalidArgument(message=f"理财加息权益{id_}不存在")
        old_data = row.to_dict(enum_to_name=True)

        row.status = kwargs["status"]
        db.session.add(row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.InvestIncreaseEquity,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route("/invest-increase/user-equity")
@respond_with_code
class InvestIncreaseEquityUserListResource(BaseUserEquityResource):
    """理财加息权益使用记录"""

    equity_type = EquityType.INVEST_INCREASE
    model = UserInvestIncreaseEquity

    export_headers = (
        {"field": "user_equity_id", Language.ZH_HANS_CN: "发放ID"},
        {"field": "business_type", Language.ZH_HANS_CN: "权益类型"},
        {"field": "plan_id", Language.ZH_HANS_CN: "推送ID"},
        {"field": "mission_id", Language.ZH_HANS_CN: "任务ID"},
        {"field": "equity_id", Language.ZH_HANS_CN: "权益ID"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "created_at", Language.ZH_HANS_CN: "发放时间(UTC+8)"},
        {"field": "investment_asset", Language.ZH_HANS_CN: "加息币种"},
        {"field": "increase_rate", Language.ZH_HANS_CN: "加息比例"},
        {"field": "increase_income_amount", Language.ZH_HANS_CN: "实发加息收益"},
        {"field": "status", Language.ZH_HANS_CN: "使用权益状态"},
        {"field": "updated_at", Language.ZH_HANS_CN: "更新时间(UTC+8)"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer,
            status=EnumField(UserEquity.Status),
            equity_id=fields.Integer,
            business_type=EnumField(UserEquity.BusinessType),
            source_value=fields.String,
            start_time=TimestampField(is_ms=True),
            end_time=TimestampField(is_ms=True),
            page=PageField,
            limit=LimitField,
            export=fields.Boolean,
        )
    )
    def get(cls, **kwargs):
        """权益中心-权益发放-理财加息权益发放列表"""
        q = UserInvestIncreaseEquity.query.join(
            UserEquity,
            UserInvestIncreaseEquity.user_equity_id == UserEquity.id,
        ).order_by(UserInvestIncreaseEquity.id.desc())

        # 应用通用过滤条件
        q = cls._apply_common_filters(q, **kwargs)

        cols = [
            UserInvestIncreaseEquity.id,
            UserInvestIncreaseEquity.user_id,
            UserInvestIncreaseEquity.user_equity_id,
            UserInvestIncreaseEquity.investment_asset,
            UserInvestIncreaseEquity.increase_rate,
            UserInvestIncreaseEquity.increase_amount,
            UserInvestIncreaseEquity.active_at,
            UserInvestIncreaseEquity.finished_at,
            UserEquity.created_at,
            UserEquity.updated_at,
            *cls._get_common_columns(),
        ]
        q = q.with_entities(*cols)

        is_export = kwargs.get("export")
        if is_export:
            rows = q.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(rows)
        else:
            pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
            total = pagination.total
            rows = pagination.items

        mis_biz_info_map = cls._get_mission_info(rows)

        # 查询权益基础信息
        equity_ids = {i.equity_id for i in rows}
        equity_base_info_map = cls._get_equity_base_info(equity_ids)

        # 查询统计数据
        equity_statistics = cls._get_equity_statistics(q)
        biz_source_id_mapper = EquityDailyConsumptionService.query_biz_source_id_mapper(cls._build_biz_type_ids_mapper(rows))
        enum_cols = ["type", "base_status", "business_type"]
        items = []
        for i in rows:
            d = dict([k, getattr(i, k)] for k in i._fields)  # noqa
            for c in enum_cols:
                d[c] = d[c].name if d[c] else ""

            # 添加任务相关信息
            d["plan_id"] = mis_biz_info_map.get(i.business_id, {}).get("plan_id", 0)
            d["mission_id"] = mis_biz_info_map.get(i.business_id, {}).get("mission_id", 0)
            d["scene_type"] = mis_biz_info_map.get(i.business_id, {}).get("scene_type", "")
            d["source_id"] = biz_source_id_mapper.get(i.business_id)

            # 添加权益基础信息
            eq_base_info = equity_base_info_map.get(i.equity_id, {})
            d.update(eq_base_info.extra_data)
            items.append(d)

        if is_export:
            for d in items:
                for field in ["created_at", "updated_at", "active_at", "finished_at"]:
                    d[field] = datetime_to_utc8_str(d[field]) if d[field] else "-"
                d["investment_asset"] = d["investment_asset"] or "-"
                d["increase_rate"] = f"{amount_to_str(Decimal(d['increase_rate']) * 100, 2)}%" or "-"
                d["increase_income_amount"] = (
                    f"{amount_to_str(d['increase_income_amount'])} {d['investment_asset']}" if d["investment_asset"] != "-" else "0"
                )
                d["business_type"] = UserEquity.BusinessType[d["business_type"]].value if d["business_type"] else ""
                d["status"] = UserEquity.Status[d["base_status"]].value if d["base_status"] else ""

            return export_xlsx(
                filename="user-invest-increase-equity-list",
                data_list=items,
                export_headers=cls.export_headers,
            )

        status_dict = {i.name: i.value for i in UserEquity.Status if i not in [UserEquity.Status.FAILED, UserEquity.Status.CREATED]}
        return dict(
            items=items,
            total=total,
            extra=dict(
                business_type_dict=cls.AdminBusinessType,
                status_dict=status_dict,
                all_equity_dict=EquityCenterService.get_all_equity_dict(cls.equity_type),
                equity_statistics=equity_statistics,
                selector_option=cls.build_selector_option(),
            ),
        )

    @classmethod
    def _get_equity_statistics(cls, base_query):
        """获取权益统计数据"""
        # 复制查询条件但不包含分页
        stats_query = base_query.with_entities(
            func.count(UserInvestIncreaseEquity.id).label("equity_count"),
            func.count(func.distinct(UserInvestIncreaseEquity.user_id)).label("user_count"),
            UserEquity.status.label("status"),
        ).group_by(UserEquity.status)

        stats_rows = stats_query.all()

        # 初始化统计数据
        equity_count = 0
        status_mapper = defaultdict(int)

        for row in stats_rows:
            equity_count += row.equity_count or 0
            if row.status:
                status_mapper[row.status.name] = equity_count

        # 获取去重用户数（需要单独查询）
        unique_user_query = base_query.with_entities(func.count(func.distinct(UserInvestIncreaseEquity.user_id)).label("unique_user_count"))
        unique_user_result = unique_user_query.first()
        user_count = unique_user_result.unique_user_count or 0

        # 按币种统计加息金额
        total_increase_usd = base_query.with_entities(func.sum(UserInvestIncreaseEquity.increase_usd).label("total_amount_usd")).scalar()

        return {
            "equity_count": equity_count,
            "user_count": user_count,
            "total_increase_usd": total_increase_usd,
            "status_mapper": status_mapper,
        }


@ns.route("/relation-history")
@respond_with_code
class EquityRelationHisResource(Resource):
    RELATION_MAP = {
        "user_airdrop_equity": UserAirdropEquity,
        "user_airdrop_equity_his": UserAirdropEquityHistory,
        "cashback_equity_his": UserCashbackEquityHistory,
        "cashback_settlement_his": UserCashbackSettlementHistory,
        "cashback_trade_fee_his": UserCashbackTradeFeeHistory,
        "user_biz_tag": UserBizTag,
    }

    @classmethod
    @ns.use_kwargs(
        dict(
            history_type=EnumField(RELATION_MAP),
            start=TimestampField,
            end=TimestampField,
            row_id=fields.Integer,
            user_id=fields.Integer,
            equity_id=fields.Integer,
            main_user_id=fields.Integer,
            user_equity_id=fields.Integer,
            trade_business_id=fields.Integer,
            settle_his_id=fields.Integer,
            settlement_status=EnumField(UserCashbackSettlementHistory.Status),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """权益中心-相关记录"""
        history_type = kwargs["history_type"]
        model = cls.RELATION_MAP[history_type]
        q = model.query.order_by(model.id.desc())
        if row_id := kwargs.get("row_id"):
            q = q.filter(model.id == row_id)
        if user_id := kwargs.get("user_id"):
            if model == UserCashbackTradeFeeHistory:
                q = q.filter(model.trade_user_id == user_id)
            else:
                q = q.filter(model.user_id == user_id)
        if start_time := kwargs.get("start"):
            q = q.filter(model.created_at >= start_time)
        if end_time := kwargs.get("end"):
            q = q.filter(model.created_at <= end_time)
        # special filter
        if model == UserAirdropEquity or model == UserAirdropEquityHistory or model == UserCashbackEquityHistory:
            if user_equity_id := kwargs.get("user_equity_id"):
                q = q.filter(model.user_equity_id == user_equity_id)
            if equity_id := kwargs.get("equity_id"):
                q = q.join(
                    UserEquity,
                    UserEquity.equity_id == equity_id,
                )
        if model == UserCashbackSettlementHistory:
            if settlement_status := kwargs.get("settlement_status"):
                q = q.filter(model.status == settlement_status)
        if model == UserCashbackTradeFeeHistory:
            if trade_business_id := kwargs.get("trade_business_id"):
                q = q.filter(model.trade_business_id == trade_business_id)
            if (settle_his_id := kwargs.get("settle_his_id")) is not None:
                if settle_his_id:
                    q = q.filter(model.settle_his_id == settle_his_id)
                else:
                    q = q.filter(model.settle_his_id.is_(None))
            if main_user_id := kwargs.get("main_user_id"):
                subs = (
                    SubAccount.query.filter(
                        SubAccount.main_user_id == main_user_id,
                    )
                    .with_entities(SubAccount.user_id)
                    .all()
                )
                _q_u_ids = {i.user_id for i in subs} | {main_user_id}
                q = q.filter(model.trade_user_id.in_(_q_u_ids))
        pagination = q.paginate(kwargs["page"], kwargs["limit"])
        total = pagination.total
        rows: list[model] = pagination.items

        items = []
        for r in rows:
            item = r.to_dict(enum_to_name=True)
            items.append(item)

        return dict(
            items=items,
            total=total,
            extra=dict(
                settlement_status_dict=UserCashbackSettlementHistory.Status,
                biz_tag_dict=UserBizTag.BizTag,
                biz_tag_source_dict=UserBizTag.Source,
            ),
        )


@ns.route("/send-apply/list")
@respond_with_code
class EquitySendApplyListResource(Resource):
    @classmethod
    def get_all_apply_title_dict(cls) -> dict[int, str]:
        rows: list[EquitySendApply] = (
            EquitySendApply.query.order_by(EquitySendApply.id.desc())
            .with_entities(EquitySendApply.id, EquitySendApply.send_type, EquitySendApply.title)
            .all()
        )
        return {i.id: f"{i.id} {i.send_type.value} {i.title}" for i in rows}

    @classmethod
    def get_equity_type_eq_info_dict(cls) -> dict[str, dict[int, str]]:
        model = EquityBaseInfo
        rows = (
            model.query.filter(
                model.status == model.Status.OPEN,
            )
            .with_entities(
                model.id,
                model.type,
                model.cost_asset,
                model.cost_amount,
                model.extra_data,
            )
            .all()
        )
        equity_type_eq_info_dict = defaultdict(dict)
        for row in rows:
            row: model
            eq_type = row.type
            if eq_type == EquityType.AIRDROP:
                desc = f"{amount_to_str(row.cost_amount)} {row.cost_asset}({row.id})"
            elif eq_type == EquityType.CASHBACK:
                cashback_scope_str = UserCashbackEquity.CashbackScope[row.extra_data["cashback_scope"]].value
                cashback_ratio = amount_to_str(Decimal(row.extra_data['cashback_ratio']) * Decimal(100))
                cashback_asset = row.extra_data['cashback_asset']
                desc = (f'{amount_to_str(row.cost_amount)} {row.cost_asset} 返 {cashback_asset} '
                        f'{cashback_scope_str}({row.id} {cashback_ratio}%)')
            elif eq_type == EquityType.INVEST_INCREASE:
                increase_rate = amount_to_str(Decimal(row.extra_data["increase_rate"]) * Decimal(100))
                principal_asset = row.extra_data.get("principal_asset", UserInvestIncreaseEquity.DEFAULT_PRINCIPAL_ASSET)
                principal_amount_limit = amount_to_str(row.extra_data.get("principal_amount_limit", 0))
                desc = f"{row.id} {'/'.join(row.extra_data['assets'])} {increase_rate}% {principal_amount_limit}{principal_asset}"
            else:
                continue
            equity_type_eq_info_dict[eq_type.name][row.id] = desc
        return equity_type_eq_info_dict

    @classmethod
    def format_equity_desc(cls, row: EquityBaseInfo):
        if not row:
            return "-"
        if row.type == EquityType.AIRDROP:
            return f"{row.type.value} ({amount_to_str(row.cost_amount)} {row.cost_asset})"
        else:
            return f"{row.type.value} (ID：{row.id})"

    @classmethod
    @ns.use_kwargs(
        dict(
            equity_type=EnumField(EquityType),
            apply_id=fields.Integer,
            equity_id=fields.Integer,
            title=fields.String,
            status=EnumField(EquitySendApply.Status),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """权益中心-权益发放列表"""
        model = EquitySendApply
        q = model.query.order_by(model.id.desc())
        if apply_id := kwargs.get("apply_id"):
            q = q.filter(model.id == apply_id)
        if equity_id := kwargs.get("equity_id"):
            q = q.filter(model.equity_id == equity_id)
        if equity_type := kwargs.get("equity_type"):
            q = q.filter(model.equity_type == equity_type)
        if status := kwargs.get("status"):
            q = q.filter(model.status == status)
        if title := kwargs.get("title"):
            q = q.filter(model.title.contains(title))

        cols = [
            model.id,
            model.created_at,
            model.updated_at,
            model.send_type,
            model.business_party,
            model.title,
            model.equity_id,
            model.equity_type,
            model.total_send_count,
            model.success_send_count,
            model.status,
            model.send_at,
            model.send_finished_at,
            model.creator,
            model.remark,
            model.user_selection_type,
            model.group_user_count,
            model.group_ids,
        ]
        q = q.with_entities(*cols)
        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        rows: list[model] = pagination.items

        creators = [i.creator for i in rows]
        creator_name_map = get_admin_user_name_map(creators)

        equity_ids = {i.equity_id for i in rows}
        eq_base_info_rows = (
            EquityBaseInfo.query.filter(
                EquityBaseInfo.id.in_(equity_ids),
            )
            .with_entities(
                EquityBaseInfo.id,
                EquityBaseInfo.type,
                EquityBaseInfo.cost_asset,
                EquityBaseInfo.cost_amount,
                EquityBaseInfo.extra_data,
            )
            .all()
        )
        equity_basic_info_map = {i.id: i for i in eq_base_info_rows}

        tag_group_ids = set()
        for item in rows:
            tag_group_ids |= set(item.group_ids)
        tag_groups_map = UserTagGroupBiz.get_tag_group_info_dict(tag_group_ids)

        items = []
        for r in rows:
            item = {c.name: getattr(r, c.name) for c in cols}
            item["send_type"] = item["send_type"].name
            item["business_party"] = item["business_party"].name
            item["equity_type"] = item["equity_type"].name
            item["status"] = item["status"].name
            item["user_selection_type"] = item["user_selection_type"].name
            item["creator_name"] = creator_name_map.get(r.creator)
            eq_basic_info = equity_basic_info_map.get(r.equity_id)
            item["cost_asset"] = eq_basic_info.cost_asset if eq_basic_info else ""
            item["cost_amount"] = eq_basic_info.cost_amount if eq_basic_info else Decimal()
            item["equity_desc"] = cls.format_equity_desc(eq_basic_info)
            tag_groups = []
            for group_id in r.group_ids:
                tag_group = tag_groups_map.get(group_id)
                if tag_group:
                    tag_groups.append(tag_group)
            item["tag_groups"] = tag_groups
            items.append(item)

        return dict(
            items=items,
            total=total,
            extra=dict(
                equity_type_dict=EquityType,
                status_dict=model.Status,
                send_type_dict=model.SendType,
                user_selection_type_dict=model.UserSelectionType,
                business_party_dict=BusinessParty,
                equity_type_eq_info_dict=cls.get_equity_type_eq_info_dict(),
                apply_title_dict=cls.get_all_apply_title_dict(),
            ),
        )

    @classmethod
    @lock_request()
    @ns.use_kwargs(
        dict(
            title=fields.String(required=True),
            business_party=EnumField(enum=BusinessParty, required=True),
            send_type=EnumField(enum=EquitySendApply.SendType, required=True),
            send_at=TimestampField(is_ms=True, required=True),
            total_send_count=fields.Integer(required=False, validate=lambda x: x > 0),
            equity_type=EnumField(enum=EquityType, required=True),
            equity_id=fields.Integer(required=False),  # 空投时不传id，传空投币种和数目
            airdrop_asset=fields.String,
            airdrop_amount=fields.Integer(required=False, validate=lambda x: x > 0),
            user_selection_type=EnumField(enum=EquitySendApply.UserSelectionType, required=False),
            manual_user_list=fields.String(required=False),  # 手动输入的用户列表
            groups=fields.List(fields.Integer),
            remark=fields.String,
        )
    )
    def post(cls, **kwargs):
        """权益中心-权益发放-创建"""
        if kwargs["send_at"] < now():
            raise InvalidArgument(message="发放时间不正确，发放时间应晚于当前时间")

        admin_user_id = g.user.id
        eq_info = cls.get_or_create_equity_base_info(admin_user_id, kwargs)

        user_selection_type = kwargs.get("user_selection_type", EquitySendApply.UserSelectionType.TAG_GROUPS)
        manual_user_list = kwargs.pop("manual_user_list", "")
        groups = kwargs.pop("groups", "") or ""

        if user_selection_type == EquitySendApply.UserSelectionType.MANUAL:
            if not manual_user_list:
                raise InvalidArgument(message="手动选择用户时，用户列表不能为空")

            valid_user_ids, invalid_users = parse_manual_users(manual_user_list)
            if invalid_users:
                raise InvalidArgument(message=f"请检查UID/邮箱填写：{', '.join(invalid_users)}")

            if not valid_user_ids:
                raise InvalidArgument(message="未找到有效用户")

            group_ids = []  # 手动模式下不使用圈群
            group_user_ids = valid_user_ids  # 直接使用解析出的用户ID
        else:
            group_ids = UserTagGroupBiz.filter_tag_group_ids(ids=groups) if groups else []
            if not group_ids:
                raise InvalidArgument(message="发放客群为空")

        total_send_count = kwargs.get("total_send_count") or 0
        apply = EquitySendApply(
            send_type=kwargs["send_type"],
            business_party=kwargs["business_party"],
            title=kwargs["title"],
            equity_id=eq_info.id,
            equity_type=eq_info.type,
            total_send_count=total_send_count,
            send_at=kwargs["send_at"],
            creator=admin_user_id,
            remark=kwargs.get("remark") or "",
            user_selection_type=user_selection_type,
            group_ids=group_ids,
        )
        # 如果是圈群，则从圈群中获取用户，手动模式下直接使用解析出的用户ID
        if user_selection_type == EquitySendApply.UserSelectionType.TAG_GROUPS:
            group_user_ids, _ = EquitySendApplyUserParser(apply).parse()
        apply.set_group_user_ids(group_user_ids)
        if not total_send_count:
            apply.total_send_count = len(group_user_ids)

        db.session.add(apply)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.EquitySendApply,
            detail=kwargs,
        )
        return dict(id=apply.id)

    @classmethod
    def get_or_create_equity_base_info(cls, admin_user_id: int, kwargs: dict) -> EquityBaseInfo:
        equity_type = kwargs["equity_type"]
        if equity_type in EquityType.need_equity_id_types():
            equity_id = kwargs.get("equity_id")
            if not equity_id:
                raise InvalidArgument(message=f"缺少{equity_type.value}权益ID")
        else:
            airdrop_asset = kwargs.get("airdrop_asset")
            airdrop_amount = kwargs.get("airdrop_amount")
            if not airdrop_asset or not airdrop_amount:
                raise InvalidArgument(message="缺少空投币种或空投数目")
            if airdrop_asset not in list_all_assets():
                raise InvalidArgument(f"空投币种{airdrop_asset}不存在")
            equity_id = EquityCenterService.get_or_create_airdrop_base_equity(
                airdrop_asset=airdrop_asset,
                airdrop_amount=airdrop_amount,
                creator=admin_user_id,
            )
        eq_info: EquityBaseInfo = EquityBaseInfo.query.get(equity_id)
        if not eq_info or eq_info.status == EquityBaseInfo.Status.CLOSE:
            raise InvalidArgument(message="未找到权益或者权益状态不允许使用")
        return eq_info


@ns.route("/send-apply/<int:apply_id>")  # noqa
@respond_with_code
class EquitySendApplyDetailResource(Resource):
    @classmethod
    def get_apply(cls, apply_id: int) -> EquitySendApply:
        apply: EquitySendApply = EquitySendApply.query.get(apply_id)
        if not apply:
            raise InvalidArgument(message=f"未找到权益发放{apply_id}")
        return apply

    @classmethod
    @ns.use_kwargs(
        dict(
            title=fields.String(required=True),
            business_party=EnumField(enum=BusinessParty, required=True),
            send_type=EnumField(enum=EquitySendApply.SendType, required=True),
            send_at=TimestampField(is_ms=True, required=True),
            equity_type=EnumField(enum=EquityType, required=True),
            equity_id=fields.Integer(required=False),  # 空投时不传id，传空投币种和数目
            airdrop_asset=fields.String,
            airdrop_amount=fields.Integer(required=False, validate=lambda x: x > 0),
            total_send_count=fields.Integer(required=False, validate=lambda x: x > 0),
            user_selection_type=EnumField(enum=EquitySendApply.UserSelectionType, required=False),
            manual_user_list=fields.String(required=False),  # 手动输入的用户列表
            groups=fields.List(fields.Integer),
            remark=fields.String,
            resubmit_wait_audit=fields.Boolean,
        )
    )
    def put(cls, apply_id, **kwargs):
        """权益中心-权益发放-修改发放"""
        if kwargs["send_at"] < now():
            raise InvalidArgument(message="发放时间不正确，发放时间应晚于当前时间")
        apply = cls.get_apply(apply_id)
        old_data = apply.to_dict(enum_to_name=True)
        del old_data["group_user_ids"]
        del old_data["send_user_ids"]

        if apply.status not in [EquitySendApply.Status.CREATED, EquitySendApply.Status.REJECTED]:
            raise InvalidArgument(message="当前状态不允许修改")

        if kwargs.get("resubmit_wait_audit") and apply.status == EquitySendApply.Status.REJECTED:
            # 重新提交审核
            cls.change_apply_status(apply, EquitySendApply.Status.CREATED)

        admin_user_id = g.user.id
        eq_info = EquitySendApplyListResource.get_or_create_equity_base_info(admin_user_id, kwargs)

        # 处理用户选择方式
        user_selection_type = kwargs.get("user_selection_type", EquitySendApply.UserSelectionType.TAG_GROUPS)
        manual_user_list = kwargs.pop("manual_user_list", "")
        groups = kwargs.pop("groups", "") or ""

        if user_selection_type == EquitySendApply.UserSelectionType.MANUAL:
            if not manual_user_list:
                raise InvalidArgument(message="手动选择用户时，用户列表不能为空")

            valid_user_ids, invalid_users = parse_manual_users(manual_user_list)
            if invalid_users:
                raise InvalidArgument(message=f"请检查UID/邮箱填写：{', '.join(invalid_users)}")

            if not valid_user_ids:
                raise InvalidArgument(message="未找到有效用户")

            group_ids = []  # 手动模式下不使用圈群
            group_user_ids = valid_user_ids  # 直接使用解析出的用户ID
        else:
            # 圈群选择模式
            group_ids = UserTagGroupBiz.filter_tag_group_ids(ids=groups) if groups else []
            if not group_ids:
                raise InvalidArgument(message="发放客群为空")

            # 先创建临时对象来解析圈群用户
            temp_apply = EquitySendApply(user_selection_type=user_selection_type, group_ids=group_ids)
            group_user_ids, _ = EquitySendApplyUserParser(temp_apply).parse()

        total_send_count = kwargs.get("total_send_count") or 0
        apply.send_type = kwargs["send_type"]
        apply.business_party = kwargs["business_party"]
        apply.title = kwargs["title"]
        apply.equity_id = eq_info.id
        apply.equity_type = eq_info.type
        apply.total_send_count = total_send_count
        apply.send_at = kwargs["send_at"]
        apply.remark = kwargs.get("remark") or ""
        apply.user_selection_type = user_selection_type
        apply.group_ids = group_ids

        # 设置用户ID（无论是圈群解析的还是手动输入的）
        apply.set_group_user_ids(group_user_ids)
        if not total_send_count:
            apply.total_send_count = len(group_user_ids)
        db.session.commit()

        new_data = apply.to_dict(enum_to_name=True)
        del new_data["group_user_ids"]
        del new_data["send_user_ids"]
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.EquitySendApply,
            old_data=old_data,
            new_data=apply.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            status=EnumField(enum=EquitySendApply.Status, required=True),
        )
    )
    def patch(cls, apply_id, **kwargs):
        """运营-权益发放-更改权益发放状态（拒绝、禁用）"""
        apply = cls.get_apply(apply_id)
        new_status = kwargs["status"]
        cls.change_apply_status(apply, new_status)

        op_log_data = apply.to_dict(enum_to_name=True)
        del op_log_data["group_user_ids"]
        del op_log_data["send_user_ids"]
        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.EquitySendApply,
            detail=op_log_data,
        )

    @classmethod
    def change_apply_status(cls, apply: EquitySendApply, new_status: EquitySendApply.Status):
        status_enum = EquitySendApply.Status
        allow_status_flows = {
            # (current_status, next_status)
            (status_enum.CREATED, status_enum.PASSED),  # 审核通过
            (status_enum.CREATED, status_enum.REJECTED),  # 审核拒绝
            (status_enum.REJECTED, status_enum.CREATED),  # 重新提交审核
            (status_enum.PASSED, status_enum.DISABLED),  # 禁用
        }

        old_status = apply.status
        flow = (old_status, new_status)
        if flow not in allow_status_flows:
            raise InvalidArgument(message=f"不支持 {old_status.value} -> {new_status.value} 的状态修改")
        apply.status = new_status
        db.session.commit()


@ns.route("/send-apply/<int:apply_id>/audit")
@respond_with_code
class EquitySendApplyAuditResource(Resource):
    @classmethod
    @require_admin_webauth_token
    def post(cls, apply_id):
        """运营-权益发放-审核权益发放"""
        admin_user_id = g.user.id
        apply = EquitySendApplyDetailResource.get_apply(apply_id)
        if apply.status != EquitySendApply.Status.CREATED:
            raise InvalidArgument(message="状态不是待审核")
        if apply.creator == admin_user_id:
            raise InvalidArgument(message="申请人和审核人不能是同一个人")
        if apply.send_at + timedelta(minutes=2) < now():
            # 审核时间不能晚于发放时间超过2分钟，如果晚于，就把状态更新为审核未通过
            EquitySendApplyDetailResource.change_apply_status(apply, EquitySendApply.Status.REJECTED)
            db.session.commit()
            raise InvalidArgument(message="审核已超时，请修改发放时间重新提交")

        EquitySendApplyDetailResource.change_apply_status(apply, EquitySendApply.Status.PASSED)
        db.session.commit()

        op_log_data = apply.to_dict(enum_to_name=True)
        del op_log_data["group_user_ids"]
        del op_log_data["send_user_ids"]
        AdminOperationLog.new_send(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.EquitySendApply,
            detail=op_log_data,
        )


@ns.route("/send-apply/<int:apply_id>/user-download")
@respond_with_code
class EquitySendApplyUserResource(Resource):
    @classmethod
    def get(cls, apply_id):
        """运营-权益发放-权益发放用户下载"""
        apply = EquitySendApplyDetailResource.get_apply(apply_id)
        data = cls.get_data(apply)
        fields_ = headers = ["id", "email"]
        stream = ExcelExporter(data_list=data, fields=fields_, headers=headers).export_streams()
        return send_file(stream, download_name=f"权益发放{apply_id}客群信息.xlsx", as_attachment=True)

    @classmethod
    def get_data(cls, row: EquitySendApply) -> list:
        user_ids = row.cached_group_user_ids
        items = []
        for chunk_ids in batch_iter(user_ids, 2000):
            chunk_objs = (
                User.query.with_entities(
                    User.id,
                    User.email,
                )
                .filter(
                    User.id.in_(chunk_ids),
                )
                .all()
            )
            items.extend(chunk_objs)
        return [{"id": obj.id, "email": obj.email} for obj in items]


@ns.route("/cashback/embed")
@respond_with_code
class EquityCashbackEmbedResource(Resource):
    model = EquityBaseInfo

    @classmethod
    @ns.use_kwargs(
        dict(
            equity_type=EnumField(enum=EquityType, required=True),
            limit=LimitField(missing=10),
            page=PageField(missing=1),
        )
    )
    def get(cls, **kwargs):
        """权益中心-返现权益-其他业务嵌入"""
        equity_type = kwargs["equity_type"]
        limit, page = kwargs["limit"], kwargs["page"]
        query = (
            cls.model.query.filter(cls.model.type == equity_type, cls.model.status == cls.model.Status.OPEN)
            .order_by(cls.model.id.desc())
            .paginate(page, limit, error_out=False)
        )
        rows = query.items
        creators = [i.creator for i in rows]
        creator_name_map = get_admin_user_name_map(creators)
        equity_list = []
        for row in rows:
            item = row.to_dict(enum_to_name=True)
            item["creator_name"] = creator_name_map.get(row.creator)
            equity_list.append(item)
        return dict(
            equity_list=equity_list,
            # 目前仅支持手续费返现，等后续提单
            equity_types={i.name: i.value for i in EquityType if i == EquityType.CASHBACK},
            statuses=cls.model.Status,
            extra=dict(
                cashback_scope_dict=UserCashbackEquity.CashbackScope,
            ),
            total=query.total,
        )


@ns.route("/daily-consumption")
@respond_with_code
class EquityDailyConsumptionResource(Resource):
    """发奖账户每日消耗统计"""

    @classmethod
    @ns.use_kwargs(
        dict(
            issue_asset=fields.String(),  # 发放币种
            issue_start_date=fields.Date(),  # 开始日期
            issue_end_date=fields.Date(),  # 结束日期
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """权益中心-权益监控-发奖账户每日消耗统计列表"""
        q = EquityDailyConsumption.query.filter(
            EquityDailyConsumption.issue_amount > 0
        ).order_by(EquityDailyConsumption.issue_date.desc())

        if issue_asset := kwargs.get("issue_asset"):
            q = q.filter(EquityDailyConsumption.issue_asset == issue_asset)
        if issue_start_date := kwargs.get("issue_start_date"):
            q = q.filter(EquityDailyConsumption.issue_date >= issue_start_date)
        if issue_end_date := kwargs.get("issue_end_date"):
            q = q.filter(EquityDailyConsumption.issue_date <= issue_end_date)

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        rows = pagination.items

        return dict(
            items=[i.to_dict() for i in rows],
            total=total,
            extra=dict(
                available_assets=EquitySetting.BALANCE_ASSETS,
            ),
        )


@ns.route("/daily-consumption-detail")
@respond_with_code
class EquityDailyConsumptionDetailResource(Resource):
    """发奖账户每日消耗明细"""

    @classmethod
    @ns.use_kwargs(
        dict(
            consumption_id=fields.Integer(required=True),  # 消耗记录ID
            # issue_date=fields.Date(),  # 发放日期
            issue_type=EnumField(UserEquity.BusinessType),  # 发放类型
            issue_asset=fields.String(),  # 发放币种
            business_id=fields.Integer(),  # 业务ID
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """权益中心-权益监控-发奖消耗明细列表"""
        consumption_id = kwargs["consumption_id"]
        q = EquityDailyConsumptionDetail.query.filter(
            EquityDailyConsumptionDetail.consumption_id == consumption_id,
        ).order_by(
            EquityDailyConsumptionDetail.issue_date.desc(),
            EquityDailyConsumptionDetail.id.desc()
        )

        if issue_date := kwargs.get("issue_date"):
            q = q.filter(EquityDailyConsumptionDetail.issue_date == issue_date)
        if issue_type := kwargs.get("issue_type"):
            q = q.filter(EquityDailyConsumptionDetail.issue_type == issue_type)
        if issue_asset := kwargs.get("issue_asset"):
            q = q.filter(EquityDailyConsumptionDetail.issue_asset == issue_asset)
        if business_id := kwargs.get("business_id"):
            q = q.filter(EquityDailyConsumptionDetail.business_id == business_id)

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        rows = pagination.items

        biz_type_ids_mapper = defaultdict(list)
        mission_ids = set()
        for row in rows:
            biz_type_ids_mapper[row.business_type].append(row.business_id)
            if row.business_type == UserEquity.BusinessType.MISSION:
                mission_ids.add(row.business_id)
        mission_plan_mapper = {
            mid: plan_id for mid, plan_id in
            Mission.query.filter(
                Mission.id.in_(mission_ids)
            ).with_entities(
                Mission.id, Mission.plan_id
            ).all()
        }
        mission_cache_mapper = MissionCache.get_cache_data_by_ids(list(mission_ids))
        items = []
        for row in rows:
            item = row.to_dict(enum_to_name=True)
            if row.business_type == UserEquity.BusinessType.MISSION:
                mission_cache = mission_cache_mapper[row.business_id]
                item["plan_id"] = mission_plan_mapper[row.business_id]
                item["scene_type"] = mission_cache["scene_type"]
            else:
                item["plan_id"] = None
                item["scene_type"] = None
            items.append(item)

        return dict(
            items=items,
            total=total,
            extra=dict(
                business_types=BaseUserEquityResource.AdminBusinessType,
                available_assets=EquitySetting.BALANCE_ASSETS,
            ),
        )