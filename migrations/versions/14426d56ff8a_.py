"""empty message

Revision ID: 14426d56ff8a
Revises: d1715b4c47e6
Create Date: 2025-09-15 11:38:00.888185

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import app.models

# revision identifiers, used by Alembic.
revision = '14426d56ff8a'
down_revision = 'd1715b4c47e6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('asset_profit_loss_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('updated_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('asset', sa.String(length=32), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('report_date', sa.Date(), nullable=False),
    sa.Column('profit', mysql.DECIMAL(precision=26, scale=8), nullable=False),
    sa.Column('price', mysql.DECIMAL(precision=26, scale=12), nullable=False),
    sa.Column('amount', mysql.DECIMAL(precision=26, scale=12), nullable=False),
    sa.Column('type', app.models.base.StringEnum('SYSTEM', 'USER'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('asset_profit_loss_history', schema=None) as batch_op:
        batch_op.create_index('idx_user_id_report_date_asset', ['user_id', 'report_date', 'asset'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('asset_profit_loss_history', schema=None) as batch_op:
        batch_op.drop_index('idx_user_id_report_date_asset')

    op.drop_table('asset_profit_loss_history')
    # ### end Alembic commands ###
